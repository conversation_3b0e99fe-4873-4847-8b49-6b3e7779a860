import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useState } from "react";
import Login from "./interface/components/features/Login";
import Dashboard from "./interface/components/features/Dashboard";

const TestIndex = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return isLoggedIn ? (
    <Dashboard onLogout={() => setIsLoggedIn(false)} />
  ) : (
    <Login onLogin={() => setIsLoggedIn(true)} />
  );
};

const App = () => (
  <BrowserRouter>
    <Routes>
      <Route path="/" element={<TestIndex />} />
      <Route path="*" element={<div style={{ padding: '20px', textAlign: 'center' }}>404 - Page Not Found</div>} />
    </Routes>
  </BrowserRouter>
);

export default App;
