/**
 * Central export file for all type definitions
 * Provides a single import point for all model types
 */

// User types
export type {
  User,
  UserProfile,
  UserCreditSummary,
  UserContentSummary,
  UserMonthlyStats,
  CreateUserPayload,
  UpdateUserPayload,
  UserSearchParams,
  UserListResponse,
} from './User';

// Credit types
export type {
  CreditTransaction,
  CreditBalance,
  CreditUsageStats,
  CreditOperationPayload,
  CreditSystemConfig,
  CreditPackage,
  CreditTransactionQuery,
  CreditTransactionHistoryResponse,
  CreditTransactionType,
  CreditTransactionStatus,
  CreditUsageContext,
} from './Credit';

// Analytics types
export type {
  DashboardStat,
  QuickStat,
  AnalyticsData,
  UserGrowthData,
  CreditUsageData,
  ContentCreationData,
  SystemPerformanceData,
  AnalyticsSummary,
  AnalyticsQuery,
  MetricComparison,
  TopPerformers,
  AnalyticsExportConfig,
  ChangeType,
  TimePeriod,
} from './Analytics';

// Content types
export type {
  Content,
  BaseContent,
  VideoContent,
  PhotoContent,
  CreateContentPayload,
  UpdateContentPayload,
  ContentSearchParams,
  ContentListResponse,
  ContentStats,
  ContentProcessingJob,
  BulkContentOperation,
  ContentAnalytics,
  ContentType,
  ProcessingStatus,
  QualityLevel,
  ContentVisibility,
} from './Content';

// Notification types
export type {
  Notification,
  NotificationAction,
  NotificationAttachment,
  NotificationPreferences,
  NotificationTemplate,
  NotificationCampaign,
  NotificationDeliveryLog,
  NotificationAnalytics,
  CreateNotificationPayload,
  NotificationSearchParams,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  NotificationDeliveryMethod,
  NotificationTarget,
} from './Notification';

// System types
export type {
  SystemConfig,
  SystemStatus,
  AdminUser,
  AuditLogEntry,
  FeatureFlags,
  CreditSystemConfig as SystemCreditConfig,
  ContentProcessingConfig,
  SecurityConfig,
  EmailConfig,
  StorageConfig,
  AnalyticsConfig,
  MaintenanceSettings,
  SystemLimits,
  MonitoringConfig,
} from './System';

// Common types
export type {
  ApiResponse,
  PaginationParams,
  PaginationMeta,
  PaginatedResponse,
  SortParams,
  FilterCondition,
  FilterGroup,
  SearchParams,
  DateRange,
  TimePeriod as CommonTimePeriod,
  LoadingState,
  ErrorState,
  AsyncState,
  ValidationError,
  FormState,
  FileUploadState,
  NotificationMessage,
  ThemeMode,
  LanguageCode,
  UserPreferences,
  SystemHealth,
  FilterOperator,
  NotificationType as CommonNotificationType,
} from './Common';

// Re-export commonly used type unions
export type AnyContent = Content;
export type AnyNotification = Notification;
export type AnyUser = User;
export type AnyTransaction = CreditTransaction;

// Utility types for common patterns
export type ID = string | number;
export type Timestamp = string;
export type Email = string;
export type PhoneNumber = string;
export type URL = string;

// Status types used across the application
export type Status = 'active' | 'inactive' | 'pending' | 'suspended';
export type ProcessStatus = 'idle' | 'processing' | 'completed' | 'error';
export type OperationResult<T = any> = {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
};

// Common entity base interface
export interface BaseEntity {
  id: ID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Audit trail interface
export interface AuditTrail {
  createdBy?: number;
  updatedBy?: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Soft delete interface
export interface SoftDelete {
  deletedAt?: Timestamp;
  deletedBy?: number;
  isDeleted: boolean;
}

// Complete entity with all common fields
export interface CompleteEntity extends BaseEntity, AuditTrail, SoftDelete {}

// Generic list response
export type ListResponse<T> = PaginatedResponse<T>;

// Generic create/update responses
export type CreateResponse<T> = OperationResult<T>;
export type UpdateResponse<T> = OperationResult<T>;
export type DeleteResponse = OperationResult<{ id: ID }>;

// Bulk operation types
export interface BulkOperation<T = any> {
  operation: 'create' | 'update' | 'delete';
  items: T[];
  options?: Record<string, any>;
}

export interface BulkOperationResult<T = any> {
  success: boolean;
  results: {
    success: T[];
    failed: {
      item: T;
      error: string;
    }[];
  };
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// Export validation schemas (will be implemented in interface layer)
export interface ValidationSchema<T = any> {
  schema: any; // Zod schema
  validate: (data: any) => { success: boolean; data?: T; errors?: ValidationError[] };
}

// Type guards for runtime type checking
export const isUser = (obj: any): obj is User => {
  return obj && typeof obj.id === 'number' && typeof obj.name === 'string' && typeof obj.email === 'string';
};

export const isContent = (obj: any): obj is Content => {
  return obj && typeof obj.id === 'string' && typeof obj.userId === 'number' && ['video', 'photo'].includes(obj.type);
};

export const isCreditTransaction = (obj: any): obj is CreditTransaction => {
  return obj && typeof obj.id === 'string' && typeof obj.userId === 'number' && typeof obj.amount === 'number';
};

export const isNotification = (obj: any): obj is Notification => {
  return obj && typeof obj.id === 'string' && typeof obj.title === 'string' && typeof obj.message === 'string';
};

// Helper types for API responses
export type UserResponse = ApiResponse<User>;
export type UserListApiResponse = ApiResponse<UserListResponse>;
export type ContentResponse = ApiResponse<Content>;
export type ContentListApiResponse = ApiResponse<ContentListResponse>;
export type AnalyticsResponse = ApiResponse<AnalyticsSummary>;
export type SystemStatusResponse = ApiResponse<SystemStatus>;
