/**
 * Content management type definitions
 * Handles video and photo content with metadata, processing status, and creation tracking
 */

/**
 * Content type enumeration
 */
export type ContentType = 'video' | 'photo';

/**
 * Content processing status
 */
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'error' | 'cancelled';

/**
 * Content quality levels
 */
export type QualityLevel = 'low' | 'medium' | 'high' | 'ultra';

/**
 * Content visibility settings
 */
export type ContentVisibility = 'private' | 'public' | 'unlisted';

/**
 * Base content interface
 */
export interface BaseContent {
  /** Unique content identifier */
  id: string;
  /** User ID who created the content */
  userId: number;
  /** Type of content */
  type: ContentType;
  /** Content title */
  title: string;
  /** Content description */
  description?: string;
  /** Processing status */
  status: ProcessingStatus;
  /** Quality level */
  quality: QualityLevel;
  /** Visibility setting */
  visibility: ContentVisibility;
  /** Credits cost for this content */
  creditsCost: number;
  /** Creation timestamp */
  createdAt: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Processing started timestamp */
  processingStartedAt?: string;
  /** Processing completed timestamp */
  processingCompletedAt?: string;
  /** Error message if processing failed */
  errorMessage?: string;
  /** File size in bytes */
  fileSize?: number;
  /** Original file name */
  originalFileName?: string;
  /** Generated file URL */
  fileUrl?: string;
  /** Thumbnail URL */
  thumbnailUrl?: string;
  /** Tags for categorization */
  tags: string[];
  /** Metadata specific to content type */
  metadata: Record<string, any>;
}

/**
 * Video-specific content interface
 */
export interface VideoContent extends BaseContent {
  type: 'video';
  /** Video duration in seconds */
  duration?: number;
  /** Video resolution width */
  width?: number;
  /** Video resolution height */
  height?: number;
  /** Video frame rate */
  frameRate?: number;
  /** Video codec used */
  codec?: string;
  /** Video bitrate */
  bitrate?: number;
  /** Audio codec */
  audioCodec?: string;
  /** Audio bitrate */
  audioBitrate?: number;
}

/**
 * Photo-specific content interface
 */
export interface PhotoContent extends BaseContent {
  type: 'photo';
  /** Image width in pixels */
  width?: number;
  /** Image height in pixels */
  height?: number;
  /** Image format (jpg, png, webp, etc.) */
  format?: string;
  /** Color space */
  colorSpace?: string;
  /** DPI/resolution */
  dpi?: number;
  /** Whether image has transparency */
  hasTransparency?: boolean;
}

/**
 * Union type for all content
 */
export type Content = VideoContent | PhotoContent;

/**
 * Content creation request payload
 */
export interface CreateContentPayload {
  /** User ID creating the content */
  userId: number;
  /** Type of content to create */
  type: ContentType;
  /** Content title */
  title: string;
  /** Content description */
  description?: string;
  /** Desired quality level */
  quality: QualityLevel;
  /** Visibility setting */
  visibility: ContentVisibility;
  /** Tags for categorization */
  tags?: string[];
  /** Type-specific parameters */
  parameters: Record<string, any>;
  /** Source file or data */
  sourceData?: any;
}

/**
 * Content update payload
 */
export interface UpdateContentPayload {
  /** New title */
  title?: string;
  /** New description */
  description?: string;
  /** New visibility setting */
  visibility?: ContentVisibility;
  /** New tags */
  tags?: string[];
  /** Additional metadata */
  metadata?: Record<string, any>;
}

/**
 * Content search and filter parameters
 */
export interface ContentSearchParams {
  /** Search term for title/description */
  searchTerm?: string;
  /** Filter by user ID */
  userId?: number;
  /** Filter by content type */
  type?: ContentType;
  /** Filter by processing status */
  status?: ProcessingStatus;
  /** Filter by quality level */
  quality?: QualityLevel;
  /** Filter by visibility */
  visibility?: ContentVisibility;
  /** Filter by tags */
  tags?: string[];
  /** Start date for creation filter */
  startDate?: string;
  /** End date for creation filter */
  endDate?: string;
  /** Sort by field */
  sortBy?: 'createdAt' | 'updatedAt' | 'title' | 'status' | 'creditsCost';
  /** Sort order */
  sortOrder?: 'asc' | 'desc';
  /** Page number for pagination */
  page?: number;
  /** Items per page */
  limit?: number;
}

/**
 * Content list response with pagination
 */
export interface ContentListResponse {
  content: Content[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Content statistics
 */
export interface ContentStats {
  /** Total content count */
  total: number;
  /** Count by type */
  byType: {
    video: number;
    photo: number;
  };
  /** Count by status */
  byStatus: {
    pending: number;
    processing: number;
    completed: number;
    error: number;
    cancelled: number;
  };
  /** Count by quality */
  byQuality: {
    low: number;
    medium: number;
    high: number;
    ultra: number;
  };
  /** Total credits spent */
  totalCreditsSpent: number;
  /** Average processing time in seconds */
  avgProcessingTime: number;
  /** Success rate percentage */
  successRate: number;
}

/**
 * Content processing job
 */
export interface ContentProcessingJob {
  /** Job identifier */
  id: string;
  /** Content ID being processed */
  contentId: string;
  /** User ID */
  userId: number;
  /** Job status */
  status: ProcessingStatus;
  /** Progress percentage (0-100) */
  progress: number;
  /** Started timestamp */
  startedAt: string;
  /** Estimated completion time */
  estimatedCompletion?: string;
  /** Error details if failed */
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  /** Processing steps */
  steps: {
    name: string;
    status: 'pending' | 'running' | 'completed' | 'error';
    startedAt?: string;
    completedAt?: string;
    error?: string;
  }[];
}

/**
 * Bulk content operations
 */
export interface BulkContentOperation {
  /** Operation type */
  operation: 'delete' | 'update_visibility' | 'update_tags' | 'reprocess';
  /** Content IDs to operate on */
  contentIds: string[];
  /** Parameters for the operation */
  parameters?: Record<string, any>;
}

/**
 * Content analytics data
 */
export interface ContentAnalytics {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Content created on this date */
  created: number;
  /** Content completed on this date */
  completed: number;
  /** Content failed on this date */
  failed: number;
  /** Credits spent on this date */
  creditsSpent: number;
  /** Average processing time */
  avgProcessingTime: number;
}
