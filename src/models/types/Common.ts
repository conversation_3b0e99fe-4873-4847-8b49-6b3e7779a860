/**
 * Common type definitions shared across the application
 * Includes API responses, pagination, filtering, and shared utilities
 */

/**
 * Generic API response wrapper
 */
export interface ApiResponse<T = any> {
  /** Response data */
  data: T;
  /** Success status */
  success: boolean;
  /** Response message */
  message?: string;
  /** Error details if any */
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  /** Response metadata */
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  /** Page number (1-based) */
  page: number;
  /** Items per page */
  limit: number;
  /** Total items count */
  total?: number;
  /** Total pages count */
  totalPages?: number;
}

/**
 * Pagination metadata
 */
export interface PaginationMeta {
  /** Current page number */
  page: number;
  /** Items per page */
  limit: number;
  /** Total items */
  total: number;
  /** Total pages */
  totalPages: number;
  /** Has previous page */
  hasPrevious: boolean;
  /** Has next page */
  hasNext: boolean;
  /** Previous page number */
  previousPage?: number;
  /** Next page number */
  nextPage?: number;
}

/**
 * Paginated response wrapper
 */
export interface PaginatedResponse<T> {
  /** Array of items */
  items: T[];
  /** Pagination metadata */
  pagination: PaginationMeta;
}

/**
 * Sort parameters
 */
export interface SortParams {
  /** Field to sort by */
  field: string;
  /** Sort direction */
  direction: 'asc' | 'desc';
}

/**
 * Filter operator types
 */
export type FilterOperator = 
  | 'eq'      // equals
  | 'ne'      // not equals
  | 'gt'      // greater than
  | 'gte'     // greater than or equal
  | 'lt'      // less than
  | 'lte'     // less than or equal
  | 'in'      // in array
  | 'nin'     // not in array
  | 'like'    // contains (string)
  | 'ilike'   // contains case insensitive
  | 'between' // between two values
  | 'exists'  // field exists
  | 'null'    // field is null
  | 'nnull';  // field is not null

/**
 * Filter condition
 */
export interface FilterCondition {
  /** Field name to filter */
  field: string;
  /** Filter operator */
  operator: FilterOperator;
  /** Filter value(s) */
  value: any;
}

/**
 * Filter group with logical operators
 */
export interface FilterGroup {
  /** Logical operator for conditions */
  operator: 'and' | 'or';
  /** Filter conditions */
  conditions: FilterCondition[];
  /** Nested filter groups */
  groups?: FilterGroup[];
}

/**
 * Search parameters
 */
export interface SearchParams {
  /** Search query string */
  query?: string;
  /** Fields to search in */
  fields?: string[];
  /** Filters to apply */
  filters?: FilterGroup;
  /** Sort parameters */
  sort?: SortParams[];
  /** Pagination */
  pagination?: PaginationParams;
}

/**
 * Date range filter
 */
export interface DateRange {
  /** Start date (ISO string) */
  start: string;
  /** End date (ISO string) */
  end: string;
}

/**
 * Time period options
 */
export type TimePeriod = 
  | 'today'
  | 'yesterday'
  | 'last_7_days'
  | 'last_30_days'
  | 'last_90_days'
  | 'this_month'
  | 'last_month'
  | 'this_quarter'
  | 'last_quarter'
  | 'this_year'
  | 'last_year'
  | 'custom';

/**
 * Loading state
 */
export interface LoadingState {
  /** Is currently loading */
  isLoading: boolean;
  /** Loading message */
  message?: string;
  /** Progress percentage (0-100) */
  progress?: number;
}

/**
 * Error state
 */
export interface ErrorState {
  /** Has error */
  hasError: boolean;
  /** Error message */
  message?: string;
  /** Error code */
  code?: string;
  /** Error details */
  details?: any;
  /** Timestamp when error occurred */
  timestamp?: string;
}

/**
 * Async operation state
 */
export interface AsyncState<T = any> {
  /** Data */
  data?: T;
  /** Loading state */
  loading: LoadingState;
  /** Error state */
  error: ErrorState;
  /** Last updated timestamp */
  lastUpdated?: string;
}

/**
 * Form validation error
 */
export interface ValidationError {
  /** Field name */
  field: string;
  /** Error message */
  message: string;
  /** Error code */
  code?: string;
}

/**
 * Form state
 */
export interface FormState<T = any> {
  /** Form data */
  data: T;
  /** Is form dirty (has changes) */
  isDirty: boolean;
  /** Is form valid */
  isValid: boolean;
  /** Is form submitting */
  isSubmitting: boolean;
  /** Validation errors */
  errors: ValidationError[];
  /** Touched fields */
  touched: string[];
}

/**
 * File upload state
 */
export interface FileUploadState {
  /** File being uploaded */
  file?: File;
  /** Upload progress (0-100) */
  progress: number;
  /** Upload status */
  status: 'idle' | 'uploading' | 'success' | 'error';
  /** Error message if upload failed */
  error?: string;
  /** Uploaded file URL */
  url?: string;
}

/**
 * Notification types
 */
export type NotificationType = 'info' | 'success' | 'warning' | 'error';

/**
 * Notification message
 */
export interface NotificationMessage {
  /** Unique notification ID */
  id: string;
  /** Notification type */
  type: NotificationType;
  /** Title */
  title: string;
  /** Message content */
  message: string;
  /** Auto dismiss timeout in ms */
  timeout?: number;
  /** Action buttons */
  actions?: {
    label: string;
    action: () => void;
  }[];
  /** Timestamp */
  timestamp: string;
}

/**
 * Theme mode
 */
export type ThemeMode = 'light' | 'dark' | 'system';

/**
 * Language code
 */
export type LanguageCode = 'en' | 'vi' | 'zh' | 'ja' | 'ko';

/**
 * User preferences
 */
export interface UserPreferences {
  /** Theme mode */
  theme: ThemeMode;
  /** Language */
  language: LanguageCode;
  /** Timezone */
  timezone: string;
  /** Date format */
  dateFormat: string;
  /** Number format */
  numberFormat: string;
  /** Notifications enabled */
  notificationsEnabled: boolean;
  /** Email notifications enabled */
  emailNotifications: boolean;
  /** Dashboard layout preferences */
  dashboardLayout: Record<string, any>;
}

/**
 * System health status
 */
export interface SystemHealth {
  /** Overall status */
  status: 'healthy' | 'degraded' | 'down';
  /** Individual service statuses */
  services: {
    name: string;
    status: 'up' | 'down' | 'degraded';
    responseTime?: number;
    lastCheck: string;
  }[];
  /** System metrics */
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  /** Last updated */
  lastUpdated: string;
}
