/**
 * System settings and configuration type definitions
 * Handles admin settings, feature flags, and system configurations
 */

/**
 * System feature flags
 */
export interface FeatureFlags {
  /** User registration enabled */
  userRegistrationEnabled: boolean;
  /** Email verification required */
  emailVerificationRequired: boolean;
  /** Credit system enabled */
  creditSystemEnabled: boolean;
  /** Video creation enabled */
  videoCreationEnabled: boolean;
  /** Photo creation enabled */
  photoCreationEnabled: boolean;
  /** Premium features enabled */
  premiumFeaturesEnabled: boolean;
  /** Analytics tracking enabled */
  analyticsEnabled: boolean;
  /** Maintenance mode */
  maintenanceMode: boolean;
  /** API rate limiting enabled */
  rateLimitingEnabled: boolean;
  /** File upload enabled */
  fileUploadEnabled: boolean;
  /** Notifications enabled */
  notificationsEnabled: boolean;
  /** Debug mode */
  debugMode: boolean;
}

/**
 * Credit system configuration
 */
export interface CreditSystemConfig {
  /** Default credits for new users */
  defaultCredits: number;
  /** Maximum credits per user */
  maxCreditsPerUser: number;
  /** Credits cost per video */
  videoCreditCost: number;
  /** Credits cost per photo */
  photoCreditCost: number;
  /** Premium user credit bonus */
  premiumCreditBonus: number;
  /** Credit expiration days (0 = no expiration) */
  creditExpirationDays: number;
  /** Minimum credit balance warning threshold */
  lowCreditThreshold: number;
  /** Auto-refill enabled */
  autoRefillEnabled: boolean;
  /** Auto-refill amount */
  autoRefillAmount: number;
  /** Auto-refill threshold */
  autoRefillThreshold: number;
}

/**
 * Content processing configuration
 */
export interface ContentProcessingConfig {
  /** Maximum file size in bytes */
  maxFileSize: number;
  /** Allowed file formats */
  allowedFormats: string[];
  /** Processing timeout in seconds */
  processingTimeout: number;
  /** Maximum concurrent processing jobs */
  maxConcurrentJobs: number;
  /** Quality levels available */
  qualityLevels: {
    name: string;
    label: string;
    creditMultiplier: number;
    maxResolution: string;
  }[];
  /** Auto-cleanup failed jobs after days */
  cleanupFailedJobsAfterDays: number;
  /** Retry failed jobs */
  retryFailedJobs: boolean;
  /** Maximum retry attempts */
  maxRetryAttempts: number;
}

/**
 * Security configuration
 */
export interface SecurityConfig {
  /** Password minimum length */
  passwordMinLength: number;
  /** Password requires uppercase */
  passwordRequireUppercase: boolean;
  /** Password requires lowercase */
  passwordRequireLowercase: boolean;
  /** Password requires numbers */
  passwordRequireNumbers: boolean;
  /** Password requires special characters */
  passwordRequireSpecialChars: boolean;
  /** Session timeout in minutes */
  sessionTimeoutMinutes: number;
  /** Maximum login attempts */
  maxLoginAttempts: number;
  /** Account lockout duration in minutes */
  lockoutDurationMinutes: number;
  /** Two-factor authentication required */
  twoFactorRequired: boolean;
  /** IP whitelist enabled */
  ipWhitelistEnabled: boolean;
  /** Allowed IP addresses */
  allowedIpAddresses: string[];
  /** API rate limit per minute */
  apiRateLimit: number;
  /** CORS allowed origins */
  corsAllowedOrigins: string[];
}

/**
 * Email configuration
 */
export interface EmailConfig {
  /** SMTP server host */
  smtpHost: string;
  /** SMTP server port */
  smtpPort: number;
  /** SMTP username */
  smtpUsername: string;
  /** SMTP password (encrypted) */
  smtpPassword: string;
  /** Use TLS encryption */
  useTls: boolean;
  /** From email address */
  fromEmail: string;
  /** From name */
  fromName: string;
  /** Reply-to email */
  replyToEmail?: string;
  /** Email templates */
  templates: {
    welcome: string;
    passwordReset: string;
    creditLow: string;
    contentReady: string;
    systemMaintenance: string;
  };
}

/**
 * Storage configuration
 */
export interface StorageConfig {
  /** Storage provider */
  provider: 'local' | 'aws_s3' | 'google_cloud' | 'azure_blob';
  /** Storage bucket/container name */
  bucketName: string;
  /** Storage region */
  region?: string;
  /** Access key ID */
  accessKeyId?: string;
  /** Secret access key (encrypted) */
  secretAccessKey?: string;
  /** CDN URL */
  cdnUrl?: string;
  /** File retention days */
  fileRetentionDays: number;
  /** Auto-cleanup enabled */
  autoCleanupEnabled: boolean;
  /** Backup enabled */
  backupEnabled: boolean;
  /** Backup frequency */
  backupFrequency: 'daily' | 'weekly' | 'monthly';
}

/**
 * Analytics configuration
 */
export interface AnalyticsConfig {
  /** Google Analytics tracking ID */
  googleAnalyticsId?: string;
  /** Data retention days */
  dataRetentionDays: number;
  /** Track user events */
  trackUserEvents: boolean;
  /** Track system events */
  trackSystemEvents: boolean;
  /** Export analytics data */
  enableDataExport: boolean;
  /** Real-time analytics */
  realTimeAnalytics: boolean;
  /** Custom event tracking */
  customEventTracking: boolean;
}

/**
 * System maintenance settings
 */
export interface MaintenanceSettings {
  /** Maintenance mode enabled */
  enabled: boolean;
  /** Maintenance start time */
  startTime?: string;
  /** Maintenance end time */
  endTime?: string;
  /** Maintenance message */
  message: string;
  /** Allow admin access during maintenance */
  allowAdminAccess: boolean;
  /** Affected services */
  affectedServices: string[];
  /** Notification sent */
  notificationSent: boolean;
}

/**
 * System limits and quotas
 */
export interface SystemLimits {
  /** Maximum users */
  maxUsers: number;
  /** Maximum content per user */
  maxContentPerUser: number;
  /** Maximum file uploads per day */
  maxUploadsPerDay: number;
  /** Maximum API calls per user per day */
  maxApiCallsPerDay: number;
  /** Maximum storage per user (bytes) */
  maxStoragePerUser: number;
  /** Maximum concurrent sessions per user */
  maxSessionsPerUser: number;
}

/**
 * System monitoring configuration
 */
export interface MonitoringConfig {
  /** Health check enabled */
  healthCheckEnabled: boolean;
  /** Health check interval (seconds) */
  healthCheckInterval: number;
  /** Performance monitoring enabled */
  performanceMonitoringEnabled: boolean;
  /** Error tracking enabled */
  errorTrackingEnabled: boolean;
  /** Log level */
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  /** Log retention days */
  logRetentionDays: number;
  /** Metrics collection enabled */
  metricsEnabled: boolean;
  /** Alert thresholds */
  alertThresholds: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    errorRate: number;
    responseTime: number;
  };
}

/**
 * Complete system configuration
 */
export interface SystemConfig {
  /** Feature flags */
  features: FeatureFlags;
  /** Credit system settings */
  credits: CreditSystemConfig;
  /** Content processing settings */
  contentProcessing: ContentProcessingConfig;
  /** Security settings */
  security: SecurityConfig;
  /** Email settings */
  email: EmailConfig;
  /** Storage settings */
  storage: StorageConfig;
  /** Analytics settings */
  analytics: AnalyticsConfig;
  /** Maintenance settings */
  maintenance: MaintenanceSettings;
  /** System limits */
  limits: SystemLimits;
  /** Monitoring settings */
  monitoring: MonitoringConfig;
  /** Last updated timestamp */
  lastUpdated: string;
  /** Updated by admin ID */
  updatedBy: number;
}

/**
 * System status information
 */
export interface SystemStatus {
  /** Overall system status */
  status: 'healthy' | 'degraded' | 'down';
  /** System version */
  version: string;
  /** Uptime in seconds */
  uptime: number;
  /** Service statuses */
  services: {
    name: string;
    status: 'up' | 'down' | 'degraded';
    responseTime?: number;
    lastCheck: string;
    errorMessage?: string;
  }[];
  /** System metrics */
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    activeUsers: number;
    totalRequests: number;
    errorRate: number;
  };
  /** Database status */
  database: {
    status: 'connected' | 'disconnected' | 'error';
    responseTime?: number;
    connections: number;
    lastBackup?: string;
  };
  /** Last updated */
  lastUpdated: string;
}

/**
 * Admin user information
 */
export interface AdminUser {
  /** Admin ID */
  id: number;
  /** Username */
  username: string;
  /** Email */
  email: string;
  /** Full name */
  fullName: string;
  /** Role */
  role: 'super_admin' | 'admin' | 'moderator';
  /** Permissions */
  permissions: string[];
  /** Last login */
  lastLogin?: string;
  /** Account status */
  status: 'active' | 'inactive' | 'suspended';
  /** Created timestamp */
  createdAt: string;
  /** Two-factor enabled */
  twoFactorEnabled: boolean;
}

/**
 * System audit log entry
 */
export interface AuditLogEntry {
  /** Log entry ID */
  id: string;
  /** Admin user who performed action */
  adminId: number;
  /** Action performed */
  action: string;
  /** Resource affected */
  resource: string;
  /** Resource ID */
  resourceId?: string;
  /** Changes made */
  changes?: Record<string, any>;
  /** IP address */
  ipAddress: string;
  /** User agent */
  userAgent: string;
  /** Timestamp */
  timestamp: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
}
