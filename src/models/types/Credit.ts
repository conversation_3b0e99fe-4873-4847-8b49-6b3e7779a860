/**
 * Credit system type definitions
 * Handles credit transactions, balance tracking, and usage history
 */

/**
 * Credit transaction types
 */
export type CreditTransactionType = 'add' | 'subtract' | 'used' | 'refund' | 'bonus';

/**
 * Credit transaction status
 */
export type CreditTransactionStatus = 'pending' | 'completed' | 'failed' | 'cancelled';

/**
 * Credit usage context - what the credits were used for
 */
export type CreditUsageContext = 'video_creation' | 'photo_creation' | 'premium_feature' | 'api_call' | 'other';

/**
 * Individual credit transaction record
 */
export interface CreditTransaction {
  /** Unique transaction identifier */
  id: string;
  /** User ID who performed the transaction */
  userId: number;
  /** Type of transaction */
  type: CreditTransactionType;
  /** Amount of credits (positive for add, negative for subtract/used) */
  amount: number;
  /** Current balance after this transaction */
  balanceAfter: number;
  /** Transaction status */
  status: CreditTransactionStatus;
  /** What the credits were used for (if applicable) */
  context?: CreditUsageContext;
  /** Additional context or description */
  description?: string;
  /** Transaction timestamp */
  createdAt: string;
  /** Admin user who performed the transaction (if applicable) */
  adminId?: number;
  /** Reference to related content (video/photo ID) if applicable */
  contentId?: string;
}

/**
 * Credit balance information for a user
 */
export interface CreditBalance {
  /** User ID */
  userId: number;
  /** Current available credits */
  current: number;
  /** Total credits ever added to account */
  totalAdded: number;
  /** Total credits ever used */
  totalUsed: number;
  /** Credits used this month */
  monthlyUsed: number;
  /** Credits added this month */
  monthlyAdded: number;
  /** Last transaction timestamp */
  lastUpdated: string;
}

/**
 * Credit usage statistics
 */
export interface CreditUsageStats {
  /** Total credits used for video creation */
  videoCreation: number;
  /** Total credits used for photo creation */
  photoCreation: number;
  /** Total credits used for premium features */
  premiumFeatures: number;
  /** Total credits used for API calls */
  apiCalls: number;
  /** Other credit usage */
  other: number;
  /** Usage breakdown by month */
  monthlyBreakdown: {
    month: string; // YYYY-MM format
    videoCreation: number;
    photoCreation: number;
    premiumFeatures: number;
    apiCalls: number;
    other: number;
    total: number;
  }[];
}

/**
 * Credit operation payload for adding/subtracting credits
 */
export interface CreditOperationPayload {
  /** User ID to modify credits for */
  userId: number;
  /** Amount to add (positive) or subtract (negative) */
  amount: number;
  /** Type of operation */
  type: CreditTransactionType;
  /** Context for the operation */
  context?: CreditUsageContext;
  /** Description or reason for the operation */
  description?: string;
  /** Admin performing the operation */
  adminId?: number;
}

/**
 * Credit system configuration
 */
export interface CreditSystemConfig {
  /** Default credits for new users */
  defaultCredits: number;
  /** Credits cost per video creation */
  videoCost: number;
  /** Credits cost per photo creation */
  photoCost: number;
  /** Minimum credits required to perform operations */
  minimumBalance: number;
  /** Maximum credits a user can have */
  maximumBalance: number;
  /** Bonus credits for premium users */
  premiumBonus: number;
}

/**
 * Credit package for purchasing
 */
export interface CreditPackage {
  /** Package identifier */
  id: string;
  /** Package name */
  name: string;
  /** Number of credits in package */
  credits: number;
  /** Price in currency */
  price: number;
  /** Currency code */
  currency: string;
  /** Package description */
  description?: string;
  /** Whether package is active */
  active: boolean;
  /** Bonus credits included */
  bonusCredits?: number;
}

/**
 * Credit transaction history query parameters
 */
export interface CreditTransactionQuery {
  /** User ID to filter by */
  userId?: number;
  /** Transaction type filter */
  type?: CreditTransactionType;
  /** Transaction status filter */
  status?: CreditTransactionStatus;
  /** Usage context filter */
  context?: CreditUsageContext;
  /** Start date for filtering */
  startDate?: string;
  /** End date for filtering */
  endDate?: string;
  /** Page number for pagination */
  page?: number;
  /** Items per page */
  limit?: number;
  /** Sort by field */
  sortBy?: 'createdAt' | 'amount' | 'balanceAfter';
  /** Sort order */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Credit transaction history response
 */
export interface CreditTransactionHistoryResponse {
  transactions: CreditTransaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
