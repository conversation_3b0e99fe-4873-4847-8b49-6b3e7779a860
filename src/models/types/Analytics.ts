/**
 * Analytics and dashboard statistics type definitions
 * Based on statsData and quickStats from Dashboard component
 */

import { LucideIcon } from 'lucide-react';

/**
 * Change type for statistics (positive, negative, neutral)
 */
export type ChangeType = 'positive' | 'negative' | 'neutral';

/**
 * Time period for analytics
 */
export type TimePeriod = 'today' | 'week' | 'month' | 'quarter' | 'year' | 'all';

/**
 * Dashboard statistic card data structure
 * Matches exactly with statsData from Dashboard.tsx
 */
export interface DashboardStat {
  /** Display title for the statistic */
  title: string;
  /** Current value as string (formatted) */
  value: string;
  /** Change percentage with sign */
  change: string;
  /** Type of change (positive/negative/neutral) */
  changeType: ChangeType;
  /** Lucide icon component */
  icon: LucideIcon;
  /** Tailwind gradient class for styling */
  gradient: string;
  /** Additional subtitle information */
  subtitle?: string;
  /** Trend data points for mini chart */
  trend?: number[];
}

/**
 * Quick stats data structure
 * Matches quickStats from Dashboard.tsx
 */
export interface QuickStat {
  /** Label for the statistic */
  label: string;
  /** Value as string (formatted) */
  value: string;
  /** Tailwind color class */
  color: string;
}

/**
 * Raw analytics data for calculations
 */
export interface AnalyticsData {
  /** Total number of individual users */
  totalUsers: number;
  /** Number of active users */
  activeUsers: number;
  /** Number of premium users */
  premiumUsers: number;
  /** Number of inactive users */
  inactiveUsers: number;
  /** Total credits used across all users */
  totalCreditsUsed: number;
  /** Total credits added to system */
  totalCreditsAdded: number;
  /** Remaining credits in system */
  remainingCredits: number;
  /** Total videos created */
  totalVideos: number;
  /** Total photos created */
  totalPhotos: number;
  /** New users today */
  newUsersToday: number;
  /** Credits used today */
  creditsUsedToday: number;
  /** Videos created today */
  videosToday: number;
  /** Photos created today */
  photosToday: number;
}

/**
 * User growth analytics
 */
export interface UserGrowthData {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Number of new users on this date */
  newUsers: number;
  /** Total active users on this date */
  activeUsers: number;
  /** Total users up to this date */
  totalUsers: number;
}

/**
 * Credit usage analytics
 */
export interface CreditUsageData {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Credits used on this date */
  creditsUsed: number;
  /** Credits added on this date */
  creditsAdded: number;
  /** Net credit change (added - used) */
  netChange: number;
}

/**
 * Content creation analytics
 */
export interface ContentCreationData {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Videos created on this date */
  videos: number;
  /** Photos created on this date */
  photos: number;
  /** Total content pieces created */
  total: number;
  /** Success rate for videos (0-100) */
  videoSuccessRate: number;
  /** Success rate for photos (0-100) */
  photoSuccessRate: number;
}

/**
 * System performance metrics
 */
export interface SystemPerformanceData {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Average response time in milliseconds */
  avgResponseTime: number;
  /** Error rate percentage (0-100) */
  errorRate: number;
  /** System uptime percentage (0-100) */
  uptime: number;
  /** Number of API calls */
  apiCalls: number;
}

/**
 * Analytics dashboard summary
 */
export interface AnalyticsSummary {
  /** Main dashboard statistics */
  dashboardStats: DashboardStat[];
  /** Quick statistics */
  quickStats: QuickStat[];
  /** User growth trend data */
  userGrowth: UserGrowthData[];
  /** Credit usage trend data */
  creditUsage: CreditUsageData[];
  /** Content creation trend data */
  contentCreation: ContentCreationData[];
  /** System performance data */
  systemPerformance: SystemPerformanceData[];
  /** Last updated timestamp */
  lastUpdated: string;
}

/**
 * Analytics query parameters
 */
export interface AnalyticsQuery {
  /** Time period for analytics */
  period: TimePeriod;
  /** Start date for custom period */
  startDate?: string;
  /** End date for custom period */
  endDate?: string;
  /** Specific metrics to include */
  metrics?: string[];
  /** Group by time interval */
  groupBy?: 'hour' | 'day' | 'week' | 'month';
}

/**
 * Metric comparison data
 */
export interface MetricComparison {
  /** Metric name */
  metric: string;
  /** Current period value */
  current: number;
  /** Previous period value */
  previous: number;
  /** Change amount */
  change: number;
  /** Change percentage */
  changePercent: number;
  /** Change type */
  changeType: ChangeType;
}

/**
 * Top performers data
 */
export interface TopPerformers {
  /** Top users by credit usage */
  topCreditUsers: {
    userId: number;
    name: string;
    creditsUsed: number;
  }[];
  /** Top users by content creation */
  topContentCreators: {
    userId: number;
    name: string;
    contentCount: number;
  }[];
  /** Most active users */
  mostActiveUsers: {
    userId: number;
    name: string;
    activityScore: number;
  }[];
}

/**
 * Analytics export configuration
 */
export interface AnalyticsExportConfig {
  /** Export format */
  format: 'csv' | 'xlsx' | 'pdf' | 'json';
  /** Metrics to include */
  metrics: string[];
  /** Date range */
  dateRange: {
    start: string;
    end: string;
  };
  /** Include charts */
  includeCharts?: boolean;
  /** File name */
  fileName?: string;
}
