/**
 * Notification system type definitions
 * Handles system notifications, alerts, and messaging
 */

/**
 * Notification types
 */
export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'announcement';

/**
 * Notification priority levels
 */
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

/**
 * Notification delivery status
 */
export type NotificationStatus = 'pending' | 'sent' | 'delivered' | 'read' | 'failed';

/**
 * Notification delivery methods
 */
export type NotificationDeliveryMethod = 'in_app' | 'email' | 'sms' | 'push' | 'webhook';

/**
 * Notification target types
 */
export type NotificationTarget = 'all_users' | 'specific_users' | 'user_groups' | 'admins';

/**
 * Base notification interface
 */
export interface Notification {
  /** Unique notification identifier */
  id: string;
  /** Notification title */
  title: string;
  /** Notification message content */
  message: string;
  /** Notification type */
  type: NotificationType;
  /** Priority level */
  priority: NotificationPriority;
  /** Delivery status */
  status: NotificationStatus;
  /** Target audience */
  target: NotificationTarget;
  /** Specific user IDs (if target is specific_users) */
  targetUserIds?: number[];
  /** User groups (if target is user_groups) */
  targetGroups?: string[];
  /** Delivery methods */
  deliveryMethods: NotificationDeliveryMethod[];
  /** Scheduled send time (if not immediate) */
  scheduledAt?: string;
  /** Actually sent time */
  sentAt?: string;
  /** Expiration time */
  expiresAt?: string;
  /** Created by admin ID */
  createdBy: number;
  /** Creation timestamp */
  createdAt: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Additional metadata */
  metadata?: Record<string, any>;
  /** Action buttons */
  actions?: NotificationAction[];
  /** Rich content (HTML, markdown, etc.) */
  richContent?: string;
  /** Attachments */
  attachments?: NotificationAttachment[];
}

/**
 * Notification action button
 */
export interface NotificationAction {
  /** Action identifier */
  id: string;
  /** Button label */
  label: string;
  /** Action type */
  type: 'link' | 'callback' | 'dismiss';
  /** URL for link actions */
  url?: string;
  /** Callback data for callback actions */
  callbackData?: any;
  /** Button style */
  style?: 'primary' | 'secondary' | 'danger';
}

/**
 * Notification attachment
 */
export interface NotificationAttachment {
  /** Attachment identifier */
  id: string;
  /** File name */
  fileName: string;
  /** File URL */
  fileUrl: string;
  /** File size in bytes */
  fileSize: number;
  /** MIME type */
  mimeType: string;
}

/**
 * User notification preferences
 */
export interface NotificationPreferences {
  /** User ID */
  userId: number;
  /** In-app notifications enabled */
  inAppEnabled: boolean;
  /** Email notifications enabled */
  emailEnabled: boolean;
  /** SMS notifications enabled */
  smsEnabled: boolean;
  /** Push notifications enabled */
  pushEnabled: boolean;
  /** Notification types to receive */
  enabledTypes: NotificationType[];
  /** Quiet hours (no notifications) */
  quietHours?: {
    start: string; // HH:mm format
    end: string;   // HH:mm format
    timezone: string;
  };
  /** Frequency settings */
  frequency: {
    immediate: boolean;
    daily: boolean;
    weekly: boolean;
  };
}

/**
 * Notification template
 */
export interface NotificationTemplate {
  /** Template identifier */
  id: string;
  /** Template name */
  name: string;
  /** Template description */
  description?: string;
  /** Template type */
  type: NotificationType;
  /** Title template with placeholders */
  titleTemplate: string;
  /** Message template with placeholders */
  messageTemplate: string;
  /** Rich content template */
  richContentTemplate?: string;
  /** Default delivery methods */
  defaultDeliveryMethods: NotificationDeliveryMethod[];
  /** Template variables */
  variables: {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'date';
    required: boolean;
    description?: string;
    defaultValue?: any;
  }[];
  /** Template actions */
  actions?: NotificationAction[];
  /** Created by admin ID */
  createdBy: number;
  /** Creation timestamp */
  createdAt: string;
  /** Last updated timestamp */
  updatedAt: string;
  /** Is template active */
  active: boolean;
}

/**
 * Notification campaign
 */
export interface NotificationCampaign {
  /** Campaign identifier */
  id: string;
  /** Campaign name */
  name: string;
  /** Campaign description */
  description?: string;
  /** Template used for campaign */
  templateId: string;
  /** Campaign target */
  target: NotificationTarget;
  /** Specific user IDs */
  targetUserIds?: number[];
  /** User groups */
  targetGroups?: string[];
  /** Template variables values */
  templateVariables: Record<string, any>;
  /** Delivery methods */
  deliveryMethods: NotificationDeliveryMethod[];
  /** Scheduled send time */
  scheduledAt?: string;
  /** Campaign status */
  status: 'draft' | 'scheduled' | 'sending' | 'sent' | 'cancelled';
  /** Send statistics */
  stats?: {
    totalTargets: number;
    sent: number;
    delivered: number;
    read: number;
    failed: number;
  };
  /** Created by admin ID */
  createdBy: number;
  /** Creation timestamp */
  createdAt: string;
  /** Last updated timestamp */
  updatedAt: string;
}

/**
 * Notification delivery log
 */
export interface NotificationDeliveryLog {
  /** Log entry identifier */
  id: string;
  /** Notification ID */
  notificationId: string;
  /** User ID who received notification */
  userId: number;
  /** Delivery method used */
  deliveryMethod: NotificationDeliveryMethod;
  /** Delivery status */
  status: NotificationStatus;
  /** Delivery attempt timestamp */
  attemptedAt: string;
  /** Successful delivery timestamp */
  deliveredAt?: string;
  /** Read timestamp */
  readAt?: string;
  /** Error message if failed */
  errorMessage?: string;
  /** Delivery metadata */
  metadata?: Record<string, any>;
}

/**
 * Notification analytics
 */
export interface NotificationAnalytics {
  /** Date in YYYY-MM-DD format */
  date: string;
  /** Total notifications sent */
  totalSent: number;
  /** Total delivered */
  totalDelivered: number;
  /** Total read */
  totalRead: number;
  /** Total failed */
  totalFailed: number;
  /** Delivery rate percentage */
  deliveryRate: number;
  /** Read rate percentage */
  readRate: number;
  /** Breakdown by type */
  byType: Record<NotificationType, {
    sent: number;
    delivered: number;
    read: number;
    failed: number;
  }>;
  /** Breakdown by delivery method */
  byDeliveryMethod: Record<NotificationDeliveryMethod, {
    sent: number;
    delivered: number;
    read: number;
    failed: number;
  }>;
}

/**
 * Create notification payload
 */
export interface CreateNotificationPayload {
  /** Notification title */
  title: string;
  /** Notification message */
  message: string;
  /** Notification type */
  type: NotificationType;
  /** Priority level */
  priority: NotificationPriority;
  /** Target audience */
  target: NotificationTarget;
  /** Specific user IDs */
  targetUserIds?: number[];
  /** User groups */
  targetGroups?: string[];
  /** Delivery methods */
  deliveryMethods: NotificationDeliveryMethod[];
  /** Scheduled send time */
  scheduledAt?: string;
  /** Expiration time */
  expiresAt?: string;
  /** Rich content */
  richContent?: string;
  /** Actions */
  actions?: NotificationAction[];
  /** Attachments */
  attachments?: NotificationAttachment[];
  /** Metadata */
  metadata?: Record<string, any>;
}

/**
 * Notification search parameters
 */
export interface NotificationSearchParams {
  /** Search term */
  searchTerm?: string;
  /** Filter by type */
  type?: NotificationType;
  /** Filter by status */
  status?: NotificationStatus;
  /** Filter by priority */
  priority?: NotificationPriority;
  /** Filter by target */
  target?: NotificationTarget;
  /** Filter by creator */
  createdBy?: number;
  /** Start date */
  startDate?: string;
  /** End date */
  endDate?: string;
  /** Sort by */
  sortBy?: 'createdAt' | 'scheduledAt' | 'sentAt' | 'priority';
  /** Sort order */
  sortOrder?: 'asc' | 'desc';
  /** Page number */
  page?: number;
  /** Items per page */
  limit?: number;
}
