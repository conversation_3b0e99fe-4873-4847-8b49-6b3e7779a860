/**
 * User-related type definitions
 * Extracted from existing UserCreditManagement component data structures
 */

/**
 * Monthly statistics for user activity
 * Tracks video/photo creation success and error rates
 */
export interface UserMonthlyStats {
  /** Number of videos successfully completed this month */
  videosCompleted: number;
  /** Number of videos that failed/errored this month */
  videosErrors: number;
  /** Number of photos successfully completed this month */
  photosCompleted: number;
  /** Number of photos that failed/errored this month */
  photosErrors: number;
  /** Total credits used this month */
  creditsUsed: number;
  /** Total credits added this month */
  creditsAdded: number;
}

/**
 * Core user entity with all properties from existing data structure
 * Matches exactly with users array in UserCreditManagement.tsx
 */
export interface User {
  /** Unique user identifier */
  id: number;
  /** Full name of the user */
  name: string;
  /** Email address */
  email: string;
  /** Phone number with country code */
  phone: string;
  /** Current available credits balance */
  credits: number;
  /** Total number of videos created by user */
  videosCreated: number;
  /** Total number of photos created by user */
  photosCreated: number;
  /** Total credits used throughout user lifetime */
  totalUsed: number;
  /** Total credits added throughout user lifetime */
  totalAdded: number;
  /** Monthly statistics for current month */
  monthlyStats: UserMonthlyStats;
  /** Last activity timestamp (ISO string format) */
  lastActivity: string;
  /** User registration date (ISO string format) */
  joinDate: string;
  /** Current user status */
  status: 'active' | 'premium' | 'inactive';
}

/**
 * User profile information for display purposes
 */
export interface UserProfile {
  id: number;
  name: string;
  email: string;
  phone: string;
  joinDate: string;
  lastActivity: string;
  status: 'active' | 'premium' | 'inactive';
}

/**
 * User credit summary information
 */
export interface UserCreditSummary {
  id: number;
  name: string;
  credits: number;
  totalUsed: number;
  totalAdded: number;
  monthlyCreditsUsed: number;
  monthlyCreditsAdded: number;
}

/**
 * User content creation summary
 */
export interface UserContentSummary {
  id: number;
  name: string;
  videosCreated: number;
  photosCreated: number;
  monthlyVideosCompleted: number;
  monthlyVideosErrors: number;
  monthlyPhotosCompleted: number;
  monthlyPhotosErrors: number;
  videoSuccessRate: number;
  photoSuccessRate: number;
}

/**
 * User creation/update payload
 */
export interface CreateUserPayload {
  name: string;
  email: string;
  phone: string;
  initialCredits?: number;
  status?: 'active' | 'premium' | 'inactive';
}

/**
 * User update payload
 */
export interface UpdateUserPayload {
  name?: string;
  email?: string;
  phone?: string;
  status?: 'active' | 'premium' | 'inactive';
}

/**
 * User search and filter parameters
 */
export interface UserSearchParams {
  searchTerm?: string;
  status?: 'active' | 'premium' | 'inactive' | 'all';
  sortBy?: 'name' | 'credits' | 'lastActivity' | 'joinDate';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

/**
 * User list response with pagination
 */
export interface UserListResponse {
  users: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
