/**
 * Model Layer - Central Export
 * 
 * This is the main entry point for the Model Layer in our 3-layer MVI architecture.
 * The Model Layer contains pure data types and business constants only.
 * 
 * Architecture Rules:
 * - Model Layer contains ONLY types and business constants
 * - NO API endpoints, cache keys, or data access logic (those belong in Data Layer)
 * - NO validation schemas (those belong in Interface Layer utils)
 * - NO UI-specific constants (those belong in Interface Layer)
 * 
 * Dependencies:
 * - Model Layer has NO dependencies on other layers
 * - Other layers can import from Model Layer
 * - This ensures clean separation of concerns
 */

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Re-export all types from the types module
export type {
  // User types
  User,
  UserProfile,
  UserCreditSummary,
  UserContentSummary,
  UserMonthlyStats,
  CreateUserPayload,
  UpdateUserPayload,
  UserSearchParams,
  UserListResponse,

  // Credit types
  CreditTransaction,
  CreditBalance,
  CreditUsageStats,
  CreditOperationPayload,
  CreditSystemConfig,
  CreditPackage,
  CreditTransactionQuery,
  CreditTransactionHistoryResponse,
  CreditTransactionType,
  CreditTransactionStatus,
  CreditUsageContext,

  // Analytics types
  DashboardStat,
  QuickStat,
  AnalyticsData,
  UserGrowthData,
  CreditUsageData,
  ContentCreationData,
  SystemPerformanceData,
  AnalyticsSummary,
  AnalyticsQuery,
  MetricComparison,
  TopPerformers,
  AnalyticsExportConfig,
  ChangeType,
  TimePeriod,

  // Content types
  Content,
  BaseContent,
  VideoContent,
  PhotoContent,
  CreateContentPayload,
  UpdateContentPayload,
  ContentSearchParams,
  ContentListResponse,
  ContentStats,
  ContentProcessingJob,
  BulkContentOperation,
  ContentAnalytics,
  ContentType,
  ProcessingStatus,
  QualityLevel,
  ContentVisibility,

  // Notification types
  Notification,
  NotificationAction,
  NotificationAttachment,
  NotificationPreferences,
  NotificationTemplate,
  NotificationCampaign,
  NotificationDeliveryLog,
  NotificationAnalytics,
  CreateNotificationPayload,
  NotificationSearchParams,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  NotificationDeliveryMethod,
  NotificationTarget,

  // System types
  SystemConfig,
  SystemStatus,
  AdminUser,
  AuditLogEntry,
  FeatureFlags,
  SystemCreditConfig,
  ContentProcessingConfig,
  SecurityConfig,
  EmailConfig,
  StorageConfig,
  AnalyticsConfig,
  MaintenanceSettings,
  SystemLimits,
  MonitoringConfig,

  // Common types
  ApiResponse,
  PaginationParams,
  PaginationMeta,
  PaginatedResponse,
  SortParams,
  FilterCondition,
  FilterGroup,
  SearchParams,
  DateRange,
  CommonTimePeriod,
  LoadingState,
  ErrorState,
  AsyncState,
  ValidationError,
  FormState,
  FileUploadState,
  NotificationMessage,
  ThemeMode,
  LanguageCode,
  UserPreferences,
  SystemHealth,
  FilterOperator,
  CommonNotificationType,

  // Utility types
  AnyContent,
  AnyNotification,
  AnyUser,
  AnyTransaction,
  ID,
  Timestamp,
  Email,
  PhoneNumber,
  URL,
  Status,
  ProcessStatus,
  OperationResult,
  BaseEntity,
  AuditTrail,
  SoftDelete,
  CompleteEntity,
  ListResponse,
  CreateResponse,
  UpdateResponse,
  DeleteResponse,
  BulkOperation,
  BulkOperationResult,
  ValidationSchema,

  // API Response types
  UserResponse,
  UserListApiResponse,
  ContentResponse,
  ContentListApiResponse,
  AnalyticsResponse,
  SystemStatusResponse,
} from './types';

// ============================================================================
// CONSTANT EXPORTS
// ============================================================================

// Re-export all constants from the constants module
export {
  // User status constants
  USER_STATUS,
  USER_ROLE,
  PERMISSION_LEVEL,
  USER_STATUS_LABELS,
  USER_STATUS_COLORS,
  USER_ROLE_LABELS,
  PERMISSION_LEVEL_LABELS,
  DEFAULT_ROLE_PERMISSIONS,
  USER_ACTIVITY_STATUS,
  USER_ACTIVITY_STATUS_LABELS,
  USER_ACTIVITY_STATUS_COLORS,
  USER_REGISTRATION_SOURCE,
  USER_VERIFICATION_STATUS,
  USER_VERIFICATION_STATUS_LABELS,
  USER_LIMITS,
  DEFAULT_USER_PREFERENCES,

  // Credit constants
  CREDIT_TRANSACTION_TYPE,
  CREDIT_TRANSACTION_STATUS,
  CREDIT_USAGE_CONTEXT,
  PAYMENT_STATUS,
  CREDIT_TRANSACTION_TYPE_LABELS,
  CREDIT_TRANSACTION_STATUS_LABELS,
  CREDIT_USAGE_CONTEXT_LABELS,
  PAYMENT_STATUS_LABELS,
  CREDIT_TRANSACTION_TYPE_COLORS,
  CREDIT_TRANSACTION_STATUS_COLORS,
  PAYMENT_STATUS_COLORS,
  DEFAULT_CREDIT_COSTS,
  CREDIT_PACKAGES,
  CREDIT_LIMITS,

  // Content constants
  CONTENT_TYPE,
  PROCESSING_STATUS,
  QUALITY_LEVEL,
  CONTENT_VISIBILITY,
  CONTENT_TYPE_LABELS,
  PROCESSING_STATUS_LABELS,
  QUALITY_LEVEL_LABELS,
  CONTENT_VISIBILITY_LABELS,
  PROCESSING_STATUS_COLORS,
  QUALITY_LEVEL_COLORS,
  CONTENT_VISIBILITY_COLORS,
  VIDEO_QUALITY_SPECS,
  PHOTO_QUALITY_SPECS,
  SUPPORTED_VIDEO_FORMATS,
  SUPPORTED_PHOTO_FORMATS,
  SUPPORTED_AUDIO_FORMATS,
  FILE_SIZE_LIMITS,
  PROCESSING_TIMEOUTS,
  CONTENT_CATEGORIES,
  CONTENT_CATEGORY_LABELS,

  // App configuration constants
  PAGINATION_CONFIG,
  API_CONFIG,
  CACHE_CONFIG,
  UI_CONFIG,
  DATE_TIME_CONFIG,
  LANGUAGE_CONFIG,
  THEME_CONFIG,
  FILE_UPLOAD_CONFIG,
  SEARCH_CONFIG,
  NOTIFICATION_CONFIG,
  ANALYTICS_CONFIG,
  SECURITY_CONFIG,
  PERFORMANCE_CONFIG,
  DEV_CONFIG,
  FEATURE_FLAGS,
  ERROR_CONFIG,
  MONITORING_CONFIG,
  BACKUP_CONFIG,
  APP_METADATA,

  // Common constants
  COMMON_CONSTANTS,
  ALL_CONSTANTS,
} from './constants';

// ============================================================================
// UTILITY FUNCTION EXPORTS
// ============================================================================

// Re-export utility functions from constants
export {
  // User utilities
  isValidUserStatus,
  isValidUserRole,
  isValidPermissionLevel,
  hasPermission,
  getUserStatusPriority,
  getUserRolePriority,

  // Credit utilities
  isValidCreditTransactionType,
  isValidCreditTransactionStatus,
  isValidCreditUsageContext,
  isValidPaymentStatus,
  getCreditCost,
  formatCredits,
  calculatePackageValue,

  // Content utilities
  isValidContentType,
  isValidProcessingStatus,
  isValidQualityLevel,
  isValidContentVisibility,
  isValidContentCategory,
  getProcessingTimeout,
  getCreditMultiplier,
  isSupportedFormat,
  getFileSizeLimit,

  // Environment utilities
  getEnvironmentConfig,

  // Validation helpers
  VALIDATION_HELPERS,
} from './constants';

// Re-export type guards from types
export {
  isUser,
  isContent,
  isCreditTransaction,
  isNotification,
} from './types';

// ============================================================================
// TYPE ALIASES FOR CONVENIENCE
// ============================================================================

// Export commonly used type combinations
export type {
  UserStatus,
  UserRole,
  PermissionLevel,
  UserActivityStatus,
  UserRegistrationSource,
  UserVerificationStatus,
  CreditTransactionType as TransactionType,
  CreditTransactionStatus as TransactionStatus,
  CreditUsageContext as UsageContext,
  PaymentStatus,
  ContentType,
  ProcessingStatus,
  QualityLevel,
  ContentVisibility,
  ContentCategory,
  NotificationType,
  NotificationPriority,
  NotificationStatus,
  NotificationDeliveryMethod,
  NotificationTarget,
} from './constants';

// ============================================================================
// MODEL LAYER METADATA
// ============================================================================

/**
 * Model Layer metadata for debugging and documentation
 */
export const MODEL_LAYER_INFO = {
  version: '1.0.0',
  description: 'Model Layer for Mega AI Admin - Contains pure types and business constants',
  architecture: '3-layer MVI',
  dependencies: [],
  exports: {
    types: [
      'User', 'Credit', 'Content', 'Analytics', 'Notification', 'System', 'Common'
    ],
    constants: [
      'UserStatus', 'CreditTypes', 'ContentTypes', 'AppConfig', 'CommonConstants'
    ],
    utilities: [
      'Validation helpers', 'Type guards', 'Utility functions'
    ]
  },
  rules: [
    'No API endpoints or data access logic',
    'No validation schemas (belong in Interface Layer)',
    'No UI-specific constants',
    'Pure types and business constants only',
    'No dependencies on other layers'
  ]
} as const;

// ============================================================================
// DEVELOPMENT HELPERS
// ============================================================================

/**
 * Development helper to validate Model Layer integrity
 * Only available in development mode
 */
export const validateModelLayer = () => {
  if (process.env.NODE_ENV !== 'development') {
    return { valid: true, message: 'Validation only available in development' };
  }

  const checks = [
    // Check that all required exports are available
    typeof USER_STATUS !== 'undefined',
    typeof CONTENT_TYPE !== 'undefined',
    typeof CREDIT_TRANSACTION_TYPE !== 'undefined',
    // Add more checks as needed
  ];

  const allValid = checks.every(check => check === true);

  return {
    valid: allValid,
    message: allValid 
      ? 'Model Layer validation passed' 
      : 'Model Layer validation failed - some exports are missing',
    timestamp: new Date().toISOString()
  };
};
