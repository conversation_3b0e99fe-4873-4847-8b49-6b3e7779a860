/**
 * Credit system constants and enums
 * Defines credit transaction types, payment status, and credit-related configurations
 */

/**
 * Credit transaction types
 */
export const CREDIT_TRANSACTION_TYPE = {
  ADD: 'add',
  SUBTRACT: 'subtract',
  USED: 'used',
  REFUND: 'refund',
  BONUS: 'bonus',
  EXPIRED: 'expired',
  TRANSFER: 'transfer',
} as const;

export type CreditTransactionType = typeof CREDIT_TRANSACTION_TYPE[keyof typeof CREDIT_TRANSACTION_TYPE];

/**
 * Credit transaction status
 */
export const CREDIT_TRANSACTION_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  PROCESSING: 'processing',
} as const;

export type CreditTransactionStatus = typeof CREDIT_TRANSACTION_STATUS[keyof typeof CREDIT_TRANSACTION_STATUS];

/**
 * Credit usage context - what credits were used for
 */
export const CREDIT_USAGE_CONTEXT = {
  VIDEO_CREATION: 'video_creation',
  PHOTO_CREATION: 'photo_creation',
  PREMIUM_FEATURE: 'premium_feature',
  API_CALL: 'api_call',
  STORAGE: 'storage',
  PROCESSING: 'processing',
  OTHER: 'other',
} as const;

export type CreditUsageContext = typeof CREDIT_USAGE_CONTEXT[keyof typeof CREDIT_USAGE_CONTEXT];

/**
 * Payment status for credit purchases
 */
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
  PARTIALLY_REFUNDED: 'partially_refunded',
} as const;

export type PaymentStatus = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];

/**
 * Credit transaction type labels (Vietnamese)
 */
export const CREDIT_TRANSACTION_TYPE_LABELS: Record<CreditTransactionType, string> = {
  [CREDIT_TRANSACTION_TYPE.ADD]: 'Thêm credits',
  [CREDIT_TRANSACTION_TYPE.SUBTRACT]: 'Trừ credits',
  [CREDIT_TRANSACTION_TYPE.USED]: 'Sử dụng credits',
  [CREDIT_TRANSACTION_TYPE.REFUND]: 'Hoàn trả credits',
  [CREDIT_TRANSACTION_TYPE.BONUS]: 'Credits thưởng',
  [CREDIT_TRANSACTION_TYPE.EXPIRED]: 'Credits hết hạn',
  [CREDIT_TRANSACTION_TYPE.TRANSFER]: 'Chuyển credits',
};

/**
 * Credit transaction status labels (Vietnamese)
 */
export const CREDIT_TRANSACTION_STATUS_LABELS: Record<CreditTransactionStatus, string> = {
  [CREDIT_TRANSACTION_STATUS.PENDING]: 'Đang chờ',
  [CREDIT_TRANSACTION_STATUS.COMPLETED]: 'Hoàn thành',
  [CREDIT_TRANSACTION_STATUS.FAILED]: 'Thất bại',
  [CREDIT_TRANSACTION_STATUS.CANCELLED]: 'Đã hủy',
  [CREDIT_TRANSACTION_STATUS.PROCESSING]: 'Đang xử lý',
};

/**
 * Credit usage context labels (Vietnamese)
 */
export const CREDIT_USAGE_CONTEXT_LABELS: Record<CreditUsageContext, string> = {
  [CREDIT_USAGE_CONTEXT.VIDEO_CREATION]: 'Tạo video',
  [CREDIT_USAGE_CONTEXT.PHOTO_CREATION]: 'Tạo ảnh',
  [CREDIT_USAGE_CONTEXT.PREMIUM_FEATURE]: 'Tính năng premium',
  [CREDIT_USAGE_CONTEXT.API_CALL]: 'Gọi API',
  [CREDIT_USAGE_CONTEXT.STORAGE]: 'Lưu trữ',
  [CREDIT_USAGE_CONTEXT.PROCESSING]: 'Xử lý',
  [CREDIT_USAGE_CONTEXT.OTHER]: 'Khác',
};

/**
 * Payment status labels (Vietnamese)
 */
export const PAYMENT_STATUS_LABELS: Record<PaymentStatus, string> = {
  [PAYMENT_STATUS.PENDING]: 'Đang chờ thanh toán',
  [PAYMENT_STATUS.PROCESSING]: 'Đang xử lý',
  [PAYMENT_STATUS.COMPLETED]: 'Thanh toán thành công',
  [PAYMENT_STATUS.FAILED]: 'Thanh toán thất bại',
  [PAYMENT_STATUS.CANCELLED]: 'Đã hủy',
  [PAYMENT_STATUS.REFUNDED]: 'Đã hoàn tiền',
  [PAYMENT_STATUS.PARTIALLY_REFUNDED]: 'Hoàn tiền một phần',
};

/**
 * Credit transaction type colors for UI
 */
export const CREDIT_TRANSACTION_TYPE_COLORS: Record<CreditTransactionType, string> = {
  [CREDIT_TRANSACTION_TYPE.ADD]: 'bg-green-100 text-green-800',
  [CREDIT_TRANSACTION_TYPE.SUBTRACT]: 'bg-red-100 text-red-800',
  [CREDIT_TRANSACTION_TYPE.USED]: 'bg-blue-100 text-blue-800',
  [CREDIT_TRANSACTION_TYPE.REFUND]: 'bg-purple-100 text-purple-800',
  [CREDIT_TRANSACTION_TYPE.BONUS]: 'bg-yellow-100 text-yellow-800',
  [CREDIT_TRANSACTION_TYPE.EXPIRED]: 'bg-gray-100 text-gray-800',
  [CREDIT_TRANSACTION_TYPE.TRANSFER]: 'bg-indigo-100 text-indigo-800',
};

/**
 * Credit transaction status colors for UI
 */
export const CREDIT_TRANSACTION_STATUS_COLORS: Record<CreditTransactionStatus, string> = {
  [CREDIT_TRANSACTION_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800',
  [CREDIT_TRANSACTION_STATUS.COMPLETED]: 'bg-green-100 text-green-800',
  [CREDIT_TRANSACTION_STATUS.FAILED]: 'bg-red-100 text-red-800',
  [CREDIT_TRANSACTION_STATUS.CANCELLED]: 'bg-gray-100 text-gray-800',
  [CREDIT_TRANSACTION_STATUS.PROCESSING]: 'bg-blue-100 text-blue-800',
};

/**
 * Payment status colors for UI
 */
export const PAYMENT_STATUS_COLORS: Record<PaymentStatus, string> = {
  [PAYMENT_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800',
  [PAYMENT_STATUS.PROCESSING]: 'bg-blue-100 text-blue-800',
  [PAYMENT_STATUS.COMPLETED]: 'bg-green-100 text-green-800',
  [PAYMENT_STATUS.FAILED]: 'bg-red-100 text-red-800',
  [PAYMENT_STATUS.CANCELLED]: 'bg-gray-100 text-gray-800',
  [PAYMENT_STATUS.REFUNDED]: 'bg-purple-100 text-purple-800',
  [PAYMENT_STATUS.PARTIALLY_REFUNDED]: 'bg-orange-100 text-orange-800',
};

/**
 * Default credit costs for different operations
 */
export const DEFAULT_CREDIT_COSTS = {
  VIDEO_CREATION_LOW: 10,
  VIDEO_CREATION_MEDIUM: 20,
  VIDEO_CREATION_HIGH: 40,
  VIDEO_CREATION_ULTRA: 80,
  PHOTO_CREATION_LOW: 5,
  PHOTO_CREATION_MEDIUM: 10,
  PHOTO_CREATION_HIGH: 20,
  PHOTO_CREATION_ULTRA: 40,
  API_CALL: 1,
  STORAGE_PER_GB: 5,
  PREMIUM_FEATURE: 50,
} as const;

/**
 * Credit package configurations
 */
export const CREDIT_PACKAGES = {
  STARTER: {
    id: 'starter',
    name: 'Gói Khởi Đầu',
    credits: 100,
    price: 50000, // VND
    bonus: 0,
    popular: false,
  },
  BASIC: {
    id: 'basic',
    name: 'Gói Cơ Bản',
    credits: 250,
    price: 100000, // VND
    bonus: 25,
    popular: false,
  },
  POPULAR: {
    id: 'popular',
    name: 'Gói Phổ Biến',
    credits: 500,
    price: 180000, // VND
    bonus: 100,
    popular: true,
  },
  PREMIUM: {
    id: 'premium',
    name: 'Gói Premium',
    credits: 1000,
    price: 300000, // VND
    bonus: 250,
    popular: false,
  },
  ENTERPRISE: {
    id: 'enterprise',
    name: 'Gói Doanh Nghiệp',
    credits: 2500,
    price: 650000, // VND
    bonus: 750,
    popular: false,
  },
} as const;

/**
 * Credit system limits and configurations
 */
export const CREDIT_LIMITS = {
  MIN_PURCHASE: 10,
  MAX_PURCHASE: 10000,
  MIN_BALANCE: 0,
  MAX_BALANCE: 50000,
  DEFAULT_NEW_USER_CREDITS: 50,
  PREMIUM_USER_BONUS: 100,
  REFERRAL_BONUS: 25,
  DAILY_FREE_CREDITS: 5,
  EXPIRATION_DAYS: 365, // Credits expire after 1 year
  LOW_BALANCE_THRESHOLD: 10,
  AUTO_REFILL_THRESHOLD: 5,
  AUTO_REFILL_AMOUNT: 100,
} as const;

/**
 * Credit validation functions
 */
export const isValidCreditTransactionType = (type: string): type is CreditTransactionType => {
  return Object.values(CREDIT_TRANSACTION_TYPE).includes(type as CreditTransactionType);
};

export const isValidCreditTransactionStatus = (status: string): status is CreditTransactionStatus => {
  return Object.values(CREDIT_TRANSACTION_STATUS).includes(status as CreditTransactionStatus);
};

export const isValidCreditUsageContext = (context: string): context is CreditUsageContext => {
  return Object.values(CREDIT_USAGE_CONTEXT).includes(context as CreditUsageContext);
};

export const isValidPaymentStatus = (status: string): status is PaymentStatus => {
  return Object.values(PAYMENT_STATUS).includes(status as PaymentStatus);
};

/**
 * Get credit cost for specific operation
 */
export const getCreditCost = (operation: CreditUsageContext, quality?: string): number => {
  switch (operation) {
    case CREDIT_USAGE_CONTEXT.VIDEO_CREATION:
      switch (quality) {
        case 'low': return DEFAULT_CREDIT_COSTS.VIDEO_CREATION_LOW;
        case 'medium': return DEFAULT_CREDIT_COSTS.VIDEO_CREATION_MEDIUM;
        case 'high': return DEFAULT_CREDIT_COSTS.VIDEO_CREATION_HIGH;
        case 'ultra': return DEFAULT_CREDIT_COSTS.VIDEO_CREATION_ULTRA;
        default: return DEFAULT_CREDIT_COSTS.VIDEO_CREATION_MEDIUM;
      }
    case CREDIT_USAGE_CONTEXT.PHOTO_CREATION:
      switch (quality) {
        case 'low': return DEFAULT_CREDIT_COSTS.PHOTO_CREATION_LOW;
        case 'medium': return DEFAULT_CREDIT_COSTS.PHOTO_CREATION_MEDIUM;
        case 'high': return DEFAULT_CREDIT_COSTS.PHOTO_CREATION_HIGH;
        case 'ultra': return DEFAULT_CREDIT_COSTS.PHOTO_CREATION_ULTRA;
        default: return DEFAULT_CREDIT_COSTS.PHOTO_CREATION_MEDIUM;
      }
    case CREDIT_USAGE_CONTEXT.API_CALL:
      return DEFAULT_CREDIT_COSTS.API_CALL;
    case CREDIT_USAGE_CONTEXT.PREMIUM_FEATURE:
      return DEFAULT_CREDIT_COSTS.PREMIUM_FEATURE;
    default:
      return 1;
  }
};

/**
 * Format credit amount for display
 */
export const formatCredits = (amount: number): string => {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  }
  if (amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }
  return amount.toLocaleString('vi-VN');
};

/**
 * Calculate credit package value (credits + bonus)
 */
export const calculatePackageValue = (packageId: string): number => {
  const pkg = Object.values(CREDIT_PACKAGES).find(p => p.id === packageId);
  return pkg ? pkg.credits + pkg.bonus : 0;
};
