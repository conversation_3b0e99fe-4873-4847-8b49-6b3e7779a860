/**
 * Central export file for all constants
 * Provides a single import point for all model constants
 */

// User status constants
export {
  USER_STATUS,
  USER_ROLE,
  PERMISSION_LEVEL,
  USER_STATUS_LABELS,
  USER_STATUS_COLORS,
  USER_ROLE_LABELS,
  PERMISSION_LEVEL_LABELS,
  DEFAULT_ROLE_PERMISSIONS,
  USER_ACTIVITY_STATUS,
  USER_ACTIVITY_STATUS_LABELS,
  USER_ACTIVITY_STATUS_COLORS,
  USER_REGISTRATION_SOURCE,
  USER_VERIFICATION_STATUS,
  USER_VERIFICATION_STATUS_LABELS,
  USER_LIMITS,
  DEFAULT_USER_PREFERENCES,
  isValidUserStatus,
  isValidUserRole,
  isValidPermissionLevel,
  hasPermission,
  getUserStatusPriority,
  getUserRolePriority,
} from './userStatus';

export type {
  UserStatus,
  UserRole,
  PermissionLevel,
  UserActivityStatus,
  UserRegistrationSource,
  UserVerificationStatus,
} from './userStatus';

// Credit types constants
export {
  CREDIT_TRANSACTION_TYPE,
  CREDIT_TRANSACTION_STATUS,
  CREDIT_USAGE_CONTEXT,
  PAYMENT_STATUS,
  CREDIT_TRANSACTION_TYPE_LABELS,
  CREDIT_TRANSACTION_STATUS_LABELS,
  CREDIT_USAGE_CONTEXT_LABELS,
  PAYMENT_STATUS_LABELS,
  CREDIT_TRANSACTION_TYPE_COLORS,
  CREDIT_TRANSACTION_STATUS_COLORS,
  PAYMENT_STATUS_COLORS,
  DEFAULT_CREDIT_COSTS,
  CREDIT_PACKAGES,
  CREDIT_LIMITS,
  isValidCreditTransactionType,
  isValidCreditTransactionStatus,
  isValidCreditUsageContext,
  isValidPaymentStatus,
  getCreditCost,
  formatCredits,
  calculatePackageValue,
} from './creditTypes';

export type {
  CreditTransactionType,
  CreditTransactionStatus,
  CreditUsageContext,
  PaymentStatus,
} from './creditTypes';

// Content types constants
export {
  CONTENT_TYPE,
  PROCESSING_STATUS,
  QUALITY_LEVEL,
  CONTENT_VISIBILITY,
  CONTENT_TYPE_LABELS,
  PROCESSING_STATUS_LABELS,
  QUALITY_LEVEL_LABELS,
  CONTENT_VISIBILITY_LABELS,
  PROCESSING_STATUS_COLORS,
  QUALITY_LEVEL_COLORS,
  CONTENT_VISIBILITY_COLORS,
  VIDEO_QUALITY_SPECS,
  PHOTO_QUALITY_SPECS,
  SUPPORTED_VIDEO_FORMATS,
  SUPPORTED_PHOTO_FORMATS,
  SUPPORTED_AUDIO_FORMATS,
  FILE_SIZE_LIMITS,
  PROCESSING_TIMEOUTS,
  CONTENT_CATEGORIES,
  CONTENT_CATEGORY_LABELS,
  isValidContentType,
  isValidProcessingStatus,
  isValidQualityLevel,
  isValidContentVisibility,
  isValidContentCategory,
  getProcessingTimeout,
  getCreditMultiplier,
  isSupportedFormat,
  getFileSizeLimit,
} from './contentTypes';

export type {
  ContentType,
  ProcessingStatus,
  QualityLevel,
  ContentVisibility,
  ContentCategory,
} from './contentTypes';

// App configuration constants
export {
  PAGINATION_CONFIG,
  API_CONFIG,
  CACHE_CONFIG,
  UI_CONFIG,
  DATE_TIME_CONFIG,
  LANGUAGE_CONFIG,
  THEME_CONFIG,
  FILE_UPLOAD_CONFIG,
  SEARCH_CONFIG,
  NOTIFICATION_CONFIG,
  ANALYTICS_CONFIG,
  SECURITY_CONFIG,
  PERFORMANCE_CONFIG,
  DEV_CONFIG,
  FEATURE_FLAGS,
  ERROR_CONFIG,
  MONITORING_CONFIG,
  BACKUP_CONFIG,
  APP_METADATA,
  getEnvironmentConfig,
} from './appConfig';

// Common constants used across the application
export const COMMON_CONSTANTS = {
  // HTTP Status Codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503,
  },

  // Common regex patterns
  REGEX: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE: /^(\+84|0)[0-9]{9,10}$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
    URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
    SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
    HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
  },

  // Date formats
  DATE_FORMATS: {
    ISO: 'YYYY-MM-DDTHH:mm:ss.SSSZ',
    DATE_ONLY: 'YYYY-MM-DD',
    TIME_ONLY: 'HH:mm:ss',
    DISPLAY: 'DD/MM/YYYY',
    DISPLAY_WITH_TIME: 'DD/MM/YYYY HH:mm',
    RELATIVE: 'relative',
  },

  // File types
  FILE_TYPES: {
    IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff'],
    VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'],
    AUDIO: ['mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a'],
    DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
    ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz'],
  },

  // Sort directions
  SORT_DIRECTION: {
    ASC: 'asc',
    DESC: 'desc',
  },

  // Loading states
  LOADING_STATE: {
    IDLE: 'idle',
    LOADING: 'loading',
    SUCCESS: 'success',
    ERROR: 'error',
  },

  // Modal sizes
  MODAL_SIZE: {
    SMALL: 'sm',
    MEDIUM: 'md',
    LARGE: 'lg',
    EXTRA_LARGE: 'xl',
    FULL: 'full',
  },

  // Button variants
  BUTTON_VARIANT: {
    PRIMARY: 'primary',
    SECONDARY: 'secondary',
    OUTLINE: 'outline',
    GHOST: 'ghost',
    LINK: 'link',
    DESTRUCTIVE: 'destructive',
  },

  // Button sizes
  BUTTON_SIZE: {
    SMALL: 'sm',
    MEDIUM: 'md',
    LARGE: 'lg',
    ICON: 'icon',
  },

  // Alert types
  ALERT_TYPE: {
    INFO: 'info',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
  },

  // Table row actions
  TABLE_ACTION: {
    VIEW: 'view',
    EDIT: 'edit',
    DELETE: 'delete',
    DUPLICATE: 'duplicate',
    EXPORT: 'export',
  },

  // Export formats
  EXPORT_FORMAT: {
    CSV: 'csv',
    XLSX: 'xlsx',
    PDF: 'pdf',
    JSON: 'json',
  },

  // Time periods
  TIME_PERIOD: {
    TODAY: 'today',
    YESTERDAY: 'yesterday',
    LAST_7_DAYS: 'last_7_days',
    LAST_30_DAYS: 'last_30_days',
    LAST_90_DAYS: 'last_90_days',
    THIS_MONTH: 'this_month',
    LAST_MONTH: 'last_month',
    THIS_QUARTER: 'this_quarter',
    LAST_QUARTER: 'last_quarter',
    THIS_YEAR: 'this_year',
    LAST_YEAR: 'last_year',
    CUSTOM: 'custom',
  },

  // Chart types
  CHART_TYPE: {
    LINE: 'line',
    BAR: 'bar',
    PIE: 'pie',
    DOUGHNUT: 'doughnut',
    AREA: 'area',
    SCATTER: 'scatter',
  },

  // Breakpoints (matching Tailwind CSS)
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536,
  },

  // Z-index layers
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
} as const;

// Validation helper functions
export const VALIDATION_HELPERS = {
  isEmail: (email: string): boolean => COMMON_CONSTANTS.REGEX.EMAIL.test(email),
  isPhone: (phone: string): boolean => COMMON_CONSTANTS.REGEX.PHONE.test(phone),
  isPassword: (password: string): boolean => COMMON_CONSTANTS.REGEX.PASSWORD.test(password),
  isUrl: (url: string): boolean => COMMON_CONSTANTS.REGEX.URL.test(url),
  isSlug: (slug: string): boolean => COMMON_CONSTANTS.REGEX.SLUG.test(slug),
  isHexColor: (color: string): boolean => COMMON_CONSTANTS.REGEX.HEX_COLOR.test(color),
  
  isImageFile: (filename: string): boolean => {
    const ext = filename.split('.').pop()?.toLowerCase();
    return ext ? COMMON_CONSTANTS.FILE_TYPES.IMAGE.includes(ext) : false;
  },
  
  isVideoFile: (filename: string): boolean => {
    const ext = filename.split('.').pop()?.toLowerCase();
    return ext ? COMMON_CONSTANTS.FILE_TYPES.VIDEO.includes(ext) : false;
  },
  
  isAudioFile: (filename: string): boolean => {
    const ext = filename.split('.').pop()?.toLowerCase();
    return ext ? COMMON_CONSTANTS.FILE_TYPES.AUDIO.includes(ext) : false;
  },
  
  getFileType: (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    if (!ext) return 'unknown';
    
    if (COMMON_CONSTANTS.FILE_TYPES.IMAGE.includes(ext)) return 'image';
    if (COMMON_CONSTANTS.FILE_TYPES.VIDEO.includes(ext)) return 'video';
    if (COMMON_CONSTANTS.FILE_TYPES.AUDIO.includes(ext)) return 'audio';
    if (COMMON_CONSTANTS.FILE_TYPES.DOCUMENT.includes(ext)) return 'document';
    if (COMMON_CONSTANTS.FILE_TYPES.ARCHIVE.includes(ext)) return 'archive';
    
    return 'unknown';
  },
} as const;

// Export all constants as a single object for convenience
export const ALL_CONSTANTS = {
  USER_STATUS,
  USER_ROLE,
  PERMISSION_LEVEL,
  CREDIT_TRANSACTION_TYPE,
  CREDIT_TRANSACTION_STATUS,
  CREDIT_USAGE_CONTEXT,
  PAYMENT_STATUS,
  CONTENT_TYPE,
  PROCESSING_STATUS,
  QUALITY_LEVEL,
  CONTENT_VISIBILITY,
  CONTENT_CATEGORIES,
  PAGINATION_CONFIG,
  API_CONFIG,
  CACHE_CONFIG,
  UI_CONFIG,
  DATE_TIME_CONFIG,
  LANGUAGE_CONFIG,
  THEME_CONFIG,
  FEATURE_FLAGS,
  ...COMMON_CONSTANTS,
} as const;
