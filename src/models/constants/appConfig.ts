/**
 * Application configuration constants
 * Defines app-wide settings, limits, and default values
 */

/**
 * Pagination configuration
 */
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 5,
  <PERSON><PERSON><PERSON><PERSON><PERSON>_PAGE_SIZES: [5, 10, 20, 50, 100],
} as const;

/**
 * API configuration
 */
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  REQUEST_TIMEOUT: 30000,
  UPLOAD_TIMEOUT: 300000, // 5 minutes for file uploads
} as const;

/**
 * Cache configuration
 */
export const CACHE_CONFIG = {
  DEFAULT_STALE_TIME: 5 * 60 * 1000, // 5 minutes
  DEFAULT_CACHE_TIME: 10 * 60 * 1000, // 10 minutes
  LONG_CACHE_TIME: 60 * 60 * 1000, // 1 hour
  SHORT_CACHE_TIME: 1 * 60 * 1000, // 1 minute
  REFETCH_INTERVAL: 30 * 1000, // 30 seconds for real-time data
} as const;

/**
 * UI configuration
 */
export const UI_CONFIG = {
  TOAST_DURATION: 5000, // 5 seconds
  DEBOUNCE_DELAY: 300, // 300ms for search inputs
  ANIMATION_DURATION: 200, // 200ms for transitions
  SIDEBAR_WIDTH: 256, // pixels
  SIDEBAR_COLLAPSED_WIDTH: 64, // pixels
  HEADER_HEIGHT: 64, // pixels
  MOBILE_BREAKPOINT: 768, // pixels
  TABLET_BREAKPOINT: 1024, // pixels
  DESKTOP_BREAKPOINT: 1280, // pixels
} as const;

/**
 * Date and time configuration
 */
export const DATE_TIME_CONFIG = {
  DEFAULT_DATE_FORMAT: 'DD/MM/YYYY',
  DEFAULT_TIME_FORMAT: 'HH:mm',
  DEFAULT_DATETIME_FORMAT: 'DD/MM/YYYY HH:mm',
  DEFAULT_TIMEZONE: 'Asia/Ho_Chi_Minh',
  SUPPORTED_TIMEZONES: [
    'Asia/Ho_Chi_Minh',
    'UTC',
    'America/New_York',
    'Europe/London',
    'Asia/Tokyo',
    'Asia/Shanghai',
  ],
  SUPPORTED_DATE_FORMATS: [
    'DD/MM/YYYY',
    'MM/DD/YYYY',
    'YYYY-MM-DD',
    'DD-MM-YYYY',
  ],
} as const;

/**
 * Language configuration
 */
export const LANGUAGE_CONFIG = {
  DEFAULT_LANGUAGE: 'vi',
  SUPPORTED_LANGUAGES: [
    { code: 'vi', name: 'Tiếng Việt', flag: '🇻🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'ko', name: '한국어', flag: '🇰🇷' },
  ],
  RTL_LANGUAGES: ['ar', 'he', 'fa'],
} as const;

/**
 * Theme configuration
 */
export const THEME_CONFIG = {
  DEFAULT_THEME: 'light',
  AVAILABLE_THEMES: ['light', 'dark', 'system'],
  THEME_STORAGE_KEY: 'mega-ai-admin-theme',
  COLOR_SCHEMES: {
    light: {
      primary: '#6366f1',
      secondary: '#8b5cf6',
      accent: '#06b6d4',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
    },
    dark: {
      primary: '#818cf8',
      secondary: '#a78bfa',
      accent: '#22d3ee',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
    },
  },
} as const;

/**
 * File upload configuration
 */
export const FILE_UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  CHUNK_SIZE: 1024 * 1024, // 1MB chunks
  CONCURRENT_UPLOADS: 3,
  ALLOWED_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'video/mp4',
    'video/avi',
    'video/mov',
    'video/wmv',
    'audio/mp3',
    'audio/wav',
    'audio/aac',
  ],
  THUMBNAIL_SIZE: 200, // pixels
  PREVIEW_SIZE: 800, // pixels
} as const;

/**
 * Search configuration
 */
export const SEARCH_CONFIG = {
  MIN_SEARCH_LENGTH: 2,
  MAX_SEARCH_LENGTH: 100,
  DEBOUNCE_DELAY: 300, // ms
  MAX_RESULTS: 50,
  HIGHLIGHT_CLASS: 'bg-yellow-200',
  SEARCH_OPERATORS: ['AND', 'OR', 'NOT', '"exact phrase"'],
} as const;

/**
 * Notification configuration
 */
export const NOTIFICATION_CONFIG = {
  MAX_NOTIFICATIONS: 100,
  DEFAULT_TIMEOUT: 5000, // ms
  POSITION: 'top-right',
  ANIMATION_DURATION: 300, // ms
  TYPES: {
    info: { icon: 'info', color: 'blue' },
    success: { icon: 'check', color: 'green' },
    warning: { icon: 'warning', color: 'yellow' },
    error: { icon: 'x', color: 'red' },
  },
} as const;

/**
 * Analytics configuration
 */
export const ANALYTICS_CONFIG = {
  TRACKING_ENABLED: process.env.NODE_ENV === 'production',
  GOOGLE_ANALYTICS_ID: process.env.REACT_APP_GA_ID,
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
  BATCH_SIZE: 10,
  FLUSH_INTERVAL: 5000, // ms
  EVENTS: {
    PAGE_VIEW: 'page_view',
    USER_ACTION: 'user_action',
    ERROR: 'error',
    PERFORMANCE: 'performance',
  },
} as const;

/**
 * Security configuration
 */
export const SECURITY_CONFIG = {
  SESSION_STORAGE_KEY: 'mega-ai-admin-session',
  TOKEN_REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 minutes before expiry
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  PASSWORD_MIN_LENGTH: 8,
  PASSWORD_REQUIREMENTS: {
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: false,
  },
  CSRF_TOKEN_HEADER: 'X-CSRF-Token',
  CONTENT_SECURITY_POLICY: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'"],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'https:'],
  },
} as const;

/**
 * Performance configuration
 */
export const PERFORMANCE_CONFIG = {
  LAZY_LOADING_THRESHOLD: 100, // pixels from viewport
  VIRTUAL_SCROLLING_THRESHOLD: 100, // items
  IMAGE_OPTIMIZATION: {
    quality: 80,
    format: 'webp',
    sizes: [200, 400, 800, 1200],
  },
  BUNDLE_SPLITTING: {
    maxSize: 250000, // bytes
    minSize: 20000, // bytes
  },
  PREFETCH_DELAY: 2000, // ms
} as const;

/**
 * Development configuration
 */
export const DEV_CONFIG = {
  DEBUG_MODE: process.env.NODE_ENV === 'development',
  MOCK_API: process.env.REACT_APP_MOCK_API === 'true',
  LOG_LEVEL: process.env.REACT_APP_LOG_LEVEL || 'info',
  HOT_RELOAD: true,
  SOURCE_MAPS: true,
  REDUX_DEVTOOLS: process.env.NODE_ENV === 'development',
} as const;

/**
 * Feature flags
 */
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_NOTIFICATIONS: true,
  ENABLE_DARK_MODE: true,
  ENABLE_MULTI_LANGUAGE: true,
  ENABLE_REAL_TIME_UPDATES: true,
  ENABLE_OFFLINE_MODE: false,
  ENABLE_PWA: false,
  ENABLE_BETA_FEATURES: process.env.NODE_ENV === 'development',
  ENABLE_A_B_TESTING: false,
  ENABLE_ADVANCED_SEARCH: true,
  ENABLE_BULK_OPERATIONS: true,
  ENABLE_EXPORT_IMPORT: true,
} as const;

/**
 * Error handling configuration
 */
export const ERROR_CONFIG = {
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // ms
  EXPONENTIAL_BACKOFF: true,
  REPORT_ERRORS: process.env.NODE_ENV === 'production',
  ERROR_BOUNDARY_FALLBACK: true,
  NETWORK_ERROR_RETRY: true,
  TIMEOUT_RETRY: true,
  IGNORED_ERRORS: [
    'Network Error',
    'ChunkLoadError',
    'Loading chunk',
  ],
} as const;

/**
 * Monitoring configuration
 */
export const MONITORING_CONFIG = {
  HEALTH_CHECK_INTERVAL: 30000, // 30 seconds
  PERFORMANCE_MONITORING: true,
  ERROR_TRACKING: true,
  USER_TRACKING: false,
  METRICS_COLLECTION: true,
  LOG_RETENTION_DAYS: 30,
  ALERT_THRESHOLDS: {
    errorRate: 5, // percent
    responseTime: 2000, // ms
    memoryUsage: 80, // percent
    cpuUsage: 80, // percent
  },
} as const;

/**
 * Backup and recovery configuration
 */
export const BACKUP_CONFIG = {
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  LOCAL_STORAGE_BACKUP: true,
  SESSION_STORAGE_BACKUP: false,
  INDEXED_DB_BACKUP: true,
  BACKUP_RETENTION_DAYS: 7,
  COMPRESSION_ENABLED: true,
  ENCRYPTION_ENABLED: false,
} as const;

/**
 * Application metadata
 */
export const APP_METADATA = {
  NAME: 'Mega AI Admin',
  VERSION: '1.0.0',
  DESCRIPTION: 'Admin dashboard for Mega AI platform',
  AUTHOR: 'Mega AI Team',
  COPYRIGHT: '© 2024 Mega AI. All rights reserved.',
  SUPPORT_EMAIL: '<EMAIL>',
  DOCUMENTATION_URL: 'https://docs.megaai.com',
  GITHUB_URL: 'https://github.com/megaai/admin',
  BUILD_DATE: new Date().toISOString(),
  ENVIRONMENT: process.env.NODE_ENV || 'development',
} as const;

/**
 * Environment-specific configuration
 */
export const getEnvironmentConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  
  const configs = {
    development: {
      apiUrl: 'http://localhost:3001/api',
      enableLogging: true,
      enableMocking: true,
      enableDevTools: true,
    },
    staging: {
      apiUrl: 'https://staging-api.megaai.com/api',
      enableLogging: true,
      enableMocking: false,
      enableDevTools: false,
    },
    production: {
      apiUrl: 'https://api.megaai.com/api',
      enableLogging: false,
      enableMocking: false,
      enableDevTools: false,
    },
  };
  
  return configs[env as keyof typeof configs] || configs.development;
};
