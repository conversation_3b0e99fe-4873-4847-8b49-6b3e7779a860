/**
 * Content management constants and enums
 * Defines content types, processing status, quality levels, and content-related configurations
 */

/**
 * Content type enumeration
 */
export const CONTENT_TYPE = {
  VIDEO: 'video',
  PHOTO: 'photo',
} as const;

export type ContentType = typeof CONTENT_TYPE[keyof typeof CONTENT_TYPE];

/**
 * Content processing status
 */
export const PROCESSING_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  ERROR: 'error',
  CANCELLED: 'cancelled',
  QUEUED: 'queued',
  PAUSED: 'paused',
} as const;

export type ProcessingStatus = typeof PROCESSING_STATUS[keyof typeof PROCESSING_STATUS];

/**
 * Content quality levels
 */
export const QUALITY_LEVEL = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  ULTRA: 'ultra',
} as const;

export type QualityLevel = typeof QUALITY_LEVEL[keyof typeof QUALITY_LEVEL];

/**
 * Content visibility settings
 */
export const CONTENT_VISIBILITY = {
  PRIVATE: 'private',
  PUBLIC: 'public',
  UNLISTED: 'unlisted',
  SHARED: 'shared',
} as const;

export type ContentVisibility = typeof CONTENT_VISIBILITY[keyof typeof CONTENT_VISIBILITY];

/**
 * Content type labels (Vietnamese)
 */
export const CONTENT_TYPE_LABELS: Record<ContentType, string> = {
  [CONTENT_TYPE.VIDEO]: 'Video',
  [CONTENT_TYPE.PHOTO]: 'Ảnh',
};

/**
 * Processing status labels (Vietnamese)
 */
export const PROCESSING_STATUS_LABELS: Record<ProcessingStatus, string> = {
  [PROCESSING_STATUS.PENDING]: 'Đang chờ',
  [PROCESSING_STATUS.PROCESSING]: 'Đang xử lý',
  [PROCESSING_STATUS.COMPLETED]: 'Hoàn thành',
  [PROCESSING_STATUS.ERROR]: 'Lỗi',
  [PROCESSING_STATUS.CANCELLED]: 'Đã hủy',
  [PROCESSING_STATUS.QUEUED]: 'Trong hàng đợi',
  [PROCESSING_STATUS.PAUSED]: 'Tạm dừng',
};

/**
 * Quality level labels (Vietnamese)
 */
export const QUALITY_LEVEL_LABELS: Record<QualityLevel, string> = {
  [QUALITY_LEVEL.LOW]: 'Thấp',
  [QUALITY_LEVEL.MEDIUM]: 'Trung bình',
  [QUALITY_LEVEL.HIGH]: 'Cao',
  [QUALITY_LEVEL.ULTRA]: 'Siêu cao',
};

/**
 * Content visibility labels (Vietnamese)
 */
export const CONTENT_VISIBILITY_LABELS: Record<ContentVisibility, string> = {
  [CONTENT_VISIBILITY.PRIVATE]: 'Riêng tư',
  [CONTENT_VISIBILITY.PUBLIC]: 'Công khai',
  [CONTENT_VISIBILITY.UNLISTED]: 'Không liệt kê',
  [CONTENT_VISIBILITY.SHARED]: 'Chia sẻ',
};

/**
 * Processing status colors for UI
 */
export const PROCESSING_STATUS_COLORS: Record<ProcessingStatus, string> = {
  [PROCESSING_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800',
  [PROCESSING_STATUS.PROCESSING]: 'bg-blue-100 text-blue-800',
  [PROCESSING_STATUS.COMPLETED]: 'bg-green-100 text-green-800',
  [PROCESSING_STATUS.ERROR]: 'bg-red-100 text-red-800',
  [PROCESSING_STATUS.CANCELLED]: 'bg-gray-100 text-gray-800',
  [PROCESSING_STATUS.QUEUED]: 'bg-purple-100 text-purple-800',
  [PROCESSING_STATUS.PAUSED]: 'bg-orange-100 text-orange-800',
};

/**
 * Quality level colors for UI
 */
export const QUALITY_LEVEL_COLORS: Record<QualityLevel, string> = {
  [QUALITY_LEVEL.LOW]: 'bg-gray-100 text-gray-800',
  [QUALITY_LEVEL.MEDIUM]: 'bg-blue-100 text-blue-800',
  [QUALITY_LEVEL.HIGH]: 'bg-green-100 text-green-800',
  [QUALITY_LEVEL.ULTRA]: 'bg-purple-100 text-purple-800',
};

/**
 * Content visibility colors for UI
 */
export const CONTENT_VISIBILITY_COLORS: Record<ContentVisibility, string> = {
  [CONTENT_VISIBILITY.PRIVATE]: 'bg-red-100 text-red-800',
  [CONTENT_VISIBILITY.PUBLIC]: 'bg-green-100 text-green-800',
  [CONTENT_VISIBILITY.UNLISTED]: 'bg-yellow-100 text-yellow-800',
  [CONTENT_VISIBILITY.SHARED]: 'bg-blue-100 text-blue-800',
};

/**
 * Video quality specifications
 */
export const VIDEO_QUALITY_SPECS = {
  [QUALITY_LEVEL.LOW]: {
    maxResolution: '480p',
    maxBitrate: 1000, // kbps
    maxFrameRate: 24,
    creditMultiplier: 1,
  },
  [QUALITY_LEVEL.MEDIUM]: {
    maxResolution: '720p',
    maxBitrate: 2500, // kbps
    maxFrameRate: 30,
    creditMultiplier: 2,
  },
  [QUALITY_LEVEL.HIGH]: {
    maxResolution: '1080p',
    maxBitrate: 5000, // kbps
    maxFrameRate: 60,
    creditMultiplier: 4,
  },
  [QUALITY_LEVEL.ULTRA]: {
    maxResolution: '4K',
    maxBitrate: 15000, // kbps
    maxFrameRate: 60,
    creditMultiplier: 8,
  },
} as const;

/**
 * Photo quality specifications
 */
export const PHOTO_QUALITY_SPECS = {
  [QUALITY_LEVEL.LOW]: {
    maxResolution: '1024x768',
    maxFileSize: 1024 * 1024, // 1MB
    creditMultiplier: 1,
  },
  [QUALITY_LEVEL.MEDIUM]: {
    maxResolution: '1920x1080',
    maxFileSize: 3 * 1024 * 1024, // 3MB
    creditMultiplier: 2,
  },
  [QUALITY_LEVEL.HIGH]: {
    maxResolution: '2560x1440',
    maxFileSize: 8 * 1024 * 1024, // 8MB
    creditMultiplier: 4,
  },
  [QUALITY_LEVEL.ULTRA]: {
    maxResolution: '3840x2160',
    maxFileSize: 20 * 1024 * 1024, // 20MB
    creditMultiplier: 8,
  },
} as const;

/**
 * Supported file formats
 */
export const SUPPORTED_VIDEO_FORMATS = [
  'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'
] as const;

export const SUPPORTED_PHOTO_FORMATS = [
  'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg'
] as const;

export const SUPPORTED_AUDIO_FORMATS = [
  'mp3', 'wav', 'aac', 'ogg', 'flac', 'm4a'
] as const;

/**
 * File size limits (in bytes)
 */
export const FILE_SIZE_LIMITS = {
  VIDEO_MAX: 500 * 1024 * 1024, // 500MB
  PHOTO_MAX: 50 * 1024 * 1024,  // 50MB
  AUDIO_MAX: 100 * 1024 * 1024, // 100MB
  THUMBNAIL_MAX: 5 * 1024 * 1024, // 5MB
} as const;

/**
 * Processing timeouts (in seconds)
 */
export const PROCESSING_TIMEOUTS = {
  VIDEO_LOW: 300,    // 5 minutes
  VIDEO_MEDIUM: 600, // 10 minutes
  VIDEO_HIGH: 1200,  // 20 minutes
  VIDEO_ULTRA: 2400, // 40 minutes
  PHOTO_LOW: 60,     // 1 minute
  PHOTO_MEDIUM: 120, // 2 minutes
  PHOTO_HIGH: 300,   // 5 minutes
  PHOTO_ULTRA: 600,  // 10 minutes
} as const;

/**
 * Content categories/tags
 */
export const CONTENT_CATEGORIES = {
  ENTERTAINMENT: 'entertainment',
  EDUCATION: 'education',
  BUSINESS: 'business',
  TECHNOLOGY: 'technology',
  LIFESTYLE: 'lifestyle',
  TRAVEL: 'travel',
  FOOD: 'food',
  FASHION: 'fashion',
  SPORTS: 'sports',
  MUSIC: 'music',
  ART: 'art',
  OTHER: 'other',
} as const;

export type ContentCategory = typeof CONTENT_CATEGORIES[keyof typeof CONTENT_CATEGORIES];

/**
 * Content category labels (Vietnamese)
 */
export const CONTENT_CATEGORY_LABELS: Record<ContentCategory, string> = {
  [CONTENT_CATEGORIES.ENTERTAINMENT]: 'Giải trí',
  [CONTENT_CATEGORIES.EDUCATION]: 'Giáo dục',
  [CONTENT_CATEGORIES.BUSINESS]: 'Kinh doanh',
  [CONTENT_CATEGORIES.TECHNOLOGY]: 'Công nghệ',
  [CONTENT_CATEGORIES.LIFESTYLE]: 'Lối sống',
  [CONTENT_CATEGORIES.TRAVEL]: 'Du lịch',
  [CONTENT_CATEGORIES.FOOD]: 'Ẩm thực',
  [CONTENT_CATEGORIES.FASHION]: 'Thời trang',
  [CONTENT_CATEGORIES.SPORTS]: 'Thể thao',
  [CONTENT_CATEGORIES.MUSIC]: 'Âm nhạc',
  [CONTENT_CATEGORIES.ART]: 'Nghệ thuật',
  [CONTENT_CATEGORIES.OTHER]: 'Khác',
};

/**
 * Validation functions
 */
export const isValidContentType = (type: string): type is ContentType => {
  return Object.values(CONTENT_TYPE).includes(type as ContentType);
};

export const isValidProcessingStatus = (status: string): status is ProcessingStatus => {
  return Object.values(PROCESSING_STATUS).includes(status as ProcessingStatus);
};

export const isValidQualityLevel = (quality: string): quality is QualityLevel => {
  return Object.values(QUALITY_LEVEL).includes(quality as QualityLevel);
};

export const isValidContentVisibility = (visibility: string): visibility is ContentVisibility => {
  return Object.values(CONTENT_VISIBILITY).includes(visibility as ContentVisibility);
};

export const isValidContentCategory = (category: string): category is ContentCategory => {
  return Object.values(CONTENT_CATEGORIES).includes(category as ContentCategory);
};

/**
 * Get processing timeout for content
 */
export const getProcessingTimeout = (type: ContentType, quality: QualityLevel): number => {
  if (type === CONTENT_TYPE.VIDEO) {
    switch (quality) {
      case QUALITY_LEVEL.LOW: return PROCESSING_TIMEOUTS.VIDEO_LOW;
      case QUALITY_LEVEL.MEDIUM: return PROCESSING_TIMEOUTS.VIDEO_MEDIUM;
      case QUALITY_LEVEL.HIGH: return PROCESSING_TIMEOUTS.VIDEO_HIGH;
      case QUALITY_LEVEL.ULTRA: return PROCESSING_TIMEOUTS.VIDEO_ULTRA;
      default: return PROCESSING_TIMEOUTS.VIDEO_MEDIUM;
    }
  } else {
    switch (quality) {
      case QUALITY_LEVEL.LOW: return PROCESSING_TIMEOUTS.PHOTO_LOW;
      case QUALITY_LEVEL.MEDIUM: return PROCESSING_TIMEOUTS.PHOTO_MEDIUM;
      case QUALITY_LEVEL.HIGH: return PROCESSING_TIMEOUTS.PHOTO_HIGH;
      case QUALITY_LEVEL.ULTRA: return PROCESSING_TIMEOUTS.PHOTO_ULTRA;
      default: return PROCESSING_TIMEOUTS.PHOTO_MEDIUM;
    }
  }
};

/**
 * Get credit multiplier for quality level
 */
export const getCreditMultiplier = (type: ContentType, quality: QualityLevel): number => {
  if (type === CONTENT_TYPE.VIDEO) {
    return VIDEO_QUALITY_SPECS[quality].creditMultiplier;
  } else {
    return PHOTO_QUALITY_SPECS[quality].creditMultiplier;
  }
};

/**
 * Check if file format is supported
 */
export const isSupportedFormat = (type: ContentType, format: string): boolean => {
  const normalizedFormat = format.toLowerCase().replace('.', '');
  
  if (type === CONTENT_TYPE.VIDEO) {
    return SUPPORTED_VIDEO_FORMATS.includes(normalizedFormat as any);
  } else {
    return SUPPORTED_PHOTO_FORMATS.includes(normalizedFormat as any);
  }
};

/**
 * Get file size limit for content type
 */
export const getFileSizeLimit = (type: ContentType): number => {
  return type === CONTENT_TYPE.VIDEO ? FILE_SIZE_LIMITS.VIDEO_MAX : FILE_SIZE_LIMITS.PHOTO_MAX;
};
