/**
 * User status constants and enums
 * Defines user status types, roles, and permission levels
 */

/**
 * User status enumeration
 * Matches exactly with status values from existing UserCreditManagement component
 */
export const USER_STATUS = {
  ACTIVE: 'active',
  PREMIUM: 'premium',
  INACTIVE: 'inactive',
} as const;

export type UserStatus = typeof USER_STATUS[keyof typeof USER_STATUS];

/**
 * User role enumeration
 */
export const USER_ROLE = {
  USER: 'user',
  PREMIUM_USER: 'premium_user',
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
} as const;

export type UserRole = typeof USER_ROLE[keyof typeof USER_ROLE];

/**
 * Permission levels
 */
export const PERMISSION_LEVEL = {
  READ: 'read',
  WRITE: 'write',
  DELETE: 'delete',
  ADMIN: 'admin',
} as const;

export type PermissionLevel = typeof PERMISSION_LEVEL[keyof typeof PERMISSION_LEVEL];

/**
 * User status display labels (Vietnamese)
 */
export const USER_STATUS_LABELS: Record<UserStatus, string> = {
  [USER_STATUS.ACTIVE]: 'Hoạt động',
  [USER_STATUS.PREMIUM]: 'Premium',
  [USER_STATUS.INACTIVE]: 'Không hoạt động',
};

/**
 * User status colors for UI (Tailwind classes)
 */
export const USER_STATUS_COLORS: Record<UserStatus, string> = {
  [USER_STATUS.ACTIVE]: 'bg-green-100 text-green-800',
  [USER_STATUS.PREMIUM]: 'bg-purple-100 text-purple-800',
  [USER_STATUS.INACTIVE]: 'bg-gray-100 text-gray-800',
};

/**
 * User role display labels (Vietnamese)
 */
export const USER_ROLE_LABELS: Record<UserRole, string> = {
  [USER_ROLE.USER]: 'Người dùng',
  [USER_ROLE.PREMIUM_USER]: 'Người dùng Premium',
  [USER_ROLE.ADMIN]: 'Quản trị viên',
  [USER_ROLE.SUPER_ADMIN]: 'Quản trị viên cấp cao',
};

/**
 * Permission level labels (Vietnamese)
 */
export const PERMISSION_LEVEL_LABELS: Record<PermissionLevel, string> = {
  [PERMISSION_LEVEL.READ]: 'Đọc',
  [PERMISSION_LEVEL.WRITE]: 'Ghi',
  [PERMISSION_LEVEL.DELETE]: 'Xóa',
  [PERMISSION_LEVEL.ADMIN]: 'Quản trị',
};

/**
 * Default permissions for each user role
 */
export const DEFAULT_ROLE_PERMISSIONS: Record<UserRole, PermissionLevel[]> = {
  [USER_ROLE.USER]: [PERMISSION_LEVEL.READ],
  [USER_ROLE.PREMIUM_USER]: [PERMISSION_LEVEL.READ, PERMISSION_LEVEL.WRITE],
  [USER_ROLE.ADMIN]: [PERMISSION_LEVEL.READ, PERMISSION_LEVEL.WRITE, PERMISSION_LEVEL.DELETE],
  [USER_ROLE.SUPER_ADMIN]: [PERMISSION_LEVEL.READ, PERMISSION_LEVEL.WRITE, PERMISSION_LEVEL.DELETE, PERMISSION_LEVEL.ADMIN],
};

/**
 * User status validation
 */
export const isValidUserStatus = (status: string): status is UserStatus => {
  return Object.values(USER_STATUS).includes(status as UserStatus);
};

/**
 * User role validation
 */
export const isValidUserRole = (role: string): role is UserRole => {
  return Object.values(USER_ROLE).includes(role as UserRole);
};

/**
 * Permission level validation
 */
export const isValidPermissionLevel = (permission: string): permission is PermissionLevel => {
  return Object.values(PERMISSION_LEVEL).includes(permission as PermissionLevel);
};

/**
 * Check if user has specific permission
 */
export const hasPermission = (userRole: UserRole, requiredPermission: PermissionLevel): boolean => {
  const userPermissions = DEFAULT_ROLE_PERMISSIONS[userRole];
  return userPermissions.includes(requiredPermission);
};

/**
 * Get user status priority for sorting
 */
export const getUserStatusPriority = (status: UserStatus): number => {
  switch (status) {
    case USER_STATUS.PREMIUM:
      return 1;
    case USER_STATUS.ACTIVE:
      return 2;
    case USER_STATUS.INACTIVE:
      return 3;
    default:
      return 999;
  }
};

/**
 * Get user role priority for sorting
 */
export const getUserRolePriority = (role: UserRole): number => {
  switch (role) {
    case USER_ROLE.SUPER_ADMIN:
      return 1;
    case USER_ROLE.ADMIN:
      return 2;
    case USER_ROLE.PREMIUM_USER:
      return 3;
    case USER_ROLE.USER:
      return 4;
    default:
      return 999;
  }
};

/**
 * User activity status
 */
export const USER_ACTIVITY_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  AWAY: 'away',
  BUSY: 'busy',
} as const;

export type UserActivityStatus = typeof USER_ACTIVITY_STATUS[keyof typeof USER_ACTIVITY_STATUS];

/**
 * User activity status labels
 */
export const USER_ACTIVITY_STATUS_LABELS: Record<UserActivityStatus, string> = {
  [USER_ACTIVITY_STATUS.ONLINE]: 'Trực tuyến',
  [USER_ACTIVITY_STATUS.OFFLINE]: 'Ngoại tuyến',
  [USER_ACTIVITY_STATUS.AWAY]: 'Vắng mặt',
  [USER_ACTIVITY_STATUS.BUSY]: 'Bận',
};

/**
 * User activity status colors
 */
export const USER_ACTIVITY_STATUS_COLORS: Record<UserActivityStatus, string> = {
  [USER_ACTIVITY_STATUS.ONLINE]: 'bg-green-500',
  [USER_ACTIVITY_STATUS.OFFLINE]: 'bg-gray-400',
  [USER_ACTIVITY_STATUS.AWAY]: 'bg-yellow-500',
  [USER_ACTIVITY_STATUS.BUSY]: 'bg-red-500',
};

/**
 * User registration source
 */
export const USER_REGISTRATION_SOURCE = {
  WEB: 'web',
  MOBILE: 'mobile',
  API: 'api',
  ADMIN: 'admin',
  IMPORT: 'import',
} as const;

export type UserRegistrationSource = typeof USER_REGISTRATION_SOURCE[keyof typeof USER_REGISTRATION_SOURCE];

/**
 * User verification status
 */
export const USER_VERIFICATION_STATUS = {
  UNVERIFIED: 'unverified',
  EMAIL_VERIFIED: 'email_verified',
  PHONE_VERIFIED: 'phone_verified',
  FULLY_VERIFIED: 'fully_verified',
} as const;

export type UserVerificationStatus = typeof USER_VERIFICATION_STATUS[keyof typeof USER_VERIFICATION_STATUS];

/**
 * User verification status labels
 */
export const USER_VERIFICATION_STATUS_LABELS: Record<UserVerificationStatus, string> = {
  [USER_VERIFICATION_STATUS.UNVERIFIED]: 'Chưa xác thực',
  [USER_VERIFICATION_STATUS.EMAIL_VERIFIED]: 'Đã xác thực email',
  [USER_VERIFICATION_STATUS.PHONE_VERIFIED]: 'Đã xác thực số điện thoại',
  [USER_VERIFICATION_STATUS.FULLY_VERIFIED]: 'Đã xác thực đầy đủ',
};

/**
 * User account limits
 */
export const USER_LIMITS = {
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,
  MIN_USERNAME_LENGTH: 3,
  MAX_USERNAME_LENGTH: 50,
  MAX_NAME_LENGTH: 100,
  MAX_BIO_LENGTH: 500,
  MAX_PROFILE_PICTURE_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION_MINUTES: 30,
} as const;

/**
 * User preferences defaults
 */
export const DEFAULT_USER_PREFERENCES = {
  language: 'vi',
  timezone: 'Asia/Ho_Chi_Minh',
  dateFormat: 'DD/MM/YYYY',
  timeFormat: '24h',
  theme: 'light',
  emailNotifications: true,
  pushNotifications: true,
  smsNotifications: false,
} as const;
