/**
 * Interface Pages - Central Export
 * 
 * This module exports all page components for the Interface Layer.
 * Pages are container components that orchestrate features and layout.
 * 
 * Architecture Rules:
 * - Pages belong to Interface Layer
 * - Pages orchestrate features and handle routing
 * - Pages use data hooks for state management
 * - Pages compose layout and feature components
 */

// Re-export page components
export { default as Index } from './Index';
export { default as NotFound } from './NotFound';

// Export page types
export type * from './DashboardPage';
export type * from './UserManagementPage';
export type * from './AnalyticsPage';
export type * from './ContentPage';
export type * from './SettingsPage';
export type * from './NotFoundPage';

// ============================================================================
// PAGE UTILITIES
// ============================================================================

/**
 * Page utilities for common patterns
 */
export const PageUtils = {
  /**
   * Get page title for document.title
   */
  getPageTitle: (pageName: string, appName: string = 'Mega AI Admin'): string => {
    return `${pageName} | ${appName}`;
  },

  /**
   * Get page metadata for SEO
   */
  getPageMeta: (page: {
    title: string;
    description?: string;
    keywords?: string[];
  }) => ({
    title: PageUtils.getPageTitle(page.title),
    description: page.description || 'Mega AI Admin - Platform quản lý AI',
    keywords: page.keywords?.join(', ') || 'AI, admin, management, video, photo',
  }),
} as const;

// ============================================================================
// PAGE METADATA
// ============================================================================

/**
 * Page layer metadata for debugging and documentation
 */
export const PAGE_LAYER_INFO = {
  version: '1.0.0',
  description: 'Interface Layer Pages - Container components for routing',
  architecture: '3-layer MVI',
  responsibilities: [
    'Route handling and navigation',
    'Feature orchestration',
    'Layout composition',
    'Data hook integration',
    'SEO and metadata management'
  ],
  dependencies: ['React Router', 'Data Hooks', 'Feature Components', 'Layout Components'],
} as const;
