/**
 * Permission Utilities
 * 
 * This module contains utilities for handling user permissions and access control in the UI.
 * According to our 3-layer MVI architecture, permission utilities belong in Interface Layer utils.
 * 
 * Architecture Rules:
 * - Permission utilities belong in Interface Layer utils
 * - Handle UI-level permission checks and access control
 * - Work with permission data from Model Layer
 * - Provide utilities for conditional rendering and access
 */

// ============================================================================
// PERMISSION TYPES
// ============================================================================

/**
 * Permission-related types for UI
 */
export interface UserPermissions {
  // User management
  canViewUsers: boolean;
  canCreateUsers: boolean;
  canEditUsers: boolean;
  canDeleteUsers: boolean;
  canManageUserCredits: boolean;

  // Content management
  canViewContent: boolean;
  canCreateContent: boolean;
  canEditContent: boolean;
  canDeleteContent: boolean;
  canProcessContent: boolean;

  // Analytics
  canViewAnalytics: boolean;
  canViewDetailedAnalytics: boolean;
  canExportAnalytics: boolean;

  // System management
  canViewSystemSettings: boolean;
  canEditSystemSettings: boolean;
  canViewAuditLogs: boolean;
  canManageNotifications: boolean;

  // Admin functions
  canAccessAdminPanel: boolean;
  canManagePermissions: boolean;
  canViewSystemStatus: boolean;
}

export interface PermissionContext {
  user: {
    id: number;
    role: string;
    permissions: string[];
  };
  resource?: {
    type: string;
    id?: number;
    ownerId?: number;
  };
}

// ============================================================================
// PERMISSION CONSTANTS
// ============================================================================

/**
 * Permission constants
 */
export const PERMISSIONS = {
  // User permissions
  USERS: {
    VIEW: 'users:view',
    CREATE: 'users:create',
    EDIT: 'users:edit',
    DELETE: 'users:delete',
    MANAGE_CREDITS: 'users:manage_credits',
  },

  // Content permissions
  CONTENT: {
    VIEW: 'content:view',
    CREATE: 'content:create',
    EDIT: 'content:edit',
    DELETE: 'content:delete',
    PROCESS: 'content:process',
  },

  // Analytics permissions
  ANALYTICS: {
    VIEW: 'analytics:view',
    VIEW_DETAILED: 'analytics:view_detailed',
    EXPORT: 'analytics:export',
  },

  // System permissions
  SYSTEM: {
    VIEW_SETTINGS: 'system:view_settings',
    EDIT_SETTINGS: 'system:edit_settings',
    VIEW_AUDIT_LOGS: 'system:view_audit_logs',
    MANAGE_NOTIFICATIONS: 'system:manage_notifications',
  },

  // Admin permissions
  ADMIN: {
    ACCESS_PANEL: 'admin:access_panel',
    MANAGE_PERMISSIONS: 'admin:manage_permissions',
    VIEW_SYSTEM_STATUS: 'admin:view_system_status',
  },
} as const;

/**
 * Role-based permission mappings
 */
export const ROLE_PERMISSIONS = {
  admin: [
    // All permissions
    ...Object.values(PERMISSIONS.USERS),
    ...Object.values(PERMISSIONS.CONTENT),
    ...Object.values(PERMISSIONS.ANALYTICS),
    ...Object.values(PERMISSIONS.SYSTEM),
    ...Object.values(PERMISSIONS.ADMIN),
  ],
  
  manager: [
    // User management
    PERMISSIONS.USERS.VIEW,
    PERMISSIONS.USERS.CREATE,
    PERMISSIONS.USERS.EDIT,
    PERMISSIONS.USERS.MANAGE_CREDITS,
    
    // Content management
    ...Object.values(PERMISSIONS.CONTENT),
    
    // Analytics
    PERMISSIONS.ANALYTICS.VIEW,
    PERMISSIONS.ANALYTICS.VIEW_DETAILED,
    PERMISSIONS.ANALYTICS.EXPORT,
    
    // Limited system access
    PERMISSIONS.SYSTEM.VIEW_SETTINGS,
    PERMISSIONS.SYSTEM.MANAGE_NOTIFICATIONS,
  ],
  
  operator: [
    // Limited user management
    PERMISSIONS.USERS.VIEW,
    PERMISSIONS.USERS.EDIT,
    PERMISSIONS.USERS.MANAGE_CREDITS,
    
    // Content management
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.CONTENT.CREATE,
    PERMISSIONS.CONTENT.EDIT,
    PERMISSIONS.CONTENT.PROCESS,
    
    // Basic analytics
    PERMISSIONS.ANALYTICS.VIEW,
  ],
  
  viewer: [
    // Read-only access
    PERMISSIONS.USERS.VIEW,
    PERMISSIONS.CONTENT.VIEW,
    PERMISSIONS.ANALYTICS.VIEW,
  ],
} as const;

// ============================================================================
// PERMISSION UTILITIES
// ============================================================================

/**
 * Permission checking utilities
 */
export const PermissionUtils = {
  /**
   * Check if user has a specific permission
   */
  hasPermission: (context: PermissionContext, permission: string): boolean => {
    const { user } = context;
    
    // Admin has all permissions
    if (user.role === 'admin') {
      return true;
    }
    
    // Check explicit permissions
    if (user.permissions.includes(permission)) {
      return true;
    }
    
    // Check role-based permissions
    const rolePermissions = ROLE_PERMISSIONS[user.role as keyof typeof ROLE_PERMISSIONS] || [];
    return rolePermissions.includes(permission);
  },

  /**
   * Check if user has any of the specified permissions
   */
  hasAnyPermission: (context: PermissionContext, permissions: string[]): boolean => {
    return permissions.some(permission => PermissionUtils.hasPermission(context, permission));
  },

  /**
   * Check if user has all of the specified permissions
   */
  hasAllPermissions: (context: PermissionContext, permissions: string[]): boolean => {
    return permissions.every(permission => PermissionUtils.hasPermission(context, permission));
  },

  /**
   * Check if user can access a specific resource
   */
  canAccessResource: (context: PermissionContext, permission: string): boolean => {
    const { user, resource } = context;
    
    // Check basic permission first
    if (!PermissionUtils.hasPermission(context, permission)) {
      return false;
    }
    
    // If no resource specified, basic permission is enough
    if (!resource) {
      return true;
    }
    
    // Check resource ownership for non-admin users
    if (user.role !== 'admin' && resource.ownerId && resource.ownerId !== user.id) {
      // Check if user has permission to access others' resources
      const adminPermissions = [
        PERMISSIONS.ADMIN.ACCESS_PANEL,
        PERMISSIONS.SYSTEM.VIEW_SETTINGS,
      ];
      
      return PermissionUtils.hasAnyPermission(context, adminPermissions);
    }
    
    return true;
  },

  /**
   * Get user permissions object
   */
  getUserPermissions: (context: PermissionContext): UserPermissions => {
    return {
      // User management
      canViewUsers: PermissionUtils.hasPermission(context, PERMISSIONS.USERS.VIEW),
      canCreateUsers: PermissionUtils.hasPermission(context, PERMISSIONS.USERS.CREATE),
      canEditUsers: PermissionUtils.hasPermission(context, PERMISSIONS.USERS.EDIT),
      canDeleteUsers: PermissionUtils.hasPermission(context, PERMISSIONS.USERS.DELETE),
      canManageUserCredits: PermissionUtils.hasPermission(context, PERMISSIONS.USERS.MANAGE_CREDITS),

      // Content management
      canViewContent: PermissionUtils.hasPermission(context, PERMISSIONS.CONTENT.VIEW),
      canCreateContent: PermissionUtils.hasPermission(context, PERMISSIONS.CONTENT.CREATE),
      canEditContent: PermissionUtils.hasPermission(context, PERMISSIONS.CONTENT.EDIT),
      canDeleteContent: PermissionUtils.hasPermission(context, PERMISSIONS.CONTENT.DELETE),
      canProcessContent: PermissionUtils.hasPermission(context, PERMISSIONS.CONTENT.PROCESS),

      // Analytics
      canViewAnalytics: PermissionUtils.hasPermission(context, PERMISSIONS.ANALYTICS.VIEW),
      canViewDetailedAnalytics: PermissionUtils.hasPermission(context, PERMISSIONS.ANALYTICS.VIEW_DETAILED),
      canExportAnalytics: PermissionUtils.hasPermission(context, PERMISSIONS.ANALYTICS.EXPORT),

      // System management
      canViewSystemSettings: PermissionUtils.hasPermission(context, PERMISSIONS.SYSTEM.VIEW_SETTINGS),
      canEditSystemSettings: PermissionUtils.hasPermission(context, PERMISSIONS.SYSTEM.EDIT_SETTINGS),
      canViewAuditLogs: PermissionUtils.hasPermission(context, PERMISSIONS.SYSTEM.VIEW_AUDIT_LOGS),
      canManageNotifications: PermissionUtils.hasPermission(context, PERMISSIONS.SYSTEM.MANAGE_NOTIFICATIONS),

      // Admin functions
      canAccessAdminPanel: PermissionUtils.hasPermission(context, PERMISSIONS.ADMIN.ACCESS_PANEL),
      canManagePermissions: PermissionUtils.hasPermission(context, PERMISSIONS.ADMIN.MANAGE_PERMISSIONS),
      canViewSystemStatus: PermissionUtils.hasPermission(context, PERMISSIONS.ADMIN.VIEW_SYSTEM_STATUS),
    };
  },

  /**
   * Filter menu items based on permissions
   */
  filterMenuItems: <T extends { permission?: string; permissions?: string[] }>(
    context: PermissionContext,
    items: T[]
  ): T[] => {
    return items.filter(item => {
      if (item.permission) {
        return PermissionUtils.hasPermission(context, item.permission);
      }
      if (item.permissions) {
        return PermissionUtils.hasAnyPermission(context, item.permissions);
      }
      return true; // No permission required
    });
  },

  /**
   * Check if user can perform action on resource
   */
  canPerformAction: (
    context: PermissionContext,
    action: 'view' | 'create' | 'edit' | 'delete',
    resourceType: 'users' | 'content' | 'analytics' | 'system'
  ): boolean => {
    const permissionMap = {
      users: {
        view: PERMISSIONS.USERS.VIEW,
        create: PERMISSIONS.USERS.CREATE,
        edit: PERMISSIONS.USERS.EDIT,
        delete: PERMISSIONS.USERS.DELETE,
      },
      content: {
        view: PERMISSIONS.CONTENT.VIEW,
        create: PERMISSIONS.CONTENT.CREATE,
        edit: PERMISSIONS.CONTENT.EDIT,
        delete: PERMISSIONS.CONTENT.DELETE,
      },
      analytics: {
        view: PERMISSIONS.ANALYTICS.VIEW,
        create: '', // Not applicable
        edit: '', // Not applicable
        delete: '', // Not applicable
      },
      system: {
        view: PERMISSIONS.SYSTEM.VIEW_SETTINGS,
        create: '', // Not applicable
        edit: PERMISSIONS.SYSTEM.EDIT_SETTINGS,
        delete: '', // Not applicable
      },
    };

    const permission = permissionMap[resourceType]?.[action];
    if (!permission) {
      return false;
    }

    return PermissionUtils.canAccessResource(context, permission);
  },
} as const;

// ============================================================================
// PERMISSION HOOKS UTILITIES
// ============================================================================

/**
 * Utilities for permission-based conditional rendering
 */
export const PermissionRenderUtils = {
  /**
   * Create a permission-based component wrapper
   */
  createPermissionWrapper: (
    context: PermissionContext,
    permission: string | string[],
    fallback?: React.ReactNode
  ) => {
    return (children: React.ReactNode): React.ReactNode => {
      const hasAccess = Array.isArray(permission)
        ? PermissionUtils.hasAnyPermission(context, permission)
        : PermissionUtils.hasPermission(context, permission);

      return hasAccess ? children : (fallback || null);
    };
  },

  /**
   * Get disabled state based on permissions
   */
  getDisabledState: (context: PermissionContext, permission: string): boolean => {
    return !PermissionUtils.hasPermission(context, permission);
  },

  /**
   * Get visibility state based on permissions
   */
  getVisibilityState: (context: PermissionContext, permission: string): boolean => {
    return PermissionUtils.hasPermission(context, permission);
  },
} as const;
