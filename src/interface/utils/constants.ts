/**
 * Interface Layer Constants
 * 
 * This module contains UI-specific constants for the Interface Layer.
 * According to our 3-layer MVI architecture, UI constants belong in Interface Layer utils.
 * 
 * Architecture Rules:
 * - UI constants belong in Interface Layer utils
 * - NOT in Model Layer (which contains only business constants)
 * - Handle UI-specific values like colors, sizes, animations
 * - Support theming and responsive design
 */

// ============================================================================
// COLOR CONSTANTS
// ============================================================================

/**
 * UI color palette
 */
export const UI_COLORS = {
  // Status colors
  status: {
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    neutral: '#6b7280',
  },

  // User status colors
  userStatus: {
    active: '#10b981',
    premium: '#3b82f6',
    inactive: '#6b7280',
  },

  // Content status colors
  contentStatus: {
    pending: '#f59e0b',
    processing: '#3b82f6',
    completed: '#10b981',
    error: '#ef4444',
    cancelled: '#6b7280',
  },

  // Priority colors
  priority: {
    low: '#6b7280',
    medium: '#f59e0b',
    high: '#f97316',
    urgent: '#ef4444',
  },

  // Chart colors
  chart: {
    primary: '#3b82f6',
    secondary: '#10b981',
    tertiary: '#f59e0b',
    quaternary: '#ef4444',
    gradient: {
      blue: ['#3b82f6', '#1d4ed8'],
      green: ['#10b981', '#047857'],
      yellow: ['#f59e0b', '#d97706'],
      red: ['#ef4444', '#dc2626'],
      purple: ['#8b5cf6', '#7c3aed'],
    },
  },
} as const;

// ============================================================================
// SIZE CONSTANTS
// ============================================================================

/**
 * UI size constants
 */
export const UI_SIZES = {
  // Spacing
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  },

  // Border radius
  radius: {
    none: '0',
    sm: '0.125rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px',
  },

  // Font sizes
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
    '4xl': '2.25rem',
  },

  // Component sizes
  component: {
    button: {
      sm: { height: '2rem', padding: '0.5rem 0.75rem' },
      md: { height: '2.5rem', padding: '0.625rem 1rem' },
      lg: { height: '3rem', padding: '0.75rem 1.25rem' },
    },
    input: {
      sm: { height: '2rem', padding: '0.25rem 0.5rem' },
      md: { height: '2.5rem', padding: '0.5rem 0.75rem' },
      lg: { height: '3rem', padding: '0.75rem 1rem' },
    },
    avatar: {
      sm: '2rem',
      md: '2.5rem',
      lg: '3rem',
      xl: '4rem',
    },
  },
} as const;

// ============================================================================
// ANIMATION CONSTANTS
// ============================================================================

/**
 * Animation and transition constants
 */
export const UI_ANIMATIONS = {
  // Duration
  duration: {
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
  },

  // Easing
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },

  // Common transitions
  transition: {
    all: 'all 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    colors: 'color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1)',
    transform: 'transform 300ms cubic-bezier(0.4, 0, 0.2, 1)',
    opacity: 'opacity 150ms cubic-bezier(0.4, 0, 0.2, 1)',
  },

  // Keyframes
  keyframes: {
    fadeIn: {
      from: { opacity: 0 },
      to: { opacity: 1 },
    },
    slideIn: {
      from: { transform: 'translateX(-100%)' },
      to: { transform: 'translateX(0)' },
    },
    bounce: {
      '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
      '40%, 43%': { transform: 'translate3d(0, -30px, 0)' },
      '70%': { transform: 'translate3d(0, -15px, 0)' },
      '90%': { transform: 'translate3d(0, -4px, 0)' },
    },
  },
} as const;

// ============================================================================
// BREAKPOINT CONSTANTS
// ============================================================================

/**
 * Responsive breakpoints
 */
export const UI_BREAKPOINTS = {
  // Screen sizes
  screens: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },

  // Media queries
  mediaQueries: {
    mobile: '(max-width: 767px)',
    tablet: '(min-width: 768px) and (max-width: 1023px)',
    desktop: '(min-width: 1024px)',
    largeDesktop: '(min-width: 1280px)',
  },

  // Container max widths
  container: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
} as const;

// ============================================================================
// LAYOUT CONSTANTS
// ============================================================================

/**
 * Layout constants
 */
export const UI_LAYOUT = {
  // Header
  header: {
    height: '4rem',
    zIndex: 50,
  },

  // Sidebar
  sidebar: {
    width: {
      collapsed: '4rem',
      expanded: '16rem',
    },
    zIndex: 40,
  },

  // Main content
  main: {
    padding: '1.5rem',
    maxWidth: '1200px',
  },

  // Modal
  modal: {
    zIndex: 100,
    backdrop: 'rgba(0, 0, 0, 0.5)',
  },

  // Toast
  toast: {
    zIndex: 200,
    position: {
      top: '1rem',
      right: '1rem',
    },
  },
} as const;

// ============================================================================
// FORM CONSTANTS
// ============================================================================

/**
 * Form-related constants
 */
export const UI_FORMS = {
  // Validation
  validation: {
    debounceMs: 300,
    showErrorDelay: 150,
  },

  // Input limits
  limits: {
    searchMinLength: 1,
    searchMaxLength: 100,
    nameMaxLength: 100,
    descriptionMaxLength: 500,
    emailMaxLength: 254,
    phoneMaxLength: 15,
  },

  // File upload
  fileUpload: {
    maxSize: {
      image: 10 * 1024 * 1024, // 10MB
      video: 100 * 1024 * 1024, // 100MB
      document: 5 * 1024 * 1024, // 5MB
    },
    acceptedTypes: {
      image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      video: ['video/mp4', 'video/webm', 'video/ogg'],
      document: ['application/pdf', 'text/plain', 'application/msword'],
    },
  },
} as const;

// ============================================================================
// TABLE CONSTANTS
// ============================================================================

/**
 * Table and data display constants
 */
export const UI_TABLES = {
  // Pagination
  pagination: {
    defaultPageSize: 10,
    pageSizeOptions: [10, 25, 50, 100],
    maxVisiblePages: 7,
  },

  // Sorting
  sorting: {
    defaultOrder: 'desc' as const,
    indicators: {
      asc: '↑',
      desc: '↓',
      none: '↕',
    },
  },

  // Row heights
  rowHeight: {
    compact: '2.5rem',
    normal: '3rem',
    comfortable: '3.5rem',
  },
} as const;

// ============================================================================
// NOTIFICATION CONSTANTS
// ============================================================================

/**
 * Notification and toast constants
 */
export const UI_NOTIFICATIONS = {
  // Duration
  duration: {
    success: 3000,
    info: 4000,
    warning: 5000,
    error: 6000,
  },

  // Position
  position: {
    topRight: 'top-right',
    topLeft: 'top-left',
    bottomRight: 'bottom-right',
    bottomLeft: 'bottom-left',
    topCenter: 'top-center',
    bottomCenter: 'bottom-center',
  },

  // Max notifications
  maxVisible: 5,
} as const;

// ============================================================================
// COMBINED CONSTANTS
// ============================================================================

/**
 * All UI constants combined for easy access
 */
export const UI_CONSTANTS = {
  COLORS: UI_COLORS,
  SIZES: UI_SIZES,
  ANIMATIONS: UI_ANIMATIONS,
  BREAKPOINTS: UI_BREAKPOINTS,
  LAYOUT: UI_LAYOUT,
  FORMS: UI_FORMS,
  TABLES: UI_TABLES,
  NOTIFICATIONS: UI_NOTIFICATIONS,
} as const;
