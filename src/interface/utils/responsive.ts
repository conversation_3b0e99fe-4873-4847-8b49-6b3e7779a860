/**
 * Responsive Utilities
 * 
 * This module contains utilities for responsive design and device detection.
 * According to our 3-layer MVI architecture, responsive utilities belong in Interface Layer utils.
 * 
 * Architecture Rules:
 * - Responsive utilities belong in Interface Layer utils
 * - Handle device detection and responsive behavior
 * - Provide utilities for conditional rendering based on screen size
 * - Support responsive design patterns
 */

import { UI_BREAKPOINTS } from './constants';

// ============================================================================
// RESPONSIVE TYPES
// ============================================================================

/**
 * Responsive-related types
 */
export interface ScreenSize {
  width: number;
  height: number;
}

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  screenSize: ScreenSize;
  orientation: 'portrait' | 'landscape';
}

export interface ResponsiveValue<T> {
  mobile?: T;
  tablet?: T;
  desktop?: T;
  largeDesktop?: T;
  default: T;
}

// ============================================================================
// DEVICE DETECTION UTILITIES
// ============================================================================

/**
 * Device detection utilities
 */
export const DeviceUtils = {
  /**
   * Get current screen size
   */
  getScreenSize: (): ScreenSize => {
    if (typeof window === 'undefined') {
      return { width: 1024, height: 768 }; // Default for SSR
    }
    
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  },

  /**
   * Check if current screen is mobile
   */
  isMobile: (): boolean => {
    const { width } = DeviceUtils.getScreenSize();
    return width < parseInt(UI_BREAKPOINTS.screens.md);
  },

  /**
   * Check if current screen is tablet
   */
  isTablet: (): boolean => {
    const { width } = DeviceUtils.getScreenSize();
    return width >= parseInt(UI_BREAKPOINTS.screens.md) && 
           width < parseInt(UI_BREAKPOINTS.screens.lg);
  },

  /**
   * Check if current screen is desktop
   */
  isDesktop: (): boolean => {
    const { width } = DeviceUtils.getScreenSize();
    return width >= parseInt(UI_BREAKPOINTS.screens.lg) && 
           width < parseInt(UI_BREAKPOINTS.screens.xl);
  },

  /**
   * Check if current screen is large desktop
   */
  isLargeDesktop: (): boolean => {
    const { width } = DeviceUtils.getScreenSize();
    return width >= parseInt(UI_BREAKPOINTS.screens.xl);
  },

  /**
   * Get device orientation
   */
  getOrientation: (): 'portrait' | 'landscape' => {
    const { width, height } = DeviceUtils.getScreenSize();
    return width > height ? 'landscape' : 'portrait';
  },

  /**
   * Get comprehensive device info
   */
  getDeviceInfo: (): DeviceInfo => {
    const screenSize = DeviceUtils.getScreenSize();
    
    return {
      isMobile: DeviceUtils.isMobile(),
      isTablet: DeviceUtils.isTablet(),
      isDesktop: DeviceUtils.isDesktop(),
      isLargeDesktop: DeviceUtils.isLargeDesktop(),
      screenSize,
      orientation: DeviceUtils.getOrientation(),
    };
  },

  /**
   * Check if device supports touch
   */
  isTouchDevice: (): boolean => {
    if (typeof window === 'undefined') return false;
    
    return 'ontouchstart' in window || 
           navigator.maxTouchPoints > 0 || 
           (navigator as any).msMaxTouchPoints > 0;
  },

  /**
   * Check if device is in standalone mode (PWA)
   */
  isStandalone: (): boolean => {
    if (typeof window === 'undefined') return false;
    
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true;
  },
} as const;

// ============================================================================
// RESPONSIVE VALUE UTILITIES
// ============================================================================

/**
 * Responsive value utilities
 */
export const ResponsiveUtils = {
  /**
   * Get value based on current screen size
   */
  getValue: <T>(responsiveValue: ResponsiveValue<T>): T => {
    const deviceInfo = DeviceUtils.getDeviceInfo();
    
    if (deviceInfo.isLargeDesktop && responsiveValue.largeDesktop !== undefined) {
      return responsiveValue.largeDesktop;
    }
    
    if (deviceInfo.isDesktop && responsiveValue.desktop !== undefined) {
      return responsiveValue.desktop;
    }
    
    if (deviceInfo.isTablet && responsiveValue.tablet !== undefined) {
      return responsiveValue.tablet;
    }
    
    if (deviceInfo.isMobile && responsiveValue.mobile !== undefined) {
      return responsiveValue.mobile;
    }
    
    return responsiveValue.default;
  },

  /**
   * Create responsive value object
   */
  createResponsiveValue: <T>(
    defaultValue: T,
    overrides?: Partial<Omit<ResponsiveValue<T>, 'default'>>
  ): ResponsiveValue<T> => {
    return {
      default: defaultValue,
      ...overrides,
    };
  },

  /**
   * Get responsive class names
   */
  getResponsiveClasses: (
    baseClass: string,
    responsiveClasses: Partial<{
      mobile: string;
      tablet: string;
      desktop: string;
      largeDesktop: string;
    }>
  ): string => {
    const classes = [baseClass];
    
    if (responsiveClasses.mobile) {
      classes.push(`sm:${responsiveClasses.mobile}`);
    }
    
    if (responsiveClasses.tablet) {
      classes.push(`md:${responsiveClasses.tablet}`);
    }
    
    if (responsiveClasses.desktop) {
      classes.push(`lg:${responsiveClasses.desktop}`);
    }
    
    if (responsiveClasses.largeDesktop) {
      classes.push(`xl:${responsiveClasses.largeDesktop}`);
    }
    
    return classes.join(' ');
  },

  /**
   * Get responsive grid columns
   */
  getResponsiveColumns: (
    columns: ResponsiveValue<number>
  ): string => {
    const currentColumns = ResponsiveUtils.getValue(columns);
    return `grid-cols-${currentColumns}`;
  },

  /**
   * Get responsive spacing
   */
  getResponsiveSpacing: (
    spacing: ResponsiveValue<keyof typeof UI_BREAKPOINTS.screens>
  ): string => {
    const currentSpacing = ResponsiveUtils.getValue(spacing);
    return `p-${currentSpacing}`;
  },
} as const;

// ============================================================================
// MEDIA QUERY UTILITIES
// ============================================================================

/**
 * Media query utilities
 */
export const MediaQueryUtils = {
  /**
   * Create media query string
   */
  createMediaQuery: (
    minWidth?: string,
    maxWidth?: string,
    orientation?: 'portrait' | 'landscape'
  ): string => {
    const conditions: string[] = [];
    
    if (minWidth) {
      conditions.push(`(min-width: ${minWidth})`);
    }
    
    if (maxWidth) {
      conditions.push(`(max-width: ${maxWidth})`);
    }
    
    if (orientation) {
      conditions.push(`(orientation: ${orientation})`);
    }
    
    return conditions.join(' and ');
  },

  /**
   * Check if media query matches
   */
  matches: (mediaQuery: string): boolean => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia(mediaQuery).matches;
  },

  /**
   * Get predefined media queries
   */
  getPredefinedQueries: () => ({
    mobile: UI_BREAKPOINTS.mediaQueries.mobile,
    tablet: UI_BREAKPOINTS.mediaQueries.tablet,
    desktop: UI_BREAKPOINTS.mediaQueries.desktop,
    largeDesktop: UI_BREAKPOINTS.mediaQueries.largeDesktop,
    portrait: '(orientation: portrait)',
    landscape: '(orientation: landscape)',
    touch: '(hover: none) and (pointer: coarse)',
    mouse: '(hover: hover) and (pointer: fine)',
    reducedMotion: '(prefers-reduced-motion: reduce)',
    darkMode: '(prefers-color-scheme: dark)',
  }),

  /**
   * Listen to media query changes
   */
  addListener: (
    mediaQuery: string,
    callback: (matches: boolean) => void
  ): (() => void) => {
    if (typeof window === 'undefined') {
      return () => {}; // No-op for SSR
    }
    
    const mediaQueryList = window.matchMedia(mediaQuery);
    const handler = (event: MediaQueryListEvent) => {
      callback(event.matches);
    };
    
    mediaQueryList.addListener(handler);
    
    // Return cleanup function
    return () => {
      mediaQueryList.removeListener(handler);
    };
  },
} as const;

// ============================================================================
// RESPONSIVE LAYOUT UTILITIES
// ============================================================================

/**
 * Responsive layout utilities
 */
export const LayoutUtils = {
  /**
   * Get responsive container width
   */
  getContainerWidth: (): string => {
    const deviceInfo = DeviceUtils.getDeviceInfo();
    
    if (deviceInfo.isLargeDesktop) {
      return UI_BREAKPOINTS.container['2xl'];
    }
    
    if (deviceInfo.isDesktop) {
      return UI_BREAKPOINTS.container.xl;
    }
    
    if (deviceInfo.isTablet) {
      return UI_BREAKPOINTS.container.lg;
    }
    
    return '100%';
  },

  /**
   * Get responsive sidebar behavior
   */
  getSidebarBehavior: (): 'overlay' | 'push' | 'static' => {
    const deviceInfo = DeviceUtils.getDeviceInfo();
    
    if (deviceInfo.isMobile) {
      return 'overlay';
    }
    
    if (deviceInfo.isTablet) {
      return 'push';
    }
    
    return 'static';
  },

  /**
   * Get responsive table behavior
   */
  getTableBehavior: (): 'scroll' | 'stack' | 'normal' => {
    const deviceInfo = DeviceUtils.getDeviceInfo();
    
    if (deviceInfo.isMobile) {
      return 'stack';
    }
    
    if (deviceInfo.isTablet) {
      return 'scroll';
    }
    
    return 'normal';
  },

  /**
   * Get responsive modal behavior
   */
  getModalBehavior: (): 'fullscreen' | 'centered' | 'drawer' => {
    const deviceInfo = DeviceUtils.getDeviceInfo();
    
    if (deviceInfo.isMobile) {
      return 'fullscreen';
    }
    
    return 'centered';
  },

  /**
   * Calculate responsive font size
   */
  getResponsiveFontSize: (
    baseFontSize: number,
    scaleFactor: number = 0.8
  ): string => {
    const deviceInfo = DeviceUtils.getDeviceInfo();
    
    if (deviceInfo.isMobile) {
      return `${baseFontSize * scaleFactor}rem`;
    }
    
    return `${baseFontSize}rem`;
  },
} as const;

// ============================================================================
// COMBINED RESPONSIVE UTILITIES
// ============================================================================

/**
 * All responsive utilities combined for easy access
 */
export const Responsive = {
  Device: DeviceUtils,
  Values: ResponsiveUtils,
  MediaQuery: MediaQueryUtils,
  Layout: LayoutUtils,
} as const;
