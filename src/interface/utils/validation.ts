/**
 * Validation Utilities and Zod Schemas
 * 
 * This module contains all validation schemas and utilities for the Interface Layer.
 * According to our 3-layer MVI architecture, validation schemas belong in Interface Layer utils.
 * 
 * Architecture Rules:
 * - Validation schemas (Zod) belong in Interface Layer utils
 * - NOT in Model Layer (which contains only pure types)
 * - NOT in Data Layer (which handles data access)
 */

import { z } from 'zod';
import type {
  User,
  CreateUserPayload,
  UpdateUserPayload,
  CreditOperationPayload,
  CreateContentPayload,
  UpdateContentPayload,
  CreateNotificationPayload,
} from '@/models';

// ============================================================================
// COMMON VALIDATION SCHEMAS
// ============================================================================

/**
 * Common field validations
 */
export const CommonValidation = {
  email: z.string().email('Email không hợp lệ'),
  phone: z.string().regex(/^(\+84|0)[0-9]{9,10}$/, 'Số điện thoại không hợp lệ'),
  password: z.string().min(8, 'Mật khẩu phải có ít nhất 8 ký tự'),
  url: z.string().url('URL không hợp lệ'),
  positiveNumber: z.number().positive('Số phải lớn hơn 0'),
  nonNegativeNumber: z.number().min(0, 'Số không được âm'),
  id: z.number().positive('ID phải là số dương'),
  stringId: z.string().min(1, 'ID không được để trống'),
  name: z.string().min(1, 'Tên không được để trống').max(100, 'Tên không được quá 100 ký tự'),
  description: z.string().max(500, 'Mô tả không được quá 500 ký tự').optional(),
  tags: z.array(z.string()).max(10, 'Không được quá 10 tags').optional(),
} as const;

/**
 * Date validation schemas
 */
export const DateValidation = {
  isoString: z.string().datetime('Ngày giờ không hợp lệ'),
  dateOnly: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Định dạng ngày phải là YYYY-MM-DD'),
  timeOnly: z.string().regex(/^\d{2}:\d{2}$/, 'Định dạng giờ phải là HH:mm'),
  dateRange: z.object({
    start: z.string().datetime(),
    end: z.string().datetime(),
  }).refine(data => new Date(data.start) <= new Date(data.end), {
    message: 'Ngày bắt đầu phải trước ngày kết thúc',
  }),
} as const;

// ============================================================================
// USER VALIDATION SCHEMAS
// ============================================================================

/**
 * User validation schemas
 */
export const UserValidation = {
  /**
   * Create user validation
   */
  create: z.object({
    name: CommonValidation.name,
    email: CommonValidation.email,
    phone: CommonValidation.phone,
    initialCredits: CommonValidation.nonNegativeNumber.optional(),
    status: z.enum(['active', 'premium', 'inactive']).optional(),
  }) satisfies z.ZodType<CreateUserPayload>,

  /**
   * Update user validation
   */
  update: z.object({
    name: CommonValidation.name.optional(),
    email: CommonValidation.email.optional(),
    phone: CommonValidation.phone.optional(),
    status: z.enum(['active', 'premium', 'inactive']).optional(),
  }) satisfies z.ZodType<UpdateUserPayload>,

  /**
   * User search parameters validation
   */
  search: z.object({
    searchTerm: z.string().optional(),
    status: z.enum(['active', 'premium', 'inactive', 'all']).optional(),
    sortBy: z.enum(['name', 'credits', 'lastActivity', 'joinDate']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
    page: CommonValidation.positiveNumber.optional(),
    limit: z.number().min(1).max(100).optional(),
  }),

  /**
   * User login validation
   */
  login: z.object({
    email: CommonValidation.email,
    password: z.string().min(1, 'Mật khẩu không được để trống'),
    rememberMe: z.boolean().optional(),
  }),

  /**
   * Change password validation
   */
  changePassword: z.object({
    currentPassword: z.string().min(1, 'Mật khẩu hiện tại không được để trống'),
    newPassword: CommonValidation.password,
    confirmPassword: z.string(),
  }).refine(data => data.newPassword === data.confirmPassword, {
    message: 'Mật khẩu xác nhận không khớp',
    path: ['confirmPassword'],
  }),
} as const;

// ============================================================================
// CREDIT VALIDATION SCHEMAS
// ============================================================================

/**
 * Credit validation schemas
 */
export const CreditValidation = {
  /**
   * Credit operation validation
   */
  operation: z.object({
    userId: CommonValidation.id,
    amount: CommonValidation.positiveNumber,
    type: z.enum(['add', 'subtract', 'used', 'refund', 'bonus']),
    context: z.enum(['video_creation', 'photo_creation', 'premium_feature', 'api_call', 'other']).optional(),
    description: z.string().max(200, 'Mô tả không được quá 200 ký tự').optional(),
    adminId: CommonValidation.id.optional(),
  }) satisfies z.ZodType<CreditOperationPayload>,

  /**
   * Credit transaction query validation
   */
  transactionQuery: z.object({
    userId: CommonValidation.id.optional(),
    type: z.enum(['add', 'subtract', 'used', 'refund', 'bonus']).optional(),
    status: z.enum(['pending', 'completed', 'failed', 'cancelled']).optional(),
    context: z.enum(['video_creation', 'photo_creation', 'premium_feature', 'api_call', 'other']).optional(),
    startDate: DateValidation.isoString.optional(),
    endDate: DateValidation.isoString.optional(),
    page: CommonValidation.positiveNumber.optional(),
    limit: z.number().min(1).max(100).optional(),
    sortBy: z.enum(['createdAt', 'amount', 'balanceAfter']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
  }),

  /**
   * Credit package validation
   */
  package: z.object({
    name: CommonValidation.name,
    credits: CommonValidation.positiveNumber,
    price: CommonValidation.positiveNumber,
    currency: z.string().length(3, 'Mã tiền tệ phải có 3 ký tự'),
    description: CommonValidation.description,
    bonusCredits: CommonValidation.nonNegativeNumber.optional(),
    active: z.boolean(),
  }),
} as const;

// ============================================================================
// CONTENT VALIDATION SCHEMAS
// ============================================================================

/**
 * Content validation schemas
 */
export const ContentValidation = {
  /**
   * Create content validation
   */
  create: z.object({
    userId: CommonValidation.id,
    type: z.enum(['video', 'photo']),
    title: CommonValidation.name,
    description: CommonValidation.description,
    quality: z.enum(['low', 'medium', 'high', 'ultra']),
    visibility: z.enum(['private', 'public', 'unlisted']),
    tags: CommonValidation.tags,
    parameters: z.record(z.any()),
    sourceData: z.any().optional(),
  }) satisfies z.ZodType<CreateContentPayload>,

  /**
   * Update content validation
   */
  update: z.object({
    title: CommonValidation.name.optional(),
    description: CommonValidation.description,
    visibility: z.enum(['private', 'public', 'unlisted']).optional(),
    tags: CommonValidation.tags,
    metadata: z.record(z.any()).optional(),
  }) satisfies z.ZodType<UpdateContentPayload>,

  /**
   * Content search parameters validation
   */
  search: z.object({
    searchTerm: z.string().optional(),
    userId: CommonValidation.id.optional(),
    type: z.enum(['video', 'photo']).optional(),
    status: z.enum(['pending', 'processing', 'completed', 'error', 'cancelled']).optional(),
    quality: z.enum(['low', 'medium', 'high', 'ultra']).optional(),
    visibility: z.enum(['private', 'public', 'unlisted']).optional(),
    tags: z.array(z.string()).optional(),
    startDate: DateValidation.isoString.optional(),
    endDate: DateValidation.isoString.optional(),
    sortBy: z.enum(['createdAt', 'updatedAt', 'title', 'status', 'creditsCost']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
    page: CommonValidation.positiveNumber.optional(),
    limit: z.number().min(1).max(100).optional(),
  }),

  /**
   * File upload validation
   */
  fileUpload: z.object({
    file: z.instanceof(File),
    maxSize: z.number().positive().optional(),
    allowedTypes: z.array(z.string()).optional(),
  }).refine(data => {
    if (data.maxSize && data.file.size > data.maxSize) {
      return false;
    }
    if (data.allowedTypes && !data.allowedTypes.includes(data.file.type)) {
      return false;
    }
    return true;
  }, {
    message: 'File không hợp lệ',
  }),
} as const;

// ============================================================================
// NOTIFICATION VALIDATION SCHEMAS
// ============================================================================

/**
 * Notification validation schemas
 */
export const NotificationValidation = {
  /**
   * Create notification validation
   */
  create: z.object({
    title: CommonValidation.name,
    message: z.string().min(1, 'Nội dung thông báo không được để trống').max(1000, 'Nội dung không được quá 1000 ký tự'),
    type: z.enum(['info', 'success', 'warning', 'error', 'announcement']),
    priority: z.enum(['low', 'medium', 'high', 'urgent']),
    target: z.enum(['all_users', 'specific_users', 'user_groups', 'admins']),
    targetUserIds: z.array(CommonValidation.id).optional(),
    targetGroups: z.array(z.string()).optional(),
    deliveryMethods: z.array(z.enum(['in_app', 'email', 'sms', 'push', 'webhook'])),
    scheduledAt: DateValidation.isoString.optional(),
    expiresAt: DateValidation.isoString.optional(),
    richContent: z.string().optional(),
    actions: z.array(z.object({
      id: CommonValidation.stringId,
      label: z.string().min(1, 'Nhãn hành động không được để trống'),
      type: z.enum(['link', 'callback', 'dismiss']),
      url: CommonValidation.url.optional(),
      callbackData: z.any().optional(),
      style: z.enum(['primary', 'secondary', 'danger']).optional(),
    })).optional(),
    attachments: z.array(z.object({
      id: CommonValidation.stringId,
      fileName: z.string().min(1, 'Tên file không được để trống'),
      fileUrl: CommonValidation.url,
      fileSize: CommonValidation.positiveNumber,
      mimeType: z.string().min(1, 'MIME type không được để trống'),
    })).optional(),
    metadata: z.record(z.any()).optional(),
  }) satisfies z.ZodType<CreateNotificationPayload>,

  /**
   * Notification search parameters validation
   */
  search: z.object({
    searchTerm: z.string().optional(),
    type: z.enum(['info', 'success', 'warning', 'error', 'announcement']).optional(),
    status: z.enum(['pending', 'sent', 'delivered', 'read', 'failed']).optional(),
    priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
    target: z.enum(['all_users', 'specific_users', 'user_groups', 'admins']).optional(),
    createdBy: CommonValidation.id.optional(),
    startDate: DateValidation.isoString.optional(),
    endDate: DateValidation.isoString.optional(),
    sortBy: z.enum(['createdAt', 'scheduledAt', 'sentAt', 'priority']).optional(),
    sortOrder: z.enum(['asc', 'desc']).optional(),
    page: CommonValidation.positiveNumber.optional(),
    limit: z.number().min(1).max(100).optional(),
  }),

  /**
   * Notification template validation
   */
  template: z.object({
    name: CommonValidation.name,
    description: CommonValidation.description,
    type: z.enum(['info', 'success', 'warning', 'error', 'announcement']),
    titleTemplate: z.string().min(1, 'Template tiêu đề không được để trống'),
    messageTemplate: z.string().min(1, 'Template nội dung không được để trống'),
    richContentTemplate: z.string().optional(),
    defaultDeliveryMethods: z.array(z.enum(['in_app', 'email', 'sms', 'push', 'webhook'])),
    variables: z.array(z.object({
      name: z.string().min(1, 'Tên biến không được để trống'),
      type: z.enum(['string', 'number', 'boolean', 'date']),
      required: z.boolean(),
      description: z.string().optional(),
      defaultValue: z.any().optional(),
    })),
    active: z.boolean(),
  }),
} as const;

// ============================================================================
// ANALYTICS VALIDATION SCHEMAS
// ============================================================================

/**
 * Analytics validation schemas
 */
export const AnalyticsValidation = {
  /**
   * Analytics query validation
   */
  query: z.object({
    period: z.enum(['today', 'week', 'month', 'quarter', 'year', 'all']),
    startDate: DateValidation.isoString.optional(),
    endDate: DateValidation.isoString.optional(),
    metrics: z.array(z.string()).optional(),
    groupBy: z.enum(['hour', 'day', 'week', 'month']).optional(),
  }),

  /**
   * Export configuration validation
   */
  exportConfig: z.object({
    format: z.enum(['csv', 'xlsx', 'pdf', 'json']),
    metrics: z.array(z.string()),
    dateRange: DateValidation.dateRange,
    includeCharts: z.boolean().optional(),
    fileName: z.string().optional(),
  }),
} as const;

// ============================================================================
// SYSTEM VALIDATION SCHEMAS
// ============================================================================

/**
 * System validation schemas
 */
export const SystemValidation = {
  /**
   * System configuration validation
   */
  config: z.object({
    features: z.record(z.boolean()),
    credits: z.object({
      defaultCredits: CommonValidation.nonNegativeNumber,
      maxCreditsPerUser: CommonValidation.positiveNumber,
      videoCreditCost: CommonValidation.positiveNumber,
      photoCreditCost: CommonValidation.positiveNumber,
    }),
    security: z.object({
      passwordMinLength: z.number().min(6).max(128),
      sessionTimeoutMinutes: CommonValidation.positiveNumber,
      maxLoginAttempts: CommonValidation.positiveNumber,
    }),
  }),

  /**
   * Maintenance settings validation
   */
  maintenance: z.object({
    enabled: z.boolean(),
    startTime: DateValidation.isoString.optional(),
    endTime: DateValidation.isoString.optional(),
    message: z.string().min(1, 'Thông báo bảo trì không được để trống'),
    allowAdminAccess: z.boolean(),
    affectedServices: z.array(z.string()),
  }),
} as const;

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validation utility functions
 */
export const ValidationUtils = {
  /**
   * Validate data against schema and return formatted errors
   */
  validate: <T>(schema: z.ZodSchema<T>, data: unknown): { success: boolean; data?: T; errors?: string[] } => {
    try {
      const result = schema.parse(data);
      return { success: true, data: result };
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => {
          const path = err.path.join('.');
          return path ? `${path}: ${err.message}` : err.message;
        });
        return { success: false, errors };
      }
      return { success: false, errors: ['Validation failed'] };
    }
  },

  /**
   * Validate data and throw error if invalid
   */
  validateOrThrow: <T>(schema: z.ZodSchema<T>, data: unknown): T => {
    return schema.parse(data);
  },

  /**
   * Safe validation that returns undefined if invalid
   */
  safeParse: <T>(schema: z.ZodSchema<T>, data: unknown): T | undefined => {
    try {
      return schema.parse(data);
    } catch {
      return undefined;
    }
  },

  /**
   * Check if data is valid without parsing
   */
  isValid: <T>(schema: z.ZodSchema<T>, data: unknown): boolean => {
    try {
      schema.parse(data);
      return true;
    } catch {
      return false;
    }
  },

  /**
   * Get validation errors as formatted strings
   */
  getErrors: (error: z.ZodError): string[] => {
    return error.errors.map(err => {
      const path = err.path.join('.');
      return path ? `${path}: ${err.message}` : err.message;
    });
  },
} as const;

// ============================================================================
// EXPORT ALL VALIDATIONS
// ============================================================================

/**
 * All validation schemas combined for easy access
 */
export const AllValidations = {
  Common: CommonValidation,
  Date: DateValidation,
  User: UserValidation,
  Credit: CreditValidation,
  Content: ContentValidation,
  Notification: NotificationValidation,
  Analytics: AnalyticsValidation,
  System: SystemValidation,
} as const;
