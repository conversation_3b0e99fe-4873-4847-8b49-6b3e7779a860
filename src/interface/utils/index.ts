/**
 * Interface Utils - Central Export
 * 
 * This module exports all utility functions for the Interface Layer.
 * Utils handle data validation, formatting, and UI-specific operations.
 * 
 * Architecture Rules:
 * - Utils belong to Interface Layer
 * - Handle data validation with Zod schemas
 * - Provide formatting and transformation utilities
 * - Support UI-specific operations and helpers
 */

// Export validation utilities
export * from './validation';

// Export formatting utilities
export * from './formatters';

// Export UI constants
export * from './constants';

// Export permission utilities
export * from './permissions';

// Export responsive utilities
export * from './responsive';

// ============================================================================
// UTILITY METADATA
// ============================================================================

/**
 * Interface utils metadata for debugging and documentation
 */
export const INTERFACE_UTILS_INFO = {
  version: '1.0.0',
  description: 'Interface Layer Utilities - Validation, Formatting, and UI helpers',
  architecture: '3-layer MVI',
  modules: {
    validation: 'Zod schemas and validation utilities',
    formatters: 'Data formatting for display',
    constants: 'UI-specific constants and configuration',
    permissions: 'Permission checking and access control',
    responsive: 'Responsive design and device detection',
  },
  dependencies: ['Zod', 'Intl API', 'Media Query API'],
  features: [
    'Type-safe validation',
    'Internationalized formatting',
    'Permission-based access control',
    'Responsive design utilities',
    'UI constants and theming'
  ]
} as const;
