/**
 * Data Formatting Utilities
 * 
 * This module contains utilities for formatting data for display in the UI.
 * According to our 3-layer MVI architecture, formatters belong in Interface Layer utils.
 * 
 * Architecture Rules:
 * - Formatters belong in Interface Layer utils
 * - Handle data presentation and display formatting
 * - Support internationalization and localization
 * - Provide consistent formatting across the application
 */

// ============================================================================
// NUMBER FORMATTERS
// ============================================================================

/**
 * Number formatting utilities
 */
export const NumberFormatters = {
  /**
   * Format number with thousand separators
   */
  formatNumber: (value: number, locale: string = 'vi-VN'): string => {
    return new Intl.NumberFormat(locale).format(value);
  },

  /**
   * Format currency (VND)
   */
  formatCurrency: (value: number, currency: string = 'VND', locale: string = 'vi-VN'): string => {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  },

  /**
   * Format percentage
   */
  formatPercentage: (value: number, decimals: number = 1, locale: string = 'vi-VN'): string => {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value / 100);
  },

  /**
   * Format file size
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  },

  /**
   * Format credits with appropriate suffix
   */
  formatCredits: (value: number): string => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return NumberFormatters.formatNumber(value);
  },

  /**
   * Format duration in seconds to readable format
   */
  formatDuration: (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  },
} as const;

// ============================================================================
// DATE FORMATTERS
// ============================================================================

/**
 * Date formatting utilities
 */
export const DateFormatters = {
  /**
   * Format date to Vietnamese format
   */
  formatDate: (date: string | Date, locale: string = 'vi-VN'): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    }).format(dateObj);
  },

  /**
   * Format date and time
   */
  formatDateTime: (date: string | Date, locale: string = 'vi-VN'): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    }).format(dateObj);
  },

  /**
   * Format time only
   */
  formatTime: (date: string | Date, locale: string = 'vi-VN'): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return new Intl.DateTimeFormat(locale, {
      hour: '2-digit',
      minute: '2-digit',
    }).format(dateObj);
  },

  /**
   * Format relative time (e.g., "2 hours ago")
   */
  formatRelativeTime: (date: string | Date, locale: string = 'vi-VN'): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second');
    }
    if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
    }
    if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
    }
    if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
    }
    if (diffInSeconds < 31536000) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
    }
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  },

  /**
   * Format date range
   */
  formatDateRange: (startDate: string | Date, endDate: string | Date, locale: string = 'vi-VN'): string => {
    const start = DateFormatters.formatDate(startDate, locale);
    const end = DateFormatters.formatDate(endDate, locale);
    return `${start} - ${end}`;
  },

  /**
   * Check if date is today
   */
  isToday: (date: string | Date): boolean => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return dateObj.toDateString() === today.toDateString();
  },

  /**
   * Check if date is this week
   */
  isThisWeek: (date: string | Date): boolean => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
    const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
    return dateObj >= weekStart && dateObj <= weekEnd;
  },
} as const;

// ============================================================================
// TEXT FORMATTERS
// ============================================================================

/**
 * Text formatting utilities
 */
export const TextFormatters = {
  /**
   * Truncate text with ellipsis
   */
  truncate: (text: string, maxLength: number, suffix: string = '...'): string => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - suffix.length) + suffix;
  },

  /**
   * Capitalize first letter
   */
  capitalize: (text: string): string => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  },

  /**
   * Convert to title case
   */
  titleCase: (text: string): string => {
    return text.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  },

  /**
   * Convert to kebab-case
   */
  kebabCase: (text: string): string => {
    return text
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  },

  /**
   * Convert to camelCase
   */
  camelCase: (text: string): string => {
    return text
      .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
        index === 0 ? word.toLowerCase() : word.toUpperCase()
      )
      .replace(/\s+/g, '');
  },

  /**
   * Extract initials from name
   */
  getInitials: (name: string, maxInitials: number = 2): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0).toUpperCase())
      .slice(0, maxInitials)
      .join('');
  },

  /**
   * Mask sensitive information
   */
  maskEmail: (email: string): string => {
    const [username, domain] = email.split('@');
    if (username.length <= 2) return email;
    
    const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
    return `${maskedUsername}@${domain}`;
  },

  /**
   * Mask phone number
   */
  maskPhone: (phone: string): string => {
    if (phone.length <= 4) return phone;
    
    const visibleStart = phone.substring(0, 3);
    const visibleEnd = phone.substring(phone.length - 2);
    const masked = '*'.repeat(phone.length - 5);
    
    return `${visibleStart}${masked}${visibleEnd}`;
  },
} as const;

// ============================================================================
// STATUS FORMATTERS
// ============================================================================

/**
 * Status formatting utilities
 */
export const StatusFormatters = {
  /**
   * Format user status
   */
  formatUserStatus: (status: string): { label: string; color: string } => {
    const statusMap = {
      active: { label: 'Hoạt động', color: 'green' },
      premium: { label: 'Premium', color: 'blue' },
      inactive: { label: 'Không hoạt động', color: 'gray' },
    };
    return statusMap[status as keyof typeof statusMap] || { label: status, color: 'gray' };
  },

  /**
   * Format content status
   */
  formatContentStatus: (status: string): { label: string; color: string } => {
    const statusMap = {
      pending: { label: 'Chờ xử lý', color: 'yellow' },
      processing: { label: 'Đang xử lý', color: 'blue' },
      completed: { label: 'Hoàn thành', color: 'green' },
      error: { label: 'Lỗi', color: 'red' },
      cancelled: { label: 'Đã hủy', color: 'gray' },
    };
    return statusMap[status as keyof typeof statusMap] || { label: status, color: 'gray' };
  },

  /**
   * Format notification priority
   */
  formatNotificationPriority: (priority: string): { label: string; color: string } => {
    const priorityMap = {
      low: { label: 'Thấp', color: 'gray' },
      medium: { label: 'Trung bình', color: 'yellow' },
      high: { label: 'Cao', color: 'orange' },
      urgent: { label: 'Khẩn cấp', color: 'red' },
    };
    return priorityMap[priority as keyof typeof priorityMap] || { label: priority, color: 'gray' };
  },
} as const;

// ============================================================================
// COMBINED FORMATTERS
// ============================================================================

/**
 * All formatters combined for easy access
 */
export const Formatters = {
  Number: NumberFormatters,
  Date: DateFormatters,
  Text: TextFormatters,
  Status: StatusFormatters,
} as const;
