/**
 * Styles - Central Export
 * 
 * This module exports style-related utilities and constants.
 * Styles belong to Interface Layer assets.
 */

// Export style utilities
export const StyleUtils = {
  /**
   * Combine class names conditionally
   */
  cn: (...classes: (string | undefined | null | false)[]): string => {
    return classes.filter(Boolean).join(' ');
  },

  /**
   * Create CSS variables object
   */
  createCSSVars: (vars: Record<string, string | number>): Record<string, string> => {
    const cssVars: Record<string, string> = {};
    Object.entries(vars).forEach(([key, value]) => {
      cssVars[`--${key}`] = String(value);
    });
    return cssVars;
  },
} as const;
