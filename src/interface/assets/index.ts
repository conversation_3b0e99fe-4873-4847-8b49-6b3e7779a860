/**
 * Assets Management - Central Export
 * 
 * This module manages all static assets for the Interface Layer.
 * According to our 3-layer MVI architecture, assets belong in Interface Layer.
 * 
 * Architecture Rules:
 * - Assets are managed in Interface Layer only
 * - Provides proper imports and path constants
 * - Organizes assets by type and usage
 */

// ============================================================================
// ASSET PATH CONSTANTS
// ============================================================================

/**
 * Base asset paths
 */
export const ASSET_PATHS = {
  IMAGES: '/src/interface/assets/images',
  FONTS: '/src/interface/assets/fonts',
  STYLES: '/src/interface/assets/styles',
  ICONS: '/src/interface/assets/icons',
} as const;

/**
 * Public asset paths (served from /public)
 */
export const PUBLIC_ASSET_PATHS = {
  FAVICON: '/favicon.ico',
  PLACEHOLDER: '/placeholder.svg',
  ROBOTS: '/robots.txt',
} as const;

// ============================================================================
// IMAGE ASSETS
// ============================================================================

/**
 * Logo and branding assets
 */
export const LOGO_ASSETS = {
  // Main logos
  MAIN_LOGO: '/src/interface/assets/images/logos/mega-ai-logo.svg',
  MAIN_LOGO_DARK: '/src/interface/assets/images/logos/mega-ai-logo-dark.svg',
  MAIN_LOGO_LIGHT: '/src/interface/assets/images/logos/mega-ai-logo-light.svg',
  
  // Icon versions
  LOGO_ICON: '/src/interface/assets/images/logos/mega-ai-icon.svg',
  LOGO_ICON_DARK: '/src/interface/assets/images/logos/mega-ai-icon-dark.svg',
  LOGO_ICON_LIGHT: '/src/interface/assets/images/logos/mega-ai-icon-light.svg',
  
  // Favicon versions
  FAVICON_16: '/src/interface/assets/images/logos/favicon-16x16.png',
  FAVICON_32: '/src/interface/assets/images/logos/favicon-32x32.png',
  FAVICON_192: '/src/interface/assets/images/logos/favicon-192x192.png',
  FAVICON_512: '/src/interface/assets/images/logos/favicon-512x512.png',
} as const;

/**
 * Illustration assets
 */
export const ILLUSTRATION_ASSETS = {
  // Empty states
  EMPTY_USERS: '/src/interface/assets/images/illustrations/empty-users.svg',
  EMPTY_CONTENT: '/src/interface/assets/images/illustrations/empty-content.svg',
  EMPTY_NOTIFICATIONS: '/src/interface/assets/images/illustrations/empty-notifications.svg',
  EMPTY_ANALYTICS: '/src/interface/assets/images/illustrations/empty-analytics.svg',
  
  // Error states
  ERROR_404: '/src/interface/assets/images/illustrations/error-404.svg',
  ERROR_500: '/src/interface/assets/images/illustrations/error-500.svg',
  ERROR_NETWORK: '/src/interface/assets/images/illustrations/error-network.svg',
  
  // Success states
  SUCCESS_UPLOAD: '/src/interface/assets/images/illustrations/success-upload.svg',
  SUCCESS_PAYMENT: '/src/interface/assets/images/illustrations/success-payment.svg',
  SUCCESS_NOTIFICATION: '/src/interface/assets/images/illustrations/success-notification.svg',
  
  // Loading states
  LOADING_CONTENT: '/src/interface/assets/images/illustrations/loading-content.svg',
  LOADING_PROCESSING: '/src/interface/assets/images/illustrations/loading-processing.svg',
  
  // Onboarding
  ONBOARDING_WELCOME: '/src/interface/assets/images/illustrations/onboarding-welcome.svg',
  ONBOARDING_FEATURES: '/src/interface/assets/images/illustrations/onboarding-features.svg',
  ONBOARDING_COMPLETE: '/src/interface/assets/images/illustrations/onboarding-complete.svg',
} as const;

/**
 * Avatar and profile assets
 */
export const AVATAR_ASSETS = {
  DEFAULT_AVATAR: '/src/interface/assets/images/avatars/default-avatar.svg',
  DEFAULT_ADMIN: '/src/interface/assets/images/avatars/default-admin.svg',
  DEFAULT_USER: '/src/interface/assets/images/avatars/default-user.svg',
  PLACEHOLDER_AVATAR: '/src/interface/assets/images/avatars/placeholder.svg',
} as const;

/**
 * Background and pattern assets
 */
export const BACKGROUND_ASSETS = {
  // Login backgrounds
  LOGIN_BG: '/src/interface/assets/images/backgrounds/login-bg.jpg',
  LOGIN_BG_DARK: '/src/interface/assets/images/backgrounds/login-bg-dark.jpg',
  
  // Dashboard backgrounds
  DASHBOARD_BG: '/src/interface/assets/images/backgrounds/dashboard-bg.svg',
  HERO_BG: '/src/interface/assets/images/backgrounds/hero-bg.svg',
  
  // Patterns
  PATTERN_DOTS: '/src/interface/assets/images/patterns/dots.svg',
  PATTERN_GRID: '/src/interface/assets/images/patterns/grid.svg',
  PATTERN_WAVES: '/src/interface/assets/images/patterns/waves.svg',
} as const;

// ============================================================================
// ICON ASSETS
// ============================================================================

/**
 * Custom icon assets (SVG)
 */
export const ICON_ASSETS = {
  // Navigation icons
  DASHBOARD: '/src/interface/assets/icons/dashboard.svg',
  USERS: '/src/interface/assets/icons/users.svg',
  CONTENT: '/src/interface/assets/icons/content.svg',
  ANALYTICS: '/src/interface/assets/icons/analytics.svg',
  NOTIFICATIONS: '/src/interface/assets/icons/notifications.svg',
  SETTINGS: '/src/interface/assets/icons/settings.svg',
  
  // Action icons
  ADD: '/src/interface/assets/icons/add.svg',
  EDIT: '/src/interface/assets/icons/edit.svg',
  DELETE: '/src/interface/assets/icons/delete.svg',
  DOWNLOAD: '/src/interface/assets/icons/download.svg',
  UPLOAD: '/src/interface/assets/icons/upload.svg',
  SEARCH: '/src/interface/assets/icons/search.svg',
  FILTER: '/src/interface/assets/icons/filter.svg',
  SORT: '/src/interface/assets/icons/sort.svg',
  
  // Status icons
  SUCCESS: '/src/interface/assets/icons/success.svg',
  ERROR: '/src/interface/assets/icons/error.svg',
  WARNING: '/src/interface/assets/icons/warning.svg',
  INFO: '/src/interface/assets/icons/info.svg',
  LOADING: '/src/interface/assets/icons/loading.svg',
  
  // Content type icons
  VIDEO: '/src/interface/assets/icons/video.svg',
  PHOTO: '/src/interface/assets/icons/photo.svg',
  AUDIO: '/src/interface/assets/icons/audio.svg',
  DOCUMENT: '/src/interface/assets/icons/document.svg',
  
  // Credit and payment icons
  CREDIT: '/src/interface/assets/icons/credit.svg',
  PAYMENT: '/src/interface/assets/icons/payment.svg',
  WALLET: '/src/interface/assets/icons/wallet.svg',
  TRANSACTION: '/src/interface/assets/icons/transaction.svg',
} as const;

// ============================================================================
// FONT ASSETS
// ============================================================================

/**
 * Font asset paths
 */
export const FONT_ASSETS = {
  // Inter font family
  INTER_REGULAR: '/src/interface/assets/fonts/inter/Inter-Regular.woff2',
  INTER_MEDIUM: '/src/interface/assets/fonts/inter/Inter-Medium.woff2',
  INTER_SEMIBOLD: '/src/interface/assets/fonts/inter/Inter-SemiBold.woff2',
  INTER_BOLD: '/src/interface/assets/fonts/inter/Inter-Bold.woff2',
  
  // Custom brand fonts (if any)
  BRAND_REGULAR: '/src/interface/assets/fonts/custom/Brand-Regular.woff2',
  BRAND_BOLD: '/src/interface/assets/fonts/custom/Brand-Bold.woff2',
} as const;

// ============================================================================
// STYLE ASSETS
// ============================================================================

/**
 * Style asset paths
 */
export const STYLE_ASSETS = {
  // Theme CSS files
  THEME_LIGHT: '/src/interface/assets/styles/themes/light.css',
  THEME_DARK: '/src/interface/assets/styles/themes/dark.css',
  
  // Component styles
  COMPONENTS: '/src/interface/assets/styles/components/index.css',
  
  // Animation styles
  ANIMATIONS: '/src/interface/assets/styles/animations/index.css',
  
  // Print styles
  PRINT: '/src/interface/assets/styles/print.css',
} as const;

// ============================================================================
// ASSET UTILITIES
// ============================================================================

/**
 * Asset utility functions
 */
export const AssetUtils = {
  /**
   * Get asset URL with base path
   */
  getAssetUrl: (assetPath: string): string => {
    // In development, assets are served from src
    // In production, they would be processed by build tool
    return assetPath;
  },

  /**
   * Get public asset URL
   */
  getPublicAssetUrl: (assetPath: string): string => {
    return assetPath;
  },

  /**
   * Get responsive image sources
   */
  getResponsiveImageSources: (basePath: string): string[] => {
    const sizes = ['400w', '800w', '1200w', '1600w'];
    return sizes.map(size => {
      const extension = basePath.split('.').pop();
      const nameWithoutExt = basePath.replace(/\.[^/.]+$/, '');
      return `${nameWithoutExt}-${size}.${extension}`;
    });
  },

  /**
   * Get optimized image URL
   */
  getOptimizedImageUrl: (assetPath: string, options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpg' | 'png';
  }): string => {
    // In a real implementation, this would integrate with an image optimization service
    // For now, return the original path
    return assetPath;
  },

  /**
   * Preload critical assets
   */
  preloadAssets: (assetPaths: string[]): void => {
    assetPaths.forEach(path => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = path;
      
      // Determine asset type
      if (path.includes('.woff') || path.includes('.ttf')) {
        link.as = 'font';
        link.crossOrigin = 'anonymous';
      } else if (path.includes('.css')) {
        link.as = 'style';
      } else if (path.includes('.js')) {
        link.as = 'script';
      } else {
        link.as = 'image';
      }
      
      document.head.appendChild(link);
    });
  },

  /**
   * Check if asset exists
   */
  assetExists: async (assetPath: string): Promise<boolean> => {
    try {
      const response = await fetch(assetPath, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  },

  /**
   * Get file size of asset
   */
  getAssetSize: async (assetPath: string): Promise<number | null> => {
    try {
      const response = await fetch(assetPath, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      return contentLength ? parseInt(contentLength, 10) : null;
    } catch {
      return null;
    }
  },
} as const;

// ============================================================================
// ASSET PRELOADING CONFIGURATION
// ============================================================================

/**
 * Critical assets that should be preloaded
 */
export const CRITICAL_ASSETS = [
  LOGO_ASSETS.MAIN_LOGO,
  FONT_ASSETS.INTER_REGULAR,
  FONT_ASSETS.INTER_MEDIUM,
  STYLE_ASSETS.THEME_LIGHT,
] as const;

/**
 * Assets to preload on specific pages
 */
export const PAGE_SPECIFIC_ASSETS = {
  LOGIN: [
    BACKGROUND_ASSETS.LOGIN_BG,
    ILLUSTRATION_ASSETS.ONBOARDING_WELCOME,
  ],
  DASHBOARD: [
    BACKGROUND_ASSETS.DASHBOARD_BG,
    ICON_ASSETS.DASHBOARD,
    ICON_ASSETS.ANALYTICS,
  ],
  USER_MANAGEMENT: [
    ICON_ASSETS.USERS,
    AVATAR_ASSETS.DEFAULT_AVATAR,
    ILLUSTRATION_ASSETS.EMPTY_USERS,
  ],
  CONTENT_MANAGEMENT: [
    ICON_ASSETS.CONTENT,
    ICON_ASSETS.VIDEO,
    ICON_ASSETS.PHOTO,
    ILLUSTRATION_ASSETS.EMPTY_CONTENT,
  ],
} as const;

// ============================================================================
// EXPORT ALL ASSETS
// ============================================================================

/**
 * All assets combined for easy access
 */
export const ALL_ASSETS = {
  PATHS: ASSET_PATHS,
  PUBLIC: PUBLIC_ASSET_PATHS,
  LOGOS: LOGO_ASSETS,
  ILLUSTRATIONS: ILLUSTRATION_ASSETS,
  AVATARS: AVATAR_ASSETS,
  BACKGROUNDS: BACKGROUND_ASSETS,
  ICONS: ICON_ASSETS,
  FONTS: FONT_ASSETS,
  STYLES: STYLE_ASSETS,
} as const;

/**
 * Asset management configuration
 */
export const ASSET_CONFIG = {
  CRITICAL_ASSETS,
  PAGE_SPECIFIC_ASSETS,
  UTILS: AssetUtils,
} as const;
