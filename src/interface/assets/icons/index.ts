/**
 * Icons - Central Export
 * 
 * This module exports icon-related utilities and constants.
 * Icons belong to Interface Layer assets.
 */

// Export icon utilities
export const IconUtils = {
  /**
   * Get icon size class
   */
  getIconSize: (size: 'sm' | 'md' | 'lg' | 'xl'): string => {
    const sizeMap = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
      xl: 'w-8 h-8',
    };
    return sizeMap[size];
  },

  /**
   * Get icon color class
   */
  getIconColor: (color: 'primary' | 'secondary' | 'success' | 'warning' | 'error'): string => {
    const colorMap = {
      primary: 'text-blue-600',
      secondary: 'text-gray-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      error: 'text-red-600',
    };
    return colorMap[color];
  },
} as const;
