/**
 * Interface Layer - Central Export
 * 
 * This is the main entry point for the Interface Layer in our 3-layer MVI architecture.
 * The Interface Layer handles all UI concerns including components, pages, hooks, utils, and assets.
 * 
 * Architecture Rules:
 * - Interface Layer contains all UI-related code
 * - Components are organized by type: UI, Features, Layout
 * - Pages orchestrate features and handle routing
 * - Hooks provide data and UI state management
 * - Utils handle validation, formatting, and UI operations
 * - Assets manage static resources and media files
 */

// ============================================================================
// COMPONENTS EXPORTS
// ============================================================================

// Export all components (UI, Features, Layout)
export * from './components';

// ============================================================================
// PAGES EXPORTS
// ============================================================================

// ============================================================================
// UTILS EXPORTS
// ============================================================================

// Export all utilities
export * from './utils';

// ============================================================================
// ASSETS EXPORTS
// ============================================================================

// Export all assets
export * from './assets';

// ============================================================================
// INTERFACE LAYER METADATA
// ============================================================================

/**
 * Interface Layer metadata for debugging and documentation
 */
export const INTERFACE_LAYER_INFO = {
  version: '1.0.0',
  description: 'Interface Layer - Complete UI solution for 3-layer MVI architecture',
  architecture: '3-layer MVI',
  
  layers: {
    current: 'Interface Layer',
    dependencies: ['Data Layer', 'Model Layer'],
    responsibilities: [
      'User interface components',
      'Page routing and navigation',
      'UI state management',
      'Data presentation and formatting',
      'User interactions and events',
      'Asset management',
      'Responsive design',
      'Accessibility',
    ],
  },

  organization: {
    components: {
      ui: 'Reusable, generic components (shadcn/ui)',
      features: 'Domain-specific business components',
      layout: 'Structure and navigation components',
    },
    pages: 'Container components for routing',
    hooks: {
      data: 'React Query integration with repositories',
      ui: 'UI state and interaction management',
    },
    utils: {
      validation: 'Zod schemas and validation',
      formatters: 'Data formatting for display',
      constants: 'UI-specific constants',
      permissions: 'Access control utilities',
      responsive: 'Responsive design utilities',
    },
    assets: {
      images: 'Image files and utilities',
      icons: 'Icon components and utilities',
      styles: 'Style utilities and CSS',
      fonts: 'Typography assets',
    },
  },

  features: [
    'Type-safe component exports',
    'React Query data hooks',
    'Zod validation schemas',
    'Responsive design utilities',
    'Permission-based access control',
    'Internationalized formatting',
    'Asset management system',
    'Consistent UI patterns',
    'Accessibility support',
    'Performance optimization',
  ],

  dependencies: {
    core: ['React', 'TypeScript'],
    ui: ['shadcn/ui', 'Tailwind CSS', 'Radix UI'],
    state: ['React Query', 'Zustand'],
    validation: ['Zod'],
    routing: ['React Router'],
    icons: ['Lucide React'],
  },

  conventions: {
    naming: {
      components: 'PascalCase',
      hooks: 'camelCase with use prefix',
      utils: 'camelCase',
      constants: 'UPPER_SNAKE_CASE',
      types: 'PascalCase',
    },
    files: {
      components: 'PascalCase.tsx',
      hooks: 'camelCase.ts',
      utils: 'camelCase.ts',
      types: 'camelCase.ts',
      constants: 'camelCase.ts',
    },
    exports: {
      default: 'For main component exports',
      named: 'For utilities and types',
      barrel: 'index.ts files for organized exports',
    },
  },

  patterns: {
    components: [
      'Compound component pattern',
      'Render props pattern',
      'Custom hooks pattern',
      'Provider pattern',
    ],
    hooks: [
      'Data fetching with React Query',
      'UI state management',
      'Form handling',
      'Event handling',
    ],
    utils: [
      'Pure functions',
      'Type-safe utilities',
      'Consistent error handling',
      'Internationalization support',
    ],
  },
} as const;

// ============================================================================
// DEVELOPMENT HELPERS
// ============================================================================

/**
 * Development helper to validate Interface Layer integrity
 * Only available in development mode
 */
export const validateInterfaceLayer = () => {
  if (process.env.NODE_ENV !== 'development') {
    return { valid: true, message: 'Validation only available in development' };
  }

  const checks = [
    // Check that main exports are available
    typeof INTERFACE_LAYER_INFO !== 'undefined',
    
    // Add more checks as needed
  ];

  const allValid = checks.every(check => check === true);

  return {
    valid: allValid,
    message: allValid 
      ? 'Interface Layer validation passed' 
      : 'Interface Layer validation failed - some exports are missing',
    timestamp: new Date().toISOString(),
    info: INTERFACE_LAYER_INFO,
  };
};

// ============================================================================
// QUICK ACCESS UTILITIES
// ============================================================================

/**
 * Quick access to commonly used Interface Layer utilities
 */
export const InterfaceUtils = {
  /**
   * Get layer information
   */
  getLayerInfo: () => INTERFACE_LAYER_INFO,

  /**
   * Validate layer integrity
   */
  validate: validateInterfaceLayer,

  /**
   * Get component organization info
   */
  getComponentInfo: () => INTERFACE_LAYER_INFO.organization.components,

  /**
   * Get hook organization info
   */
  getHookInfo: () => INTERFACE_LAYER_INFO.organization.hooks,

  /**
   * Get utility organization info
   */
  getUtilInfo: () => INTERFACE_LAYER_INFO.organization.utils,

  /**
   * Get asset organization info
   */
  getAssetInfo: () => INTERFACE_LAYER_INFO.organization.assets,
} as const;
