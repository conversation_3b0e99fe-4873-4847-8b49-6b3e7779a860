/**
 * useFilters Hook
 * 
 * React hook for table filtering and sorting logic.
 * Provides utilities for managing filter state and operations.
 * 
 * Architecture Rules:
 * - UI hook belongs to Interface Layer
 * - Handles UI state and interactions only
 * - Independent of data layer operations
 * - Provides consistent filtering patterns
 */

import { useState, useCallback, useMemo } from 'react';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface FilterState {
  searchTerm: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  filters: Record<string, any>;
  page: number;
  limit: number;
}

export interface UseFiltersOptions {
  defaultFilters?: Partial<FilterState>;
  onFiltersChange?: (filters: FilterState) => void;
}

export interface UseFiltersResult {
  filters: FilterState;
  setSearchTerm: (term: string) => void;
  setSortBy: (field: string) => void;
  setSortOrder: (order: 'asc' | 'desc') => void;
  setFilter: (key: string, value: any) => void;
  removeFilter: (key: string) => void;
  clearFilters: () => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  resetPagination: () => void;
  hasActiveFilters: boolean;
  getQueryParams: () => Record<string, any>;
}

// ============================================================================
// FILTERS HOOK
// ============================================================================

/**
 * Hook for managing filter state
 */
export function useFilters(options: UseFiltersOptions = {}): UseFiltersResult {
  const { defaultFilters = {}, onFiltersChange } = options;

  const initialState: FilterState = {
    searchTerm: '',
    sortBy: 'createdAt',
    sortOrder: 'desc',
    filters: {},
    page: 1,
    limit: 10,
    ...defaultFilters,
  };

  const [filters, setFilters] = useState<FilterState>(initialState);

  // Update filters and notify parent
  const updateFilters = useCallback((newFilters: Partial<FilterState>) => {
    setFilters(prev => {
      const updated = { ...prev, ...newFilters };
      onFiltersChange?.(updated);
      return updated;
    });
  }, [onFiltersChange]);

  const setSearchTerm = useCallback((term: string) => {
    updateFilters({ searchTerm: term, page: 1 }); // Reset page when searching
  }, [updateFilters]);

  const setSortBy = useCallback((field: string) => {
    updateFilters({ sortBy: field, page: 1 }); // Reset page when sorting
  }, [updateFilters]);

  const setSortOrder = useCallback((order: 'asc' | 'desc') => {
    updateFilters({ sortOrder: order, page: 1 }); // Reset page when sorting
  }, [updateFilters]);

  const setFilter = useCallback((key: string, value: any) => {
    updateFilters({
      filters: { ...filters.filters, [key]: value },
      page: 1, // Reset page when filtering
    });
  }, [filters.filters, updateFilters]);

  const removeFilter = useCallback((key: string) => {
    const newFilters = { ...filters.filters };
    delete newFilters[key];
    updateFilters({
      filters: newFilters,
      page: 1, // Reset page when removing filter
    });
  }, [filters.filters, updateFilters]);

  const clearFilters = useCallback(() => {
    updateFilters({
      searchTerm: '',
      filters: {},
      page: 1,
    });
  }, [updateFilters]);

  const setPage = useCallback((page: number) => {
    updateFilters({ page });
  }, [updateFilters]);

  const setLimit = useCallback((limit: number) => {
    updateFilters({ limit, page: 1 }); // Reset page when changing limit
  }, [updateFilters]);

  const resetPagination = useCallback(() => {
    updateFilters({ page: 1 });
  }, [updateFilters]);

  // Check if there are active filters
  const hasActiveFilters = useMemo(() => {
    return (
      filters.searchTerm.length > 0 ||
      Object.keys(filters.filters).length > 0 ||
      filters.sortBy !== initialState.sortBy ||
      filters.sortOrder !== initialState.sortOrder
    );
  }, [filters, initialState]);

  // Get query parameters for API calls
  const getQueryParams = useCallback(() => {
    const params: Record<string, any> = {
      page: filters.page,
      limit: filters.limit,
    };

    if (filters.searchTerm) {
      params.searchTerm = filters.searchTerm;
    }

    if (filters.sortBy) {
      params.sortBy = filters.sortBy;
      params.sortOrder = filters.sortOrder;
    }

    // Add custom filters
    Object.entries(filters.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params[key] = value;
      }
    });

    return params;
  }, [filters]);

  return {
    filters,
    setSearchTerm,
    setSortBy,
    setSortOrder,
    setFilter,
    removeFilter,
    clearFilters,
    setPage,
    setLimit,
    resetPagination,
    hasActiveFilters,
    getQueryParams,
  };
}

// ============================================================================
// FILTER UTILITIES
// ============================================================================

/**
 * Filter utilities for common patterns
 */
export const FilterUtils = {
  /**
   * Create a debounced search handler
   */
  createDebouncedSearch: (
    callback: (term: string) => void,
    delay: number = 300
  ) => {
    let timeoutId: NodeJS.Timeout;
    
    return (term: string) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => callback(term), delay);
    };
  },

  /**
   * Parse URL search params to filter state
   */
  parseUrlParams: (searchParams: URLSearchParams): Partial<FilterState> => {
    const filters: Partial<FilterState> = {};

    if (searchParams.has('search')) {
      filters.searchTerm = searchParams.get('search') || '';
    }

    if (searchParams.has('sortBy')) {
      filters.sortBy = searchParams.get('sortBy') || '';
    }

    if (searchParams.has('sortOrder')) {
      filters.sortOrder = (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc';
    }

    if (searchParams.has('page')) {
      filters.page = parseInt(searchParams.get('page') || '1', 10);
    }

    if (searchParams.has('limit')) {
      filters.limit = parseInt(searchParams.get('limit') || '10', 10);
    }

    return filters;
  },

  /**
   * Convert filter state to URL search params
   */
  toUrlParams: (filters: FilterState): URLSearchParams => {
    const params = new URLSearchParams();

    if (filters.searchTerm) {
      params.set('search', filters.searchTerm);
    }

    if (filters.sortBy) {
      params.set('sortBy', filters.sortBy);
      params.set('sortOrder', filters.sortOrder);
    }

    if (filters.page > 1) {
      params.set('page', filters.page.toString());
    }

    if (filters.limit !== 10) {
      params.set('limit', filters.limit.toString());
    }

    // Add custom filters
    Object.entries(filters.filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, String(value));
      }
    });

    return params;
  },
} as const;
