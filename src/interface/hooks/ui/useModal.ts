/**
 * useModal Hook
 * 
 * React hook for modal state management.
 * Provides utilities for opening, closing, and managing modal state.
 * 
 * Architecture Rules:
 * - UI hook belongs to Interface Layer
 * - Handles UI state and interactions only
 * - Independent of data layer operations
 * - Provides consistent modal patterns
 */

import { useState, useCallback } from 'react';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseModalOptions {
  defaultOpen?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
}

export interface UseModalResult {
  isOpen: boolean;
  open: () => void;
  close: () => void;
  toggle: () => void;
  setOpen: (open: boolean) => void;
}

// ============================================================================
// MODAL HOOK
// ============================================================================

/**
 * Hook for managing modal state
 */
export function useModal(options: UseModalOptions = {}): UseModalResult {
  const { defaultOpen = false, onOpen, onClose } = options;
  
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const open = useCallback(() => {
    setIsOpen(true);
    onOpen?.();
  }, [onOpen]);

  const close = useCallback(() => {
    setIsOpen(false);
    onClose?.();
  }, [onClose]);

  const toggle = useCallback(() => {
    if (isOpen) {
      close();
    } else {
      open();
    }
  }, [isOpen, open, close]);

  const setOpen = useCallback((open: boolean) => {
    if (open) {
      setIsOpen(true);
      onOpen?.();
    } else {
      setIsOpen(false);
      onClose?.();
    }
  }, [onOpen, onClose]);

  return {
    isOpen,
    open,
    close,
    toggle,
    setOpen,
  };
}

// ============================================================================
// MODAL UTILITIES
// ============================================================================

/**
 * Modal utilities for common patterns
 */
export const ModalUtils = {
  /**
   * Create a confirmation modal handler
   */
  createConfirmationHandler: (
    message: string,
    onConfirm: () => void,
    onCancel?: () => void
  ) => {
    return () => {
      if (window.confirm(message)) {
        onConfirm();
      } else {
        onCancel?.();
      }
    };
  },

  /**
   * Handle escape key to close modal
   */
  handleEscapeKey: (callback: () => void) => {
    return (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        callback();
      }
    };
  },
} as const;
