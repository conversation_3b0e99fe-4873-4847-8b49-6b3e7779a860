/**
 * usePagination Hook
 * 
 * React hook for pagination logic.
 * Provides utilities for managing pagination state and navigation.
 * 
 * Architecture Rules:
 * - UI hook belongs to Interface Layer
 * - Handles UI state and interactions only
 * - Independent of data layer operations
 * - Provides consistent pagination patterns
 */

import { useState, useCallback, useMemo } from 'react';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface UsePaginationOptions {
  defaultPage?: number;
  defaultLimit?: number;
  total?: number;
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
}

export interface UsePaginationResult {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  isFirstPage: boolean;
  isLastPage: boolean;
  startIndex: number;
  endIndex: number;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setTotal: (total: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  firstPage: () => void;
  lastPage: () => void;
  getPageNumbers: (maxVisible?: number) => (number | '...')[];
  getPageInfo: () => string;
}

// ============================================================================
// PAGINATION HOOK
// ============================================================================

/**
 * Hook for managing pagination state
 */
export function usePagination(options: UsePaginationOptions = {}): UsePaginationResult {
  const {
    defaultPage = 1,
    defaultLimit = 10,
    total: initialTotal = 0,
    onPageChange,
    onLimitChange,
  } = options;

  const [page, setPageState] = useState(defaultPage);
  const [limit, setLimitState] = useState(defaultLimit);
  const [total, setTotal] = useState(initialTotal);

  // Calculate derived values
  const totalPages = useMemo(() => {
    return Math.ceil(total / limit);
  }, [total, limit]);

  const hasNextPage = useMemo(() => {
    return page < totalPages;
  }, [page, totalPages]);

  const hasPreviousPage = useMemo(() => {
    return page > 1;
  }, [page]);

  const isFirstPage = useMemo(() => {
    return page === 1;
  }, [page]);

  const isLastPage = useMemo(() => {
    return page === totalPages;
  }, [page, totalPages]);

  const startIndex = useMemo(() => {
    return (page - 1) * limit + 1;
  }, [page, limit]);

  const endIndex = useMemo(() => {
    return Math.min(page * limit, total);
  }, [page, limit, total]);

  // Page navigation functions
  const setPage = useCallback((newPage: number) => {
    const validPage = Math.max(1, Math.min(newPage, totalPages));
    setPageState(validPage);
    onPageChange?.(validPage);
  }, [totalPages, onPageChange]);

  const setLimit = useCallback((newLimit: number) => {
    const validLimit = Math.max(1, newLimit);
    setLimitState(validLimit);
    setPageState(1); // Reset to first page when changing limit
    onLimitChange?.(validLimit);
  }, [onLimitChange]);

  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setPage(page + 1);
    }
  }, [hasNextPage, page, setPage]);

  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setPage(page - 1);
    }
  }, [hasPreviousPage, page, setPage]);

  const firstPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  const lastPage = useCallback(() => {
    setPage(totalPages);
  }, [setPage, totalPages]);

  // Generate page numbers for pagination UI
  const getPageNumbers = useCallback((maxVisible: number = 7): (number | '...')[] => {
    if (totalPages <= maxVisible) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    const pages: (number | '...')[] = [];
    const halfVisible = Math.floor(maxVisible / 2);

    if (page <= halfVisible + 1) {
      // Show pages from start
      for (let i = 1; i <= maxVisible - 2; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    } else if (page >= totalPages - halfVisible) {
      // Show pages from end
      pages.push(1);
      pages.push('...');
      for (let i = totalPages - maxVisible + 3; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show pages around current page
      pages.push(1);
      pages.push('...');
      for (let i = page - halfVisible + 1; i <= page + halfVisible - 1; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    }

    return pages;
  }, [page, totalPages]);

  // Get pagination info text
  const getPageInfo = useCallback((): string => {
    if (total === 0) {
      return 'Không có dữ liệu';
    }
    return `Hiển thị ${startIndex}-${endIndex} trong tổng số ${total} mục`;
  }, [startIndex, endIndex, total]);

  return {
    page,
    limit,
    total,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    isFirstPage,
    isLastPage,
    startIndex,
    endIndex,
    setPage,
    setLimit,
    setTotal,
    nextPage,
    previousPage,
    firstPage,
    lastPage,
    getPageNumbers,
    getPageInfo,
  };
}

// ============================================================================
// PAGINATION UTILITIES
// ============================================================================

/**
 * Pagination utilities for common patterns
 */
export const PaginationUtils = {
  /**
   * Calculate offset for API calls
   */
  getOffset: (page: number, limit: number): number => {
    return (page - 1) * limit;
  },

  /**
   * Calculate page from offset
   */
  getPageFromOffset: (offset: number, limit: number): number => {
    return Math.floor(offset / limit) + 1;
  },

  /**
   * Get common page size options
   */
  getPageSizeOptions: (): number[] => {
    return [10, 25, 50, 100];
  },

  /**
   * Validate pagination parameters
   */
  validateParams: (page: number, limit: number, total: number) => {
    const validPage = Math.max(1, Math.min(page, Math.ceil(total / limit)));
    const validLimit = Math.max(1, Math.min(limit, 100)); // Max 100 items per page
    
    return {
      page: validPage,
      limit: validLimit,
      isValid: page === validPage && limit === validLimit,
    };
  },
} as const;
