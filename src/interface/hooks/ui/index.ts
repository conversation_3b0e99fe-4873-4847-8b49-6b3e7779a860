/**
 * UI Hooks - Central Export
 *
 * This module exports all UI hooks for the Interface Layer.
 * UI hooks handle UI state management, interactions, and utilities.
 *
 * Architecture Rules:
 * - UI hooks belong to Interface Layer
 * - Handle UI-specific state and interactions
 * - Provide utilities for common UI patterns
 * - Independent of data layer operations
 */

// Re-export existing hooks
export { useIsMobile } from './use-mobile';
export { useToast } from './use-toast';

// Export new UI hooks
export { useModal, ModalUtils } from './useModal';
export { useFilters, FilterUtils } from './useFilters';
export { usePagination, PaginationUtils } from './usePagination';
export { useSearch, SearchUtils } from './useSearch';

// Export UI hook types
export type * from './use-mobile';
export type * from './use-toast';
export type * from './useModal';
export type * from './useFilters';
export type * from './usePagination';
export type * from './useSearch';
