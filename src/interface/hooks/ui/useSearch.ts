/**
 * useSearch Hook
 * 
 * React hook for search functionality with debouncing.
 * Provides utilities for managing search state and operations.
 * 
 * Architecture Rules:
 * - UI hook belongs to Interface Layer
 * - Handles UI state and interactions only
 * - Independent of data layer operations
 * - Provides consistent search patterns
 */

import { useState, useCallback, useEffect, useRef } from 'react';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseSearchOptions {
  defaultQuery?: string;
  debounceMs?: number;
  minLength?: number;
  onSearch?: (query: string) => void;
  onClear?: () => void;
}

export interface UseSearchResult {
  query: string;
  debouncedQuery: string;
  isSearching: boolean;
  setQuery: (query: string) => void;
  clearQuery: () => void;
  search: (query?: string) => void;
  hasQuery: boolean;
  isMinLength: boolean;
}

// ============================================================================
// SEARCH HOOK
// ============================================================================

/**
 * Hook for managing search state with debouncing
 */
export function useSearch(options: UseSearchOptions = {}): UseSearchResult {
  const {
    defaultQuery = '',
    debounceMs = 300,
    minLength = 1,
    onSearch,
    onClear,
  } = options;

  const [query, setQueryState] = useState(defaultQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(defaultQuery);
  const [isSearching, setIsSearching] = useState(false);
  
  const debounceRef = useRef<NodeJS.Timeout>();
  const searchRef = useRef(onSearch);
  const clearRef = useRef(onClear);

  // Update refs to avoid stale closures
  useEffect(() => {
    searchRef.current = onSearch;
    clearRef.current = onClear;
  }, [onSearch, onClear]);

  // Debounce search query
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    setIsSearching(true);

    debounceRef.current = setTimeout(() => {
      setDebouncedQuery(query);
      setIsSearching(false);
      
      if (query.length >= minLength) {
        searchRef.current?.(query);
      } else if (query.length === 0) {
        clearRef.current?.();
      }
    }, debounceMs);

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [query, debounceMs, minLength]);

  const setQuery = useCallback((newQuery: string) => {
    setQueryState(newQuery);
  }, []);

  const clearQuery = useCallback(() => {
    setQueryState('');
    setDebouncedQuery('');
    setIsSearching(false);
    clearRef.current?.();
  }, []);

  const search = useCallback((searchQuery?: string) => {
    const queryToSearch = searchQuery ?? query;
    if (queryToSearch.length >= minLength) {
      setDebouncedQuery(queryToSearch);
      searchRef.current?.(queryToSearch);
    }
  }, [query, minLength]);

  const hasQuery = query.length > 0;
  const isMinLength = query.length >= minLength;

  return {
    query,
    debouncedQuery,
    isSearching,
    setQuery,
    clearQuery,
    search,
    hasQuery,
    isMinLength,
  };
}

// ============================================================================
// SEARCH UTILITIES
// ============================================================================

/**
 * Search utilities for common patterns
 */
export const SearchUtils = {
  /**
   * Highlight search terms in text
   */
  highlightText: (text: string, searchTerm: string): string => {
    if (!searchTerm.trim()) return text;
    
    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  },

  /**
   * Create search filter function
   */
  createFilter: <T>(
    searchFields: (keyof T)[],
    caseSensitive: boolean = false
  ) => {
    return (items: T[], searchTerm: string): T[] => {
      if (!searchTerm.trim()) return items;
      
      const term = caseSensitive ? searchTerm : searchTerm.toLowerCase();
      
      return items.filter(item => {
        return searchFields.some(field => {
          const value = String(item[field] || '');
          const searchValue = caseSensitive ? value : value.toLowerCase();
          return searchValue.includes(term);
        });
      });
    };
  },

  /**
   * Extract search suggestions from data
   */
  extractSuggestions: <T>(
    items: T[],
    field: keyof T,
    maxSuggestions: number = 10
  ): string[] => {
    const suggestions = new Set<string>();
    
    items.forEach(item => {
      const value = String(item[field] || '').trim();
      if (value) {
        suggestions.add(value);
      }
    });
    
    return Array.from(suggestions).slice(0, maxSuggestions);
  },

  /**
   * Fuzzy search implementation
   */
  fuzzySearch: (text: string, pattern: string): boolean => {
    const textLower = text.toLowerCase();
    const patternLower = pattern.toLowerCase();
    
    let textIndex = 0;
    let patternIndex = 0;
    
    while (textIndex < textLower.length && patternIndex < patternLower.length) {
      if (textLower[textIndex] === patternLower[patternIndex]) {
        patternIndex++;
      }
      textIndex++;
    }
    
    return patternIndex === patternLower.length;
  },

  /**
   * Calculate search relevance score
   */
  calculateRelevance: (text: string, searchTerm: string): number => {
    if (!searchTerm.trim()) return 0;
    
    const textLower = text.toLowerCase();
    const termLower = searchTerm.toLowerCase();
    
    // Exact match gets highest score
    if (textLower === termLower) return 100;
    
    // Starts with search term gets high score
    if (textLower.startsWith(termLower)) return 80;
    
    // Contains search term gets medium score
    if (textLower.includes(termLower)) return 60;
    
    // Fuzzy match gets low score
    if (SearchUtils.fuzzySearch(text, searchTerm)) return 30;
    
    return 0;
  },

  /**
   * Sort search results by relevance
   */
  sortByRelevance: <T>(
    items: T[],
    searchTerm: string,
    getSearchText: (item: T) => string
  ): T[] => {
    return items
      .map(item => ({
        item,
        relevance: SearchUtils.calculateRelevance(getSearchText(item), searchTerm),
      }))
      .filter(({ relevance }) => relevance > 0)
      .sort((a, b) => b.relevance - a.relevance)
      .map(({ item }) => item);
  },
} as const;
