/**
 * useAnalytics Hook
 * 
 * React Query hook for analytics data management.
 * Integrates analyticsRepository with React Query for caching and state management.
 * Uses Zod validators from Interface utils for analytics query validation.
 * 
 * Architecture Rules:
 * - Data hook belongs to Interface Layer
 * - Uses analyticsRepository from Data Layer for business logic
 * - Uses Zod validators from Interface utils
 * - Provides real-time updates for dashboard data
 * - Integrates with React Query for caching
 */

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { analyticsRepository } from '@/data';
import { ANALYTICS_QUERY_KEYS } from '@/data';
import { AnalyticsValidation, ValidationUtils } from '@/interface/utils/validation';
import type {
  DashboardStat,
  QuickStat,
  AnalyticsData,
  UserGrowthData,
  CreditUsageData,
  ContentCreationData,
  SystemPerformanceData,
  AnalyticsSummary,
  AnalyticsQuery,
  MetricComparison,
  TopPerformers,
  AnalyticsExportConfig,
} from '@/models';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseAnalyticsOptions {
  enabled?: boolean;
  refetchInterval?: number;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export interface UseDashboardStatsResult {
  stats: DashboardStat[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseQuickStatsResult {
  quickStats: QuickStat[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseAnalyticsDataResult {
  data: AnalyticsData | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

// ============================================================================
// DASHBOARD STATS HOOK
// ============================================================================

/**
 * Hook for fetching dashboard statistics
 */
export function useDashboardStats(options?: UseAnalyticsOptions): UseDashboardStatsResult {
  const query = useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.dashboardStats(),
    queryFn: async (): Promise<DashboardStat[]> => {
      const response = await analyticsRepository.getDashboardStats();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch dashboard stats');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 30 * 1000, // Refresh every 30 seconds
    staleTime: options?.staleTime || 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  return {
    stats: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// QUICK STATS HOOK
// ============================================================================

/**
 * Hook for fetching quick statistics
 */
export function useQuickStats(options?: UseAnalyticsOptions): UseQuickStatsResult {
  const query = useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.quickStats(),
    queryFn: async (): Promise<QuickStat[]> => {
      const response = await analyticsRepository.getQuickStats();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch quick stats');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 60 * 1000, // Refresh every minute
    staleTime: options?.staleTime || 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  return {
    quickStats: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// ANALYTICS DATA HOOK
// ============================================================================

/**
 * Hook for fetching analytics data with query parameters
 */
export function useAnalyticsData(
  analyticsQuery?: AnalyticsQuery,
  options?: UseAnalyticsOptions
): UseAnalyticsDataResult {
  // Validate query parameters
  const validatedQuery = analyticsQuery 
    ? ValidationUtils.safeParse(AnalyticsValidation.query, analyticsQuery)
    : undefined;

  const query = useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.data(validatedQuery || {}),
    queryFn: async (): Promise<AnalyticsData> => {
      const response = await analyticsRepository.getAnalyticsData(validatedQuery);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch analytics data');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// USER GROWTH DATA HOOK
// ============================================================================

/**
 * Hook for fetching user growth data
 */
export function useUserGrowthData(
  period: 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseAnalyticsOptions
) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.userGrowth(period),
    queryFn: async (): Promise<UserGrowthData[]> => {
      const response = await analyticsRepository.getUserGrowthData(period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch user growth data');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// CREDIT USAGE DATA HOOK
// ============================================================================

/**
 * Hook for fetching credit usage data
 */
export function useCreditUsageData(
  period: 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseAnalyticsOptions
) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.creditUsage(period),
    queryFn: async (): Promise<CreditUsageData[]> => {
      const response = await analyticsRepository.getCreditUsageData(period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch credit usage data');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// CONTENT CREATION DATA HOOK
// ============================================================================

/**
 * Hook for fetching content creation data
 */
export function useContentCreationData(
  period: 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseAnalyticsOptions
) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.contentCreation(period),
    queryFn: async (): Promise<ContentCreationData[]> => {
      const response = await analyticsRepository.getContentCreationData(period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch content creation data');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// SYSTEM PERFORMANCE DATA HOOK
// ============================================================================

/**
 * Hook for fetching system performance data
 */
export function useSystemPerformanceData(options?: UseAnalyticsOptions) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.systemPerformance(),
    queryFn: async (): Promise<SystemPerformanceData> => {
      const response = await analyticsRepository.getSystemPerformanceData();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch system performance data');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 30 * 1000, // Refresh every 30 seconds
    staleTime: options?.staleTime || 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// ANALYTICS SUMMARY HOOK
// ============================================================================

/**
 * Hook for fetching analytics summary
 */
export function useAnalyticsSummary(
  period: 'today' | 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseAnalyticsOptions
) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.summary(period),
    queryFn: async (): Promise<AnalyticsSummary> => {
      const response = await analyticsRepository.getAnalyticsSummary(period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch analytics summary');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// METRIC COMPARISON HOOK
// ============================================================================

/**
 * Hook for fetching metric comparisons
 */
export function useMetricComparison(
  metrics: string[],
  period: 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseAnalyticsOptions
) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.metricComparison(metrics, period),
    queryFn: async (): Promise<MetricComparison[]> => {
      const response = await analyticsRepository.getMetricComparison(metrics, period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch metric comparison');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && metrics.length > 0,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// TOP PERFORMERS HOOK
// ============================================================================

/**
 * Hook for fetching top performers
 */
export function useTopPerformers(
  category: 'users' | 'content' | 'credits',
  limit: number = 10,
  options?: UseAnalyticsOptions
) {
  return useQuery({
    queryKey: ANALYTICS_QUERY_KEYS.topPerformers(category, limit),
    queryFn: async (): Promise<TopPerformers> => {
      const response = await analyticsRepository.getTopPerformers(category, limit);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch top performers');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}
