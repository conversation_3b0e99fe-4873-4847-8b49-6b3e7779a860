/**
 * useNotifications Hook
 * 
 * React Query hook for notification management with validation.
 * Integrates notificationRepository with React Query for caching and state management.
 * Uses Zod validators from Interface utils for notification validation.
 * 
 * Architecture Rules:
 * - Data hook belongs to Interface Layer
 * - Uses notificationRepository from Data Layer for business logic
 * - Uses Zod validators from Interface utils
 * - Provides real-time updates for notifications
 * - Integrates with React Query for caching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notificationRepository } from '@/data';
import { NOTIFICATION_QUERY_KEYS } from '@/data';
import { NotificationValidation, ValidationUtils } from '@/interface/utils/validation';
import type {
  Notification,
  NotificationAction,
  NotificationAttachment,
  NotificationPreferences,
  NotificationTemplate,
  NotificationCampaign,
  NotificationDeliveryLog,
  NotificationAnalytics,
  CreateNotificationPayload,
  NotificationSearchParams,
  ApiResponse,
} from '@/models';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseNotificationsOptions {
  enabled?: boolean;
  refetchInterval?: number;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export interface UseNotificationsResult {
  notifications: Notification[];
  total: number;
  page: number;
  totalPages: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UseNotificationResult {
  notification: Notification | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseNotificationMutationsResult {
  createNotification: {
    mutate: (payload: CreateNotificationPayload) => void;
    mutateAsync: (payload: CreateNotificationPayload) => Promise<Notification>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  markAsRead: {
    mutate: (id: number) => void;
    mutateAsync: (id: number) => Promise<Notification>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  deleteNotification: {
    mutate: (id: number) => void;
    mutateAsync: (id: number) => Promise<{ id: number }>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
}

// ============================================================================
// NOTIFICATIONS LIST HOOK
// ============================================================================

/**
 * Hook for fetching notifications list with filtering and pagination
 */
export function useNotifications(
  searchParams?: NotificationSearchParams,
  options?: UseNotificationsOptions
): UseNotificationsResult {
  const queryClient = useQueryClient();

  // Validate search parameters
  const validatedParams = searchParams 
    ? ValidationUtils.safeParse(NotificationValidation.search, searchParams)
    : undefined;

  const query = useQuery({
    queryKey: NOTIFICATION_QUERY_KEYS.list(validatedParams || {}),
    queryFn: async (): Promise<{ notifications: Notification[]; total: number; page: number; totalPages: number }> => {
      const response = await notificationRepository.getNotifications(validatedParams);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch notifications');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 30 * 1000, // Refresh every 30 seconds
    staleTime: options?.staleTime || 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  const currentPage = validatedParams?.page || 1;
  const totalPages = query.data?.totalPages || 0;

  return {
    notifications: query.data?.notifications || [],
    total: query.data?.total || 0,
    page: query.data?.page || 1,
    totalPages,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
}

// ============================================================================
// SINGLE NOTIFICATION HOOK
// ============================================================================

/**
 * Hook for fetching a single notification by ID
 */
export function useNotification(id: number, options?: UseNotificationsOptions): UseNotificationResult {
  const query = useQuery({
    queryKey: NOTIFICATION_QUERY_KEYS.detail(id),
    queryFn: async (): Promise<Notification> => {
      const response = await notificationRepository.getNotificationById(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch notification');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!id,
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
  });

  return {
    notification: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// USER NOTIFICATIONS HOOK
// ============================================================================

/**
 * Hook for fetching notifications for a specific user
 */
export function useUserNotifications(
  userId: number,
  searchParams?: Omit<NotificationSearchParams, 'targetUserIds'>,
  options?: UseNotificationsOptions
) {
  const fullParams: NotificationSearchParams = {
    ...searchParams,
    targetUserIds: [userId],
  };

  return useNotifications(fullParams, options);
}

// ============================================================================
// UNREAD NOTIFICATIONS HOOK
// ============================================================================

/**
 * Hook for fetching unread notifications count
 */
export function useUnreadNotificationsCount(userId?: number, options?: UseNotificationsOptions) {
  return useQuery({
    queryKey: NOTIFICATION_QUERY_KEYS.unreadCount(userId),
    queryFn: async (): Promise<number> => {
      const response = await notificationRepository.getUnreadNotificationsCount(userId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch unread notifications count');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 30 * 1000, // Refresh every 30 seconds
    staleTime: options?.staleTime || 30 * 1000, // 30 seconds
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// NOTIFICATION PREFERENCES HOOK
// ============================================================================

/**
 * Hook for fetching notification preferences
 */
export function useNotificationPreferences(userId: number, options?: UseNotificationsOptions) {
  return useQuery({
    queryKey: NOTIFICATION_QUERY_KEYS.preferences(userId),
    queryFn: async (): Promise<NotificationPreferences> => {
      const response = await notificationRepository.getNotificationPreferences(userId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch notification preferences');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!userId,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// NOTIFICATION TEMPLATES HOOK
// ============================================================================

/**
 * Hook for fetching notification templates
 */
export function useNotificationTemplates(options?: UseNotificationsOptions) {
  return useQuery({
    queryKey: NOTIFICATION_QUERY_KEYS.templates(),
    queryFn: async (): Promise<NotificationTemplate[]> => {
      const response = await notificationRepository.getNotificationTemplates();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch notification templates');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// NOTIFICATION ANALYTICS HOOK
// ============================================================================

/**
 * Hook for fetching notification analytics
 */
export function useNotificationAnalytics(
  period: 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseNotificationsOptions
) {
  return useQuery({
    queryKey: NOTIFICATION_QUERY_KEYS.analytics(period),
    queryFn: async (): Promise<NotificationAnalytics> => {
      const response = await notificationRepository.getNotificationAnalytics(period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch notification analytics');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// NOTIFICATION MUTATIONS HOOK
// ============================================================================

/**
 * Hook for notification mutations (create, mark as read, delete)
 */
export function useNotificationMutations(): UseNotificationMutationsResult {
  const queryClient = useQueryClient();

  // Create notification mutation
  const createNotificationMutation = useMutation({
    mutationFn: async (payload: CreateNotificationPayload): Promise<Notification> => {
      // Validate payload with Zod
      const validationResult = ValidationUtils.validate(NotificationValidation.create, payload);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await notificationRepository.createNotification(validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to create notification');
      }
      return response.data;
    },
    onSuccess: (newNotification) => {
      // Invalidate and refetch notifications list
      queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.lists() });

      // Add new notification to cache
      queryClient.setQueryData(NOTIFICATION_QUERY_KEYS.detail(newNotification.id), newNotification);

      // Update unread count for affected users
      if (newNotification.targetUserIds) {
        newNotification.targetUserIds.forEach(userId => {
          queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.unreadCount(userId) });
        });
      } else {
        // If targeting all users, invalidate all unread counts
        queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey;
            return Array.isArray(key) && key[0] === 'notifications' && key[1] === 'unreadCount';
          }
        });
      }

      // Update analytics
      queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.analytics() });
    },
  });

  // Mark as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: async (id: number): Promise<Notification> => {
      const response = await notificationRepository.markNotificationAsRead(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to mark notification as read');
      }
      return response.data;
    },
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: NOTIFICATION_QUERY_KEYS.detail(id) });

      // Snapshot previous value
      const previousNotification = queryClient.getQueryData<Notification>(NOTIFICATION_QUERY_KEYS.detail(id));

      // Optimistically update notification
      if (previousNotification) {
        queryClient.setQueryData<Notification>(NOTIFICATION_QUERY_KEYS.detail(id), {
          ...previousNotification,
          status: 'read',
          readAt: new Date().toISOString(),
        });
      }

      return { previousNotification };
    },
    onError: (err, id, context) => {
      // Rollback optimistic update
      if (context?.previousNotification) {
        queryClient.setQueryData(NOTIFICATION_QUERY_KEYS.detail(id), context.previousNotification);
      }
    },
    onSuccess: (updatedNotification) => {
      // Update cache with server response
      queryClient.setQueryData(NOTIFICATION_QUERY_KEYS.detail(updatedNotification.id), updatedNotification);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.lists() });

      // Update unread count for affected users
      if (updatedNotification.targetUserIds) {
        updatedNotification.targetUserIds.forEach(userId => {
          queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.unreadCount(userId) });
        });
      }
    },
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: async (id: number): Promise<{ id: number }> => {
      const response = await notificationRepository.deleteNotification(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete notification');
      }
      return response.data;
    },
    onSuccess: (deletedNotification, id) => {
      // Remove notification from cache
      queryClient.removeQueries({ queryKey: NOTIFICATION_QUERY_KEYS.detail(id) });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: NOTIFICATION_QUERY_KEYS.analytics() });

      // Update unread counts
      queryClient.invalidateQueries({
        predicate: (query) => {
          const key = query.queryKey;
          return Array.isArray(key) && key[0] === 'notifications' && key[1] === 'unreadCount';
        }
      });
    },
  });

  return {
    createNotification: {
      mutate: createNotificationMutation.mutate,
      mutateAsync: createNotificationMutation.mutateAsync,
      isLoading: createNotificationMutation.isPending,
      isError: createNotificationMutation.isError,
      error: createNotificationMutation.error,
    },
    markAsRead: {
      mutate: markAsReadMutation.mutate,
      mutateAsync: markAsReadMutation.mutateAsync,
      isLoading: markAsReadMutation.isPending,
      isError: markAsReadMutation.isError,
      error: markAsReadMutation.error,
    },
    deleteNotification: {
      mutate: deleteNotificationMutation.mutate,
      mutateAsync: deleteNotificationMutation.mutateAsync,
      isLoading: deleteNotificationMutation.isPending,
      isError: deleteNotificationMutation.isError,
      error: deleteNotificationMutation.error,
    },
  };
}
