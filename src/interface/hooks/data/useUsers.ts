/**
 * useUsers Hook
 * 
 * React Query hook for user data management with validation.
 * Integrates userRepository with React Query for caching and state management.
 * Uses Zod validators from Interface utils for data validation.
 * 
 * Architecture Rules:
 * - Data hook belongs to Interface Layer
 * - Uses userRepository from Data Layer for business logic
 * - Uses Zod validators from Interface utils
 * - Provides optimistic updates for better UX
 * - Integrates with React Query for caching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { userRepository } from '@/data';
import { USER_QUERY_KEYS } from '@/data';
import { UserValidation, ValidationUtils } from '@/interface/utils/validation';
import type {
  User,
  UserProfile,
  UserCreditSummary,
  UserContentSummary,
  CreateUserPayload,
  UpdateUserPayload,
  UserSearchParams,
  UserListResponse,
  ApiResponse,
} from '@/models';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseUsersOptions {
  enabled?: boolean;
  refetchInterval?: number;
  staleTime?: number;
}

export interface UseUserOptions {
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
}

export interface UseUsersResult {
  users: User[];
  total: number;
  page: number;
  totalPages: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UseUserResult {
  user: User | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseUserMutationsResult {
  createUser: {
    mutate: (payload: CreateUserPayload) => void;
    mutateAsync: (payload: CreateUserPayload) => Promise<User>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  updateUser: {
    mutate: (params: { id: number; payload: UpdateUserPayload }) => void;
    mutateAsync: (params: { id: number; payload: UpdateUserPayload }) => Promise<User>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  deleteUser: {
    mutate: (id: number) => void;
    mutateAsync: (id: number) => Promise<{ id: number }>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
}

// ============================================================================
// USERS LIST HOOK
// ============================================================================

/**
 * Hook for fetching users list with filtering and pagination
 */
export function useUsers(
  searchParams?: UserSearchParams,
  options?: UseUsersOptions
): UseUsersResult {
  const queryClient = useQueryClient();

  // Validate search parameters
  const validatedParams = searchParams 
    ? ValidationUtils.safeParse(UserValidation.search, searchParams)
    : undefined;

  const query = useQuery({
    queryKey: USER_QUERY_KEYS.list(validatedParams || {}),
    queryFn: async (): Promise<UserListResponse> => {
      const response = await userRepository.getUsers(validatedParams);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch users');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const currentPage = validatedParams?.page || 1;
  const totalPages = query.data?.totalPages || 0;

  return {
    users: query.data?.users || [],
    total: query.data?.total || 0,
    page: query.data?.page || 1,
    totalPages,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
}

// ============================================================================
// SINGLE USER HOOK
// ============================================================================

/**
 * Hook for fetching a single user by ID
 */
export function useUser(id: number, options?: UseUserOptions): UseUserResult {
  const query = useQuery({
    queryKey: USER_QUERY_KEYS.detail(id),
    queryFn: async (): Promise<User> => {
      const response = await userRepository.getUserById(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch user');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!id,
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    user: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// USER PROFILE HOOKS
// ============================================================================

/**
 * Hook for fetching user profile
 */
export function useUserProfile(id: number, options?: UseUserOptions) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.preferences(id),
    queryFn: async (): Promise<UserProfile> => {
      const response = await userRepository.getUserProfile(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch user profile');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook for fetching user credit summary
 */
export function useUserCreditSummary(id: number, options?: UseUserOptions) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.creditStats(id),
    queryFn: async (): Promise<UserCreditSummary> => {
      const response = await userRepository.getUserCreditSummary(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch user credit summary');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes for credit data
  });
}

/**
 * Hook for fetching user content summary
 */
export function useUserContentSummary(id: number, options?: UseUserOptions) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.contentStats(id),
    queryFn: async (): Promise<UserContentSummary> => {
      const response = await userRepository.getUserContentSummary(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch user content summary');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!id,
    staleTime: 5 * 60 * 1000,
  });
}

// ============================================================================
// USERS BY STATUS HOOK
// ============================================================================

/**
 * Hook for fetching users by status
 */
export function useUsersByStatus(status: 'active' | 'premium' | 'inactive', options?: UseUsersOptions) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.list({ status }),
    queryFn: async (): Promise<User[]> => {
      const response = await userRepository.getUsersByStatus(status);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch users by status');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000,
  });
}

// ============================================================================
// USER MUTATIONS HOOK
// ============================================================================

/**
 * Hook for user mutations (create, update, delete)
 */
export function useUserMutations(): UseUserMutationsResult {
  const queryClient = useQueryClient();

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: async (payload: CreateUserPayload): Promise<User> => {
      // Validate payload with Zod
      const validationResult = ValidationUtils.validate(UserValidation.create, payload);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await userRepository.createUser(validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to create user');
      }
      return response.data;
    },
    onSuccess: (newUser) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });

      // Add new user to cache
      queryClient.setQueryData(USER_QUERY_KEYS.detail(newUser.id), newUser);

      // Update users by status cache
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.list({ status: newUser.status }) });
    },
  });

  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: async ({ id, payload }: { id: number; payload: UpdateUserPayload }): Promise<User> => {
      // Validate payload with Zod
      const validationResult = ValidationUtils.validate(UserValidation.update, payload);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await userRepository.updateUser(id, validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update user');
      }
      return response.data;
    },
    onMutate: async ({ id, payload }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: USER_QUERY_KEYS.detail(id) });

      // Snapshot previous value
      const previousUser = queryClient.getQueryData<User>(USER_QUERY_KEYS.detail(id));

      // Optimistically update user
      if (previousUser) {
        queryClient.setQueryData<User>(USER_QUERY_KEYS.detail(id), {
          ...previousUser,
          ...payload,
        });
      }

      return { previousUser };
    },
    onError: (err, { id }, context) => {
      // Rollback optimistic update
      if (context?.previousUser) {
        queryClient.setQueryData(USER_QUERY_KEYS.detail(id), context.previousUser);
      }
    },
    onSuccess: (updatedUser) => {
      // Update cache with server response
      queryClient.setQueryData(USER_QUERY_KEYS.detail(updatedUser.id), updatedUser);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.preferences(updatedUser.id) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.creditStats(updatedUser.id) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.contentStats(updatedUser.id) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.list({ status: updatedUser.status }) });
    },
  });

  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: async (id: number): Promise<{ id: number }> => {
      const response = await userRepository.deleteUser(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete user');
      }
      return response.data;
    },
    onSuccess: (deletedUser, id) => {
      // Remove user from cache
      queryClient.removeQueries({ queryKey: USER_QUERY_KEYS.detail(id) });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.preferences(id) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.creditStats(id) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.contentStats(id) });

      // Invalidate all status queries since we don't know the user's status
      queryClient.invalidateQueries({
        predicate: (query) => {
          const key = query.queryKey;
          return Array.isArray(key) && key[0] === 'users' && key[1] === 'byStatus';
        }
      });
    },
  });

  return {
    createUser: {
      mutate: createUserMutation.mutate,
      mutateAsync: createUserMutation.mutateAsync,
      isLoading: createUserMutation.isPending,
      isError: createUserMutation.isError,
      error: createUserMutation.error,
    },
    updateUser: {
      mutate: updateUserMutation.mutate,
      mutateAsync: updateUserMutation.mutateAsync,
      isLoading: updateUserMutation.isPending,
      isError: updateUserMutation.isError,
      error: updateUserMutation.error,
    },
    deleteUser: {
      mutate: deleteUserMutation.mutate,
      mutateAsync: deleteUserMutation.mutateAsync,
      isLoading: deleteUserMutation.isPending,
      isError: deleteUserMutation.isError,
      error: deleteUserMutation.error,
    },
  };
}
