/**
 * useSystem Hook
 * 
 * React Query hook for system management with validation.
 * Integrates systemRepository with React Query for caching and state management.
 * Uses Zod validators from Interface utils for system configuration validation.
 * 
 * Architecture Rules:
 * - Data hook belongs to Interface Layer
 * - Uses systemRepository from Data Layer for business logic
 * - Uses Zod validators from Interface utils
 * - Provides system configuration and status management
 * - Integrates with React Query for caching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { systemRepository } from '@/data';
import { SYSTEM_QUERY_KEYS } from '@/data';
import { SystemValidation, ValidationUtils } from '@/interface/utils/validation';
import type {
  SystemConfig,
  SystemStatus,
  AdminUser,
  AuditLogEntry,
  FeatureFlags,
  SystemCreditConfig,
  ContentProcessingConfig,
  SecurityConfig,
  EmailConfig,
  StorageConfig,
  AnalyticsConfig,
  MaintenanceSettings,
  SystemLimits,
  MonitoringConfig,
  ApiResponse,
} from '@/models';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseSystemOptions {
  enabled?: boolean;
  refetchInterval?: number;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export interface UseSystemConfigResult {
  config: SystemConfig | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseSystemStatusResult {
  status: SystemStatus | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseSystemMutationsResult {
  updateConfig: {
    mutate: (config: Partial<SystemConfig>) => void;
    mutateAsync: (config: Partial<SystemConfig>) => Promise<SystemConfig>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  updateMaintenanceSettings: {
    mutate: (settings: MaintenanceSettings) => void;
    mutateAsync: (settings: MaintenanceSettings) => Promise<MaintenanceSettings>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  updateFeatureFlags: {
    mutate: (flags: Partial<FeatureFlags>) => void;
    mutateAsync: (flags: Partial<FeatureFlags>) => Promise<FeatureFlags>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
}

// ============================================================================
// SYSTEM CONFIG HOOK
// ============================================================================

/**
 * Hook for fetching system configuration
 */
export function useSystemConfig(options?: UseSystemOptions): UseSystemConfigResult {
  const query = useQuery({
    queryKey: SYSTEM_QUERY_KEYS.config(),
    queryFn: async (): Promise<SystemConfig> => {
      const response = await systemRepository.getSystemConfig();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch system config');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  return {
    config: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// SYSTEM STATUS HOOK
// ============================================================================

/**
 * Hook for fetching system status
 */
export function useSystemStatus(options?: UseSystemOptions): UseSystemStatusResult {
  const query = useQuery({
    queryKey: SYSTEM_QUERY_KEYS.status(),
    queryFn: async (): Promise<SystemStatus> => {
      const response = await systemRepository.getSystemStatus();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch system status');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 30 * 1000, // Refresh every 30 seconds
    staleTime: options?.staleTime || 1 * 60 * 1000, // 1 minute
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  return {
    status: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// FEATURE FLAGS HOOK
// ============================================================================

/**
 * Hook for fetching feature flags
 */
export function useFeatureFlags(options?: UseSystemOptions) {
  return useQuery({
    queryKey: SYSTEM_QUERY_KEYS.featureFlags(),
    queryFn: async (): Promise<FeatureFlags> => {
      const response = await systemRepository.getFeatureFlags();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch feature flags');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// MAINTENANCE SETTINGS HOOK
// ============================================================================

/**
 * Hook for fetching maintenance settings
 */
export function useMaintenanceSettings(options?: UseSystemOptions) {
  return useQuery({
    queryKey: SYSTEM_QUERY_KEYS.maintenance(),
    queryFn: async (): Promise<MaintenanceSettings> => {
      const response = await systemRepository.getMaintenanceSettings();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch maintenance settings');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// AUDIT LOG HOOK
// ============================================================================

/**
 * Hook for fetching audit log entries
 */
export function useAuditLog(
  filters?: {
    userId?: number;
    action?: string;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
  },
  options?: UseSystemOptions
) {
  return useQuery({
    queryKey: SYSTEM_QUERY_KEYS.auditLog(filters || {}),
    queryFn: async (): Promise<{ entries: AuditLogEntry[]; total: number; page: number; totalPages: number }> => {
      const response = await systemRepository.getAuditLog(filters);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch audit log');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// SYSTEM LIMITS HOOK
// ============================================================================

/**
 * Hook for fetching system limits
 */
export function useSystemLimits(options?: UseSystemOptions) {
  return useQuery({
    queryKey: SYSTEM_QUERY_KEYS.limits(),
    queryFn: async (): Promise<SystemLimits> => {
      const response = await systemRepository.getSystemLimits();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch system limits');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 15 * 60 * 1000, // 15 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// MONITORING CONFIG HOOK
// ============================================================================

/**
 * Hook for fetching monitoring configuration
 */
export function useMonitoringConfig(options?: UseSystemOptions) {
  return useQuery({
    queryKey: SYSTEM_QUERY_KEYS.monitoring(),
    queryFn: async (): Promise<MonitoringConfig> => {
      const response = await systemRepository.getMonitoringConfig();
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch monitoring config');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// SYSTEM MUTATIONS HOOK
// ============================================================================

/**
 * Hook for system mutations (update config, maintenance, feature flags)
 */
export function useSystemMutations(): UseSystemMutationsResult {
  const queryClient = useQueryClient();

  // Update system config mutation
  const updateConfigMutation = useMutation({
    mutationFn: async (config: Partial<SystemConfig>): Promise<SystemConfig> => {
      // Validate config with Zod
      const validationResult = ValidationUtils.validate(SystemValidation.config, config);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await systemRepository.updateSystemConfig(validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update system config');
      }
      return response.data;
    },
    onMutate: async (config) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: SYSTEM_QUERY_KEYS.config() });

      // Snapshot previous value
      const previousConfig = queryClient.getQueryData<SystemConfig>(SYSTEM_QUERY_KEYS.config());

      // Optimistically update config
      if (previousConfig) {
        queryClient.setQueryData<SystemConfig>(SYSTEM_QUERY_KEYS.config(), {
          ...previousConfig,
          ...config,
        });
      }

      return { previousConfig };
    },
    onError: (err, config, context) => {
      // Rollback optimistic update
      if (context?.previousConfig) {
        queryClient.setQueryData(SYSTEM_QUERY_KEYS.config(), context.previousConfig);
      }
    },
    onSuccess: (updatedConfig) => {
      // Update cache with server response
      queryClient.setQueryData(SYSTEM_QUERY_KEYS.config(), updatedConfig);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: SYSTEM_QUERY_KEYS.status() });
      queryClient.invalidateQueries({ queryKey: SYSTEM_QUERY_KEYS.limits() });
    },
  });

  // Update maintenance settings mutation
  const updateMaintenanceSettingsMutation = useMutation({
    mutationFn: async (settings: MaintenanceSettings): Promise<MaintenanceSettings> => {
      // Validate settings with Zod
      const validationResult = ValidationUtils.validate(SystemValidation.maintenance, settings);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await systemRepository.updateMaintenanceSettings(validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update maintenance settings');
      }
      return response.data;
    },
    onSuccess: (updatedSettings) => {
      // Update cache
      queryClient.setQueryData(SYSTEM_QUERY_KEYS.maintenance(), updatedSettings);

      // Invalidate system status
      queryClient.invalidateQueries({ queryKey: SYSTEM_QUERY_KEYS.status() });
    },
  });

  // Update feature flags mutation
  const updateFeatureFlagsMutation = useMutation({
    mutationFn: async (flags: Partial<FeatureFlags>): Promise<FeatureFlags> => {
      const response = await systemRepository.updateFeatureFlags(flags);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update feature flags');
      }
      return response.data;
    },
    onMutate: async (flags) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: SYSTEM_QUERY_KEYS.featureFlags() });

      // Snapshot previous value
      const previousFlags = queryClient.getQueryData<FeatureFlags>(SYSTEM_QUERY_KEYS.featureFlags());

      // Optimistically update flags
      if (previousFlags) {
        queryClient.setQueryData<FeatureFlags>(SYSTEM_QUERY_KEYS.featureFlags(), {
          ...previousFlags,
          ...flags,
        });
      }

      return { previousFlags };
    },
    onError: (err, flags, context) => {
      // Rollback optimistic update
      if (context?.previousFlags) {
        queryClient.setQueryData(SYSTEM_QUERY_KEYS.featureFlags(), context.previousFlags);
      }
    },
    onSuccess: (updatedFlags) => {
      // Update cache with server response
      queryClient.setQueryData(SYSTEM_QUERY_KEYS.featureFlags(), updatedFlags);

      // Invalidate system config and status
      queryClient.invalidateQueries({ queryKey: SYSTEM_QUERY_KEYS.config() });
      queryClient.invalidateQueries({ queryKey: SYSTEM_QUERY_KEYS.status() });
    },
  });

  return {
    updateConfig: {
      mutate: updateConfigMutation.mutate,
      mutateAsync: updateConfigMutation.mutateAsync,
      isLoading: updateConfigMutation.isPending,
      isError: updateConfigMutation.isError,
      error: updateConfigMutation.error,
    },
    updateMaintenanceSettings: {
      mutate: updateMaintenanceSettingsMutation.mutate,
      mutateAsync: updateMaintenanceSettingsMutation.mutateAsync,
      isLoading: updateMaintenanceSettingsMutation.isPending,
      isError: updateMaintenanceSettingsMutation.isError,
      error: updateMaintenanceSettingsMutation.error,
    },
    updateFeatureFlags: {
      mutate: updateFeatureFlagsMutation.mutate,
      mutateAsync: updateFeatureFlagsMutation.mutateAsync,
      isLoading: updateFeatureFlagsMutation.isPending,
      isError: updateFeatureFlagsMutation.isError,
      error: updateFeatureFlagsMutation.error,
    },
  };
}
