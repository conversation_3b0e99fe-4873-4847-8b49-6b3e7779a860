/**
 * useCredits Hook
 * 
 * React Query hook for credit management with validation.
 * Integrates creditRepository with React Query for caching and state management.
 * Uses Zod validators from Interface utils for credit operation validation.
 * 
 * Architecture Rules:
 * - Data hook belongs to Interface Layer
 * - Uses creditRepository from Data Layer for business logic
 * - Uses Zod validators from Interface utils
 * - Provides optimistic updates for credit operations
 * - Integrates with React Query for caching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { creditRepository } from '@/data';
import { CREDIT_QUERY_KEYS, USER_QUERY_KEYS } from '@/data';
import { CreditValidation, ValidationUtils } from '@/interface/utils/validation';
import type {
  CreditTransaction,
  CreditBalance,
  CreditUsageStats,
  CreditOperationPayload,
  CreditTransactionQuery,
  CreditTransactionHistoryResponse,
  ApiResponse,
} from '@/models';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseCreditTransactionsOptions {
  enabled?: boolean;
  refetchInterval?: number;
  staleTime?: number;
}

export interface UseCreditTransactionsResult {
  transactions: CreditTransaction[];
  total: number;
  page: number;
  totalPages: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UseCreditBalanceResult {
  balance: CreditBalance | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseCreditMutationsResult {
  addCredits: {
    mutate: (payload: CreditOperationPayload) => void;
    mutateAsync: (payload: CreditOperationPayload) => Promise<CreditTransaction>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  subtractCredits: {
    mutate: (payload: CreditOperationPayload) => void;
    mutateAsync: (payload: CreditOperationPayload) => Promise<CreditTransaction>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  useCredits: {
    mutate: (payload: CreditOperationPayload) => void;
    mutateAsync: (payload: CreditOperationPayload) => Promise<CreditTransaction>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  refundCredits: {
    mutate: (payload: CreditOperationPayload) => void;
    mutateAsync: (payload: CreditOperationPayload) => Promise<CreditTransaction>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
}

// ============================================================================
// CREDIT TRANSACTIONS HOOK
// ============================================================================

/**
 * Hook for fetching credit transactions with filtering and pagination
 */
export function useCreditTransactions(
  query?: CreditTransactionQuery,
  options?: UseCreditTransactionsOptions
): UseCreditTransactionsResult {
  const queryClient = useQueryClient();

  // Validate query parameters
  const validatedQuery = query 
    ? ValidationUtils.safeParse(CreditValidation.transactionQuery, query)
    : undefined;

  const queryResult = useQuery({
    queryKey: CREDIT_QUERY_KEYS.transactions(),
    queryFn: async (): Promise<CreditTransactionHistoryResponse> => {
      const response = await creditRepository.getCreditTransactions(validatedQuery);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch credit transactions');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval,
    staleTime: options?.staleTime || 2 * 60 * 1000, // 2 minutes for credit data
    refetchOnWindowFocus: false,
  });

  const currentPage = validatedQuery?.page || 1;
  const totalPages = queryResult.data?.totalPages || 0;

  return {
    transactions: queryResult.data?.transactions || [],
    total: queryResult.data?.total || 0,
    page: queryResult.data?.page || 1,
    totalPages,
    isLoading: queryResult.isLoading,
    isError: queryResult.isError,
    error: queryResult.error,
    refetch: queryResult.refetch,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
}

// ============================================================================
// USER CREDIT BALANCE HOOK
// ============================================================================

/**
 * Hook for fetching user credit balance
 */
export function useCreditBalance(userId: number, options?: UseCreditTransactionsOptions): UseCreditBalanceResult {
  const query = useQuery({
    queryKey: CREDIT_QUERY_KEYS.balance(userId),
    queryFn: async (): Promise<CreditBalance> => {
      const response = await creditRepository.getCreditBalance(userId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch credit balance');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!userId,
    refetchInterval: options?.refetchInterval || 30 * 1000, // Refresh every 30 seconds
    staleTime: options?.staleTime || 1 * 60 * 1000, // 1 minute for balance data
    refetchOnWindowFocus: true, // Always refetch balance on focus
  });

  return {
    balance: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// CREDIT USAGE STATS HOOK
// ============================================================================

/**
 * Hook for fetching credit usage statistics
 */
export function useCreditUsageStats(userId?: number, options?: UseCreditTransactionsOptions) {
  return useQuery({
    queryKey: CREDIT_QUERY_KEYS.userStats(userId || 0),
    queryFn: async (): Promise<CreditUsageStats> => {
      const response = await creditRepository.getCreditUsageStats(userId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch credit usage stats');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes for stats
    refetchOnWindowFocus: false,
  });
}

// ============================================================================
// USER CREDIT TRANSACTIONS HOOK
// ============================================================================

/**
 * Hook for fetching credit transactions for a specific user
 */
export function useUserCreditTransactions(
  userId: number,
  query?: Omit<CreditTransactionQuery, 'userId'>,
  options?: UseCreditTransactionsOptions
) {
  const fullQuery: CreditTransactionQuery = {
    ...query,
    userId,
  };

  return useCreditTransactions(fullQuery, options);
}

// ============================================================================
// CREDIT MUTATIONS HOOK
// ============================================================================

/**
 * Hook for credit mutations (add, subtract, use, refund)
 */
export function useCreditMutations(): UseCreditMutationsResult {
  const queryClient = useQueryClient();

  // Helper function to create credit mutation
  const createCreditMutation = (
    operationType: 'add' | 'subtract' | 'used' | 'refund',
    mutationFn: (payload: CreditOperationPayload) => Promise<ApiResponse<CreditTransaction>>
  ) => {
    return useMutation({
      mutationFn: async (payload: CreditOperationPayload): Promise<CreditTransaction> => {
        // Validate payload with Zod
        const validationResult = ValidationUtils.validate(CreditValidation.operation, {
          ...payload,
          type: operationType,
        });
        if (!validationResult.success) {
          throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
        }

        const response = await mutationFn(validationResult.data!);
        if (!response.success) {
          throw new Error(response.message || `Failed to ${operationType} credits`);
        }
        return response.data;
      },
      onMutate: async (payload) => {
        // Cancel outgoing refetches for user balance
        await queryClient.cancelQueries({ queryKey: CREDIT_QUERY_KEYS.balance(payload.userId) });

        // Snapshot previous balance
        const previousBalance = queryClient.getQueryData<CreditBalance>(
          CREDIT_QUERY_KEYS.balance(payload.userId)
        );

        // Optimistically update balance
        if (previousBalance) {
          const balanceChange = operationType === 'add' || operationType === 'refund' 
            ? payload.amount 
            : -payload.amount;
          
          queryClient.setQueryData<CreditBalance>(CREDIT_QUERY_KEYS.balance(payload.userId), {
            ...previousBalance,
            currentBalance: previousBalance.currentBalance + balanceChange,
          });
        }

        return { previousBalance };
      },
      onError: (err, payload, context) => {
        // Rollback optimistic update
        if (context?.previousBalance) {
          queryClient.setQueryData(
            CREDIT_QUERY_KEYS.balance(payload.userId), 
            context.previousBalance
          );
        }
      },
      onSuccess: (transaction) => {
        // Invalidate and refetch related queries
        queryClient.invalidateQueries({ queryKey: CREDIT_QUERY_KEYS.balance(transaction.userId) });
        queryClient.invalidateQueries({ queryKey: CREDIT_QUERY_KEYS.transactions() });
        queryClient.invalidateQueries({ queryKey: CREDIT_QUERY_KEYS.stats() });
        queryClient.invalidateQueries({ queryKey: CREDIT_QUERY_KEYS.userStats(transaction.userId) });

        // Invalidate user-related queries
        queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(transaction.userId) });
        queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.creditStats(transaction.userId) });
        queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
      },
    });
  };

  // Create mutations for each operation type
  const addCreditsMutation = createCreditMutation('add', creditRepository.addCredits.bind(creditRepository));
  const subtractCreditsMutation = createCreditMutation('subtract', creditRepository.subtractCredits.bind(creditRepository));
  const useCreditsMutation = createCreditMutation('used', creditRepository.useCredits.bind(creditRepository));
  const refundCreditsMutation = createCreditMutation('refund', creditRepository.refundCredits.bind(creditRepository));

  return {
    addCredits: {
      mutate: addCreditsMutation.mutate,
      mutateAsync: addCreditsMutation.mutateAsync,
      isLoading: addCreditsMutation.isPending,
      isError: addCreditsMutation.isError,
      error: addCreditsMutation.error,
    },
    subtractCredits: {
      mutate: subtractCreditsMutation.mutate,
      mutateAsync: subtractCreditsMutation.mutateAsync,
      isLoading: subtractCreditsMutation.isPending,
      isError: subtractCreditsMutation.isError,
      error: subtractCreditsMutation.error,
    },
    useCredits: {
      mutate: useCreditsMutation.mutate,
      mutateAsync: useCreditsMutation.mutateAsync,
      isLoading: useCreditsMutation.isPending,
      isError: useCreditsMutation.isError,
      error: useCreditsMutation.error,
    },
    refundCredits: {
      mutate: refundCreditsMutation.mutate,
      mutateAsync: refundCreditsMutation.mutateAsync,
      isLoading: refundCreditsMutation.isPending,
      isError: refundCreditsMutation.isError,
      error: refundCreditsMutation.error,
    },
  };
}
