/**
 * useContent Hook
 * 
 * React Query hook for content management with validation.
 * Integrates contentRepository with React Query for caching and state management.
 * Uses Zod validators from Interface utils for content validation.
 * 
 * Architecture Rules:
 * - Data hook belongs to Interface Layer
 * - Uses contentRepository from Data Layer for business logic
 * - Uses Zod validators from Interface utils
 * - Provides optimistic updates for content operations
 * - Integrates with React Query for caching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { contentRepository } from '@/data';
import { CONTENT_QUERY_KEYS, USER_QUERY_KEYS } from '@/data';
import { ContentValidation, ValidationUtils } from '@/interface/utils/validation';
import type {
  Content,
  BaseContent,
  VideoContent,
  PhotoContent,
  CreateContentPayload,
  UpdateContentPayload,
  ContentSearchParams,
  ContentListResponse,
  ContentStats,
  ContentProcessingJob,
  BulkContentOperation,
  ContentAnalytics,
  ApiResponse,
} from '@/models';

// ============================================================================
// HOOK TYPES
// ============================================================================

export interface UseContentOptions {
  enabled?: boolean;
  refetchInterval?: number;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export interface UseContentListResult {
  content: Content[];
  total: number;
  page: number;
  totalPages: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UseContentResult {
  content: Content | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
}

export interface UseContentMutationsResult {
  createContent: {
    mutate: (payload: CreateContentPayload) => void;
    mutateAsync: (payload: CreateContentPayload) => Promise<Content>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  updateContent: {
    mutate: (params: { id: number; payload: UpdateContentPayload }) => void;
    mutateAsync: (params: { id: number; payload: UpdateContentPayload }) => Promise<Content>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  deleteContent: {
    mutate: (id: number) => void;
    mutateAsync: (id: number) => Promise<{ id: number }>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
  bulkOperation: {
    mutate: (operation: BulkContentOperation) => void;
    mutateAsync: (operation: BulkContentOperation) => Promise<{ processed: number; errors: string[] }>;
    isLoading: boolean;
    isError: boolean;
    error: Error | null;
  };
}

// ============================================================================
// CONTENT LIST HOOK
// ============================================================================

/**
 * Hook for fetching content list with filtering and pagination
 */
export function useContent(
  searchParams?: ContentSearchParams,
  options?: UseContentOptions
): UseContentListResult {
  const queryClient = useQueryClient();

  // Validate search parameters
  const validatedParams = searchParams 
    ? ValidationUtils.safeParse(ContentValidation.search, searchParams)
    : undefined;

  const query = useQuery({
    queryKey: CONTENT_QUERY_KEYS.list(validatedParams),
    queryFn: async (): Promise<ContentListResponse> => {
      const response = await contentRepository.getContent(validatedParams);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch content');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval,
    staleTime: options?.staleTime || 3 * 60 * 1000, // 3 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });

  const currentPage = validatedParams?.page || 1;
  const totalPages = query.data?.totalPages || 0;

  return {
    content: query.data?.content || [],
    total: query.data?.total || 0,
    page: query.data?.page || 1,
    totalPages,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    hasNextPage: currentPage < totalPages,
    hasPreviousPage: currentPage > 1,
  };
}

// ============================================================================
// SINGLE CONTENT HOOK
// ============================================================================

/**
 * Hook for fetching a single content item by ID
 */
export function useContentById(id: number, options?: UseContentOptions): UseContentResult {
  const query = useQuery({
    queryKey: CONTENT_QUERY_KEYS.detail(id),
    queryFn: async (): Promise<Content> => {
      const response = await contentRepository.getContentById(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch content');
      }
      return response.data;
    },
    enabled: options?.enabled !== false && !!id,
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
  });

  return {
    content: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
  };
}

// ============================================================================
// USER CONTENT HOOK
// ============================================================================

/**
 * Hook for fetching content for a specific user
 */
export function useUserContent(
  userId: number,
  searchParams?: Omit<ContentSearchParams, 'userId'>,
  options?: UseContentOptions
) {
  const fullParams: ContentSearchParams = {
    ...searchParams,
    userId,
  };

  return useContent(fullParams, options);
}

// ============================================================================
// CONTENT BY TYPE HOOK
// ============================================================================

/**
 * Hook for fetching content by type (video/photo)
 */
export function useContentByType(
  type: 'video' | 'photo',
  searchParams?: Omit<ContentSearchParams, 'type'>,
  options?: UseContentOptions
) {
  const fullParams: ContentSearchParams = {
    ...searchParams,
    type,
  };

  return useContent(fullParams, options);
}

// ============================================================================
// CONTENT STATS HOOK
// ============================================================================

/**
 * Hook for fetching content statistics
 */
export function useContentStats(userId?: number, options?: UseContentOptions) {
  return useQuery({
    queryKey: CONTENT_QUERY_KEYS.userStats(userId || 0),
    queryFn: async (): Promise<ContentStats> => {
      const response = await contentRepository.getContentStats(userId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch content stats');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// PROCESSING JOBS HOOK
// ============================================================================

/**
 * Hook for fetching content processing jobs
 */
export function useContentProcessingJobs(userId?: number, options?: UseContentOptions) {
  return useQuery({
    queryKey: CONTENT_QUERY_KEYS.userProcessingJobs(userId || 0),
    queryFn: async (): Promise<ContentProcessingJob[]> => {
      const response = await contentRepository.getProcessingJobs(userId);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch processing jobs');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    refetchInterval: options?.refetchInterval || 5 * 1000, // Refresh every 5 seconds for processing status
    staleTime: options?.staleTime || 30 * 1000, // 30 seconds
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// CONTENT ANALYTICS HOOK
// ============================================================================

/**
 * Hook for fetching content analytics
 */
export function useContentAnalytics(
  period: 'week' | 'month' | 'quarter' | 'year' = 'month',
  options?: UseContentOptions
) {
  return useQuery({
    queryKey: CONTENT_QUERY_KEYS.stats(),
    queryFn: async (): Promise<ContentAnalytics> => {
      const response = await contentRepository.getContentAnalytics(period);
      if (!response.success) {
        throw new Error(response.message || 'Failed to fetch content analytics');
      }
      return response.data;
    },
    enabled: options?.enabled !== false,
    staleTime: options?.staleTime || 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus !== false,
  });
}

// ============================================================================
// CONTENT MUTATIONS HOOK
// ============================================================================

/**
 * Hook for content mutations (create, update, delete, bulk operations)
 */
export function useContentMutations(): UseContentMutationsResult {
  const queryClient = useQueryClient();

  // Create content mutation
  const createContentMutation = useMutation({
    mutationFn: async (payload: CreateContentPayload): Promise<Content> => {
      // Validate payload with Zod
      const validationResult = ValidationUtils.validate(ContentValidation.create, payload);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await contentRepository.createContent(validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to create content');
      }
      return response.data;
    },
    onSuccess: (newContent) => {
      // Invalidate and refetch content lists
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.lists() });

      // Add new content to cache
      queryClient.setQueryData(CONTENT_QUERY_KEYS.detail(newContent.id), newContent);

      // Update content stats
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.stats() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.userStats(newContent.userId) });

      // Update user-related queries
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.detail(newContent.userId) });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.contentStats(newContent.userId) });

      // Update processing jobs
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.processingJobs() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.userProcessingJobs(newContent.userId) });
    },
  });

  // Update content mutation
  const updateContentMutation = useMutation({
    mutationFn: async ({ id, payload }: { id: number; payload: UpdateContentPayload }): Promise<Content> => {
      // Validate payload with Zod
      const validationResult = ValidationUtils.validate(ContentValidation.update, payload);
      if (!validationResult.success) {
        throw new Error(`Validation failed: ${validationResult.errors?.join(', ')}`);
      }

      const response = await contentRepository.updateContent(id, validationResult.data!);
      if (!response.success) {
        throw new Error(response.message || 'Failed to update content');
      }
      return response.data;
    },
    onMutate: async ({ id, payload }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: CONTENT_QUERY_KEYS.detail(id) });

      // Snapshot previous value
      const previousContent = queryClient.getQueryData<Content>(CONTENT_QUERY_KEYS.detail(id));

      // Optimistically update content
      if (previousContent) {
        queryClient.setQueryData<Content>(CONTENT_QUERY_KEYS.detail(id), {
          ...previousContent,
          ...payload,
        });
      }

      return { previousContent };
    },
    onError: (err, { id }, context) => {
      // Rollback optimistic update
      if (context?.previousContent) {
        queryClient.setQueryData(CONTENT_QUERY_KEYS.detail(id), context.previousContent);
      }
    },
    onSuccess: (updatedContent) => {
      // Update cache with server response
      queryClient.setQueryData(CONTENT_QUERY_KEYS.detail(updatedContent.id), updatedContent);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.stats() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.userStats(updatedContent.userId) });
    },
  });

  // Delete content mutation
  const deleteContentMutation = useMutation({
    mutationFn: async (id: number): Promise<{ id: number }> => {
      const response = await contentRepository.deleteContent(id);
      if (!response.success) {
        throw new Error(response.message || 'Failed to delete content');
      }
      return response.data;
    },
    onSuccess: (deletedContent, id) => {
      // Remove content from cache
      queryClient.removeQueries({ queryKey: CONTENT_QUERY_KEYS.detail(id) });

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.stats() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.processingJobs() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.analytics() });
    },
  });

  // Bulk operation mutation
  const bulkOperationMutation = useMutation({
    mutationFn: async (operation: BulkContentOperation): Promise<{ processed: number; errors: string[] }> => {
      const response = await contentRepository.bulkContentOperation(operation);
      if (!response.success) {
        throw new Error(response.message || 'Failed to perform bulk operation');
      }
      return response.data;
    },
    onSuccess: () => {
      // Invalidate all content-related queries after bulk operation
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.lists() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.stats() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.processingJobs() });
      queryClient.invalidateQueries({ queryKey: CONTENT_QUERY_KEYS.analytics() });

      // Invalidate user-related queries
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.lists() });
    },
  });

  return {
    createContent: {
      mutate: createContentMutation.mutate,
      mutateAsync: createContentMutation.mutateAsync,
      isLoading: createContentMutation.isPending,
      isError: createContentMutation.isError,
      error: createContentMutation.error,
    },
    updateContent: {
      mutate: updateContentMutation.mutate,
      mutateAsync: updateContentMutation.mutateAsync,
      isLoading: updateContentMutation.isPending,
      isError: updateContentMutation.isError,
      error: updateContentMutation.error,
    },
    deleteContent: {
      mutate: deleteContentMutation.mutate,
      mutateAsync: deleteContentMutation.mutateAsync,
      isLoading: deleteContentMutation.isPending,
      isError: deleteContentMutation.isError,
      error: deleteContentMutation.error,
    },
    bulkOperation: {
      mutate: bulkOperationMutation.mutate,
      mutateAsync: bulkOperationMutation.mutateAsync,
      isLoading: bulkOperationMutation.isPending,
      isError: bulkOperationMutation.isError,
      error: bulkOperationMutation.error,
    },
  };
}
