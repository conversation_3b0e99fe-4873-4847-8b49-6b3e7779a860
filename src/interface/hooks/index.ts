/**
 * Hooks - Central Export
 * 
 * This is the main entry point for all hooks in the Interface Layer.
 * Provides organized access to both data hooks and UI hooks.
 * 
 * Architecture Rules:
 * - All hooks belong to Interface Layer
 * - Data hooks integrate with repositories and provide validation
 * - UI hooks handle UI state and interactions
 * - Clear separation between data and UI concerns
 */

// ============================================================================
// DATA HOOKS EXPORTS
// ============================================================================

// Export all data hooks
export {
  // User hooks
  useUsers,
  useUser,
  useUserProfile,
  useUserCreditSummary,
  useUserContentSummary,
  useUsersByStatus,
  useUserMutations,
  
  // Credit hooks
  useCreditTransactions,
  useCreditBalance,
  useCreditUsageStats,
  useUserCreditTransactions,
  useCreditMutations,
  
  // Analytics hooks
  useDashboardStats,
  useQuickStats,
  useAnalyticsData,
  useUserGrowthData,
  useCreditUsageData,
  useContentCreationData,
  useSystemPerformanceData,
  useAnalyticsSummary,
  useMetricComparison,
  useTopPerformers,
  
  // Content hooks
  useContent,
  useContentById,
  useUserContent,
  useContentByType,
  useContentStats,
  useContentProcessingJobs,
  useContentAnalytics,
  useContentMutations,
  
  // Notification hooks
  useNotifications,
  useNotification,
  useUserNotifications,
  useUnreadNotificationsCount,
  useNotificationPreferences,
  useNotificationTemplates,
  useNotificationAnalytics,
  useNotificationMutations,
  
  // System hooks
  useSystemConfig,
  useSystemStatus,
  useFeatureFlags,
  useMaintenanceSettings,
  useAuditLog,
  useSystemLimits,
  useMonitoringConfig,
  useSystemMutations,
} from './data';

// Export data hook types
export type {
  // User hook types
  UseUsersOptions,
  UseUserOptions,
  UseUsersResult,
  UseUserResult,
  UseUserMutationsResult,
  
  // Credit hook types
  UseCreditTransactionsOptions,
  UseCreditTransactionsResult,
  UseCreditBalanceResult,
  UseCreditMutationsResult,
  
  // Analytics hook types
  UseAnalyticsOptions,
  UseDashboardStatsResult,
  UseQuickStatsResult,
  UseAnalyticsDataResult,
  
  // Content hook types
  UseContentOptions,
  UseContentListResult,
  UseContentResult,
  UseContentMutationsResult,
  
  // Notification hook types
  UseNotificationsOptions,
  UseNotificationsResult,
  UseNotificationResult,
  UseNotificationMutationsResult,
  
  // System hook types
  UseSystemOptions,
  UseSystemConfigResult,
  UseSystemStatusResult,
  UseSystemMutationsResult,
} from './data';

// ============================================================================
// UI HOOKS EXPORTS
// ============================================================================

// Export all UI hooks
export {
  useIsMobile,
  useToast,
  useModal,
  useFilters,
  usePagination,
  useSearch,
  ModalUtils,
  FilterUtils,
  PaginationUtils,
  SearchUtils,
} from './ui';

// Export UI hook types
export type * from './ui';

// ============================================================================
// HOOK UTILITIES
// ============================================================================

/**
 * Hook utilities for common patterns
 */
export const HookUtils = {
  /**
   * Default options for data hooks
   */
  getDefaultDataHookOptions: () => ({
    enabled: true,
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  }),

  /**
   * Default options for real-time data hooks
   */
  getDefaultRealTimeOptions: () => ({
    enabled: true,
    refetchInterval: 30 * 1000, // 30 seconds
    refetchOnWindowFocus: true,
    staleTime: 1 * 60 * 1000, // 1 minute
  }),

  /**
   * Default options for analytics hooks
   */
  getDefaultAnalyticsOptions: () => ({
    enabled: true,
    refetchOnWindowFocus: false,
    staleTime: 10 * 60 * 1000, // 10 minutes
  }),

  /**
   * Default options for system hooks
   */
  getDefaultSystemOptions: () => ({
    enabled: true,
    refetchOnWindowFocus: false,
    staleTime: 15 * 60 * 1000, // 15 minutes
  }),
} as const;

// ============================================================================
// HOOK METADATA
// ============================================================================

/**
 * Hook layer metadata for debugging and documentation
 */
export const HOOK_LAYER_INFO = {
  version: '1.0.0',
  description: 'Interface Layer Hooks - Data and UI state management',
  architecture: '3-layer MVI',
  dependencies: ['Data Layer', 'React Query', 'Zod'],
  exports: {
    dataHooks: [
      'useUsers', 'useCredits', 'useAnalytics', 'useContent', 'useNotifications', 'useSystem'
    ],
    uiHooks: [
      'useIsMobile', 'useToast'
    ],
    utilities: [
      'HookUtils', 'Default options', 'Common patterns'
    ]
  },
  features: [
    'React Query integration',
    'Zod validation',
    'Optimistic updates',
    'Cache management',
    'Error handling',
    'Type safety'
  ]
} as const;

// ============================================================================
// DEVELOPMENT HELPERS
// ============================================================================

/**
 * Development helper to validate hook layer integrity
 * Only available in development mode
 */
export const validateHookLayer = () => {
  if (process.env.NODE_ENV !== 'development') {
    return { valid: true, message: 'Validation only available in development' };
  }

  const checks = [
    // Check that hook utilities are available
    typeof HookUtils !== 'undefined',
    typeof HookUtils.getDefaultDataHookOptions === 'function',
    // Add more checks as needed
  ];

  const allValid = checks.every(check => check === true);

  return {
    valid: allValid,
    message: allValid 
      ? 'Hook Layer validation passed' 
      : 'Hook Layer validation failed - some exports are missing',
    timestamp: new Date().toISOString()
  };
};
