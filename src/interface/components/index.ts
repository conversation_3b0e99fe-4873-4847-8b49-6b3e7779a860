/**
 * Interface Components - Central Export
 * 
 * This is the main entry point for all components in the Interface Layer.
 * Components are organized by type: UI, Features, and Layout.
 * 
 * Architecture Rules:
 * - UI components are reusable, generic components (shadcn/ui)
 * - Feature components are domain-specific business components
 * - Layout components handle structure and navigation
 * - All components belong to Interface Layer
 */

// ============================================================================
// UI COMPONENTS (shadcn/ui)
// ============================================================================

// Re-export all UI components
export * from './ui';

// ============================================================================
// FEATURE COMPONENTS
// ============================================================================

// Export all feature-based components
export * from './features';

// ============================================================================
// LAYOUT COMPONENTS
// ============================================================================

// Export all layout components
export * from './layout';

// ============================================================================
// COMPONENT UTILITIES
// ============================================================================

/**
 * Component utilities for common patterns
 */
export const ComponentUtils = {
  /**
   * Get component display name for debugging
   */
  getDisplayName: (component: React.ComponentType<any>): string => {
    return component.displayName || component.name || 'Component';
  },

  /**
   * Create a compound component pattern
   */
  createCompound: <T extends Record<string, React.ComponentType<any>>>(
    components: T
  ): T => {
    return components;
  },
} as const;

// ============================================================================
// COMPONENT METADATA
// ============================================================================

/**
 * Component layer metadata for debugging and documentation
 */
export const COMPONENT_LAYER_INFO = {
  version: '1.0.0',
  description: 'Interface Layer Components - UI, Features, and Layout',
  architecture: '3-layer MVI',
  organization: {
    ui: 'Reusable, generic components (shadcn/ui)',
    features: 'Domain-specific business components',
    layout: 'Structure and navigation components',
  },
  dependencies: ['React', 'shadcn/ui', 'Tailwind CSS'],
  features: [
    'Feature-based organization',
    'Compound component patterns',
    'Type-safe exports',
    'Consistent naming',
    'Debugging utilities'
  ]
} as const;
