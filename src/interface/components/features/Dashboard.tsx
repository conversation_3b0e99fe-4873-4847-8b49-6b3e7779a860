
interface DashboardProps {
  onLogout: () => void;
}

const Dashboard = ({ onLogout }: DashboardProps) => {
  return (
    <div style={{ padding: '20px', minHeight: '100vh', background: '#f8fafc' }}>
      <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', marginBottom: '20px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <h1 style={{ margin: 0, color: '#1e293b' }}>Mega AI Admin Dashboard</h1>
          <button
            onClick={onLogout}
            style={{
              padding: '8px 16px',
              fontSize: '14px',
              background: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Logout
          </button>
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
        <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#1e293b' }}>Tổng người dùng</h3>
          <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#3b82f6', margin: 0 }}>2,847</p>
          <p style={{ fontSize: '14px', color: '#10b981', margin: '5px 0 0 0' }}>+12.5%</p>
        </div>

        <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#1e293b' }}>Credits đã sử dụng</h3>
          <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#8b5cf6', margin: 0 }}>45,672</p>
          <p style={{ fontSize: '14px', color: '#10b981', margin: '5px 0 0 0' }}>+8.2%</p>
        </div>

        <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}>
          <h3 style={{ margin: '0 0 10px 0', color: '#1e293b' }}>Videos tạo ra</h3>
          <p style={{ fontSize: '24px', fontWeight: 'bold', color: '#f59e0b', margin: 0 }}>1,234</p>
          <p style={{ fontSize: '14px', color: '#10b981', margin: '5px 0 0 0' }}>+15.8%</p>
        </div>
      </div>

      <div style={{ background: 'white', padding: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', marginTop: '20px' }}>
        <h3 style={{ margin: '0 0 15px 0', color: '#1e293b' }}>Kiến trúc MVI 3-layer hoàn chỉnh</h3>
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
          <div style={{ padding: '15px', background: '#f1f5f9', borderRadius: '6px' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#475569' }}>Model Layer</h4>
            <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>Types & Constants</p>
          </div>
          <div style={{ padding: '15px', background: '#f1f5f9', borderRadius: '6px' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#475569' }}>Data Layer</h4>
            <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>Repositories & Sources</p>
          </div>
          <div style={{ padding: '15px', background: '#f1f5f9', borderRadius: '6px' }}>
            <h4 style={{ margin: '0 0 8px 0', color: '#475569' }}>Interface Layer</h4>
            <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>Components & Hooks</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
