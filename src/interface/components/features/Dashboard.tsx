
import { useState } from 'react';
import {
  Users,
  Search,
  Bell,
  User,
  CreditCard,
  Video
} from 'lucide-react';
import Sidebar from '../layout/Sidebar';
import EnhancedStatsCard from '../ui/EnhancedStatsCard';
import UserGrowthChart from '../ui/UserGrowthChart';
import UserCreditManagement from './UserCreditManagement';
import ContentManagement from './ContentManagement';
import Analytics from './Analytics';
import NotificationManagement from './NotificationManagement';
import SystemSettings from './SystemSettings';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card } from '../ui/card';

interface DashboardProps {
  onLogout: () => void;
}

const Dashboard = ({ onLogout }: DashboardProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const statsData = [
    {
      title: 'Tổng người dùng cá nhân',
      value: '12,450',
      change: '+12.5%',
      changeType: 'positive' as const,
      icon: Users,
      gradient: 'bg-gradient-to-r from-blue-500 to-blue-600',
      subtitle: '89 đang hoạt động',
      trend: [20, 35, 25, 40, 30, 45, 50]
    },
    {
      title: 'Credits cá nhân đã dùng',
      value: '28,400',
      change: '+23.5%',
      changeType: 'positive' as const,
      icon: CreditCard,
      gradient: 'bg-gradient-to-r from-green-500 to-green-600',
      subtitle: '456 hôm nay',
      trend: [30, 20, 45, 35, 50, 40, 60]
    },
    {
      title: 'Videos cá nhân',
      value: '8,942',
      change: '+18.2%',
      changeType: 'positive' as const,
      icon: Video,
      gradient: 'bg-gradient-to-r from-purple-500 to-purple-600',
      subtitle: '342 hôm nay',
      trend: [15, 25, 20, 35, 25, 40, 45]
    }
  ];

  const quickStats = [
    { label: 'Credits còn lại hệ thống', value: '2.4M', color: 'text-green-600' },
    { label: 'Người dùng mới hôm nay', value: '12', color: 'text-blue-600' },
    { label: 'Credits đã phát hành', value: '3.2M', color: 'text-purple-600' }
  ];

  const getSectionTitle = (section: string) => {
    switch (section) {
      case 'dashboard': return 'Bảng điều khiển';
      case 'users': return 'Quản lý người dùng & Credits';
      case 'content': return 'Quản lý nội dung';
      case 'analytics': return 'Báo cáo & Phân tích';
      case 'notifications': return 'Quản lý thông báo';
      case 'settings': return 'Cài đặt hệ thống';
      default: return section;
    }
  };

  const getSectionDescription = (section: string) => {
    switch (section) {
      case 'dashboard': return 'Tổng quan platform SaaS cho nội dung cá nhân';
      case 'users': return 'Quản lý người dùng, credits và thống kê chi tiết';
      case 'content': return 'Quản lý videos và photos được tạo';
      case 'analytics': return 'Thống kê và phân tích hiệu suất';
      case 'notifications': return 'Gửi và quản lý thông báo';
      case 'settings': return 'Cấu hình và thiết lập hệ thống';
      default: return 'Quản lý platform tạo video và photo cá nhân bằng credits';
    }
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'users':
        return <UserCreditManagement />;
      case 'content':
        return <ContentManagement />;
      case 'analytics':
        return <Analytics />;
      case 'notifications':
        return <NotificationManagement />;
      case 'settings':
        return <SystemSettings />;
      default:
        return (
          <div className="spacing-responsive">
            {/* Enhanced Stats Grid */}
            <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
              {statsData.map((stat, index) => (
                <EnhancedStatsCard key={index} {...stat} />
              ))}
            </div>

            {/* Quick Stats Row */}
            <div className="grid-responsive-1-2-3 gap-4">
              {quickStats.map((stat, index) => (
                <Card key={index} className="p-4 sm:p-6 text-center">
                  <p className="text-xs sm:text-sm text-slate-600 mb-1">{stat.label}</p>
                  <p className={`text-lg sm:text-xl font-bold ${stat.color}`}>{stat.value}</p>
                </Card>
              ))}
            </div>

            {/* Charts */}
            <UserGrowthChart />
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen bg-slate-50">
      <Sidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        onLogout={onLogout}
      />

      <div className="flex-1 overflow-auto w-full min-w-0">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-slate-200 p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl font-bold text-slate-900 truncate">
                {getSectionTitle(activeSection)}
              </h1>
              <p className="text-sm sm:text-base text-slate-600 mt-1">
                {getSectionDescription(activeSection)}
              </p>
            </div>

            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <div className="relative hidden sm:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                <Input
                  placeholder="Tìm kiếm..."
                  className="pl-10 w-60 lg:w-80 bg-slate-50 border-slate-200 focus:bg-white"
                />
              </div>

              <Button variant="ghost" size="icon" className="relative flex-shrink-0">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </Button>

              <Button variant="ghost" size="icon" className="flex-shrink-0">
                <User className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Mobile Search */}
          <div className="relative mt-4 sm:hidden">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            <Input
              placeholder="Tìm kiếm..."
              className="pl-10 w-full bg-slate-50 border-slate-200 focus:bg-white"
            />
          </div>
        </header>

        {/* Main Content */}
        <main className="p-4 sm:p-6">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
