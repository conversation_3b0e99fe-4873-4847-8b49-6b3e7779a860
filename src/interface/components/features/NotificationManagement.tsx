/**
 * NotificationManagement Component
 * 
 * Feature component for managing notifications in the Interface Layer.
 * Uses data hooks from Interface Layer for React Query integration.
 * Follows 3-layer MVI architecture principles.
 * 
 * Architecture Rules:
 * - Feature component belongs to Interface Layer
 * - Uses data hooks (useNotifications, useNotificationMutations) for data management
 * - Uses Zod validation through data hooks
 * - No direct repository access (goes through hooks)
 * - Responsive design with Tailwind CSS
 */

import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { Button } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/interface/components/ui/tabs';
import { 
  Bell,
  Search,
  Filter,
  MoreHorizontal,
  Send,
  Edit,
  Trash2,
  Eye,
  Clock,
  User,
  Calendar,
  Plus,
  Settings,
  Mail,
  MessageSquare,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';

// Import data hooks from Interface Layer
import {
  useNotifications,
  useNotificationMutations,
  useNotificationTemplates,
  useNotificationAnalytics,
  useUnreadNotificationsCount,
} from '@/interface/hooks/data';

import type { NotificationSearchParams, CreateNotificationPayload } from '@/models';

const NotificationManagement = () => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'system' | 'user' | 'marketing'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'sent' | 'pending' | 'failed'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState('notifications');
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>([]);

  // ============================================================================
  // DATA HOOKS
  // ============================================================================

  // Search parameters for notifications
  const searchParams: NotificationSearchParams = {
    searchTerm: searchTerm || undefined,
    type: typeFilter === 'all' ? undefined : typeFilter,
    status: statusFilter === 'all' ? undefined : statusFilter,
    page: currentPage,
    limit: 10,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  };

  // Fetch notifications with React Query
  const {
    notifications,
    total,
    page,
    totalPages,
    isLoading: notificationsLoading,
    isError: notificationsError,
    error: notificationsErrorMessage,
    refetch: refetchNotifications,
    hasNextPage,
    hasPreviousPage,
  } = useNotifications(searchParams, {
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refresh every 30 seconds
  });

  // Notification mutations
  const {
    createNotification,
    updateNotification,
    deleteNotification,
    sendNotification,
    markAsRead,
    bulkDelete,
  } = useNotificationMutations();

  // Notification templates
  const {
    data: templates,
    isLoading: templatesLoading,
  } = useNotificationTemplates({
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Notification analytics
  const {
    data: analytics,
    isLoading: analyticsLoading,
  } = useNotificationAnalytics({
    period: 'week',
  }, {
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Unread count
  const {
    data: unreadCount,
  } = useUnreadNotificationsCount({
    refetchInterval: 30 * 1000, // Refresh every 30 seconds
  });

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTypeFilter = (type: 'all' | 'system' | 'user' | 'marketing') => {
    setTypeFilter(type);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleStatusFilter = (status: 'all' | 'sent' | 'pending' | 'failed') => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleSelectNotification = (id: number) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === notifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(notifications.map(item => item.id));
    }
  };

  const handleSendNotification = async (id: number) => {
    try {
      await sendNotification.mutateAsync(id);
      console.log('Notification sent successfully');
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  };

  const handleDeleteNotification = async (id: number) => {
    try {
      await deleteNotification.mutateAsync(id);
      console.log('Notification deleted successfully');
    } catch (error) {
      console.error('Failed to delete notification:', error);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNotifications.length === 0) return;

    try {
      await bulkDelete.mutateAsync(selectedNotifications);
      setSelectedNotifications([]);
      console.log('Notifications deleted successfully');
    } catch (error) {
      console.error('Failed to delete notifications:', error);
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'system': return 'bg-blue-100 text-blue-800';
      case 'user': return 'bg-green-100 text-green-800';
      case 'marketing': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'system': return 'Hệ thống';
      case 'user': return 'Người dùng';
      case 'marketing': return 'Marketing';
      default: return 'Khác';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'sent': return 'Đã gửi';
      case 'pending': return 'Chờ gửi';
      case 'failed': return 'Thất bại';
      default: return 'Không xác định';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'system': return AlertCircle;
      case 'user': return User;
      case 'marketing': return Mail;
      default: return Info;
    }
  };

  // ============================================================================
  // LOADING AND ERROR STATES
  // ============================================================================

  if (notificationsLoading) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-slate-600">Đang tải thông báo...</p>
          </div>
        </div>
      </div>
    );
  }

  if (notificationsError) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Lỗi khi tải thông báo: {notificationsErrorMessage?.message}</p>
            <Button 
              onClick={() => refetchNotifications()} 
              className="mt-4"
              variant="outline"
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const totalSent = analytics?.totalSent || 0;
  const totalPending = analytics?.totalPending || 0;
  const totalFailed = analytics?.totalFailed || 0;
  const openRate = analytics?.openRate || 0;

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Quản lý thông báo</h2>
          <p className="text-sm sm:text-base text-slate-600">
            Quản lý và gửi thông báo đến người dùng
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedNotifications.length > 0 && (
            <Button 
              variant="outline"
              onClick={handleBulkDelete}
              disabled={bulkDelete.isLoading}
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Xóa ({selectedNotifications.length})
            </Button>
          )}
          <Button className="bg-indigo-600 hover:bg-indigo-700">
            <Plus className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Tạo thông báo</span>
            <span className="sm:hidden">Tạo</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotificationManagement;
