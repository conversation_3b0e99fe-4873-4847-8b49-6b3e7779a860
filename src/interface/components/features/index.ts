/**
 * Feature Components - Central Export
 *
 * This module exports all feature components for the Interface Layer.
 * Feature components are domain-specific business components that use data hooks.
 *
 * Architecture Rules:
 * - Feature components use data hooks for data management
 * - Components are organized by business domain
 * - Each component follows responsive design principles
 * - All components use Zod validation through data hooks
 */

// ============================================================================
// AUTHENTICATION FEATURES
// ============================================================================

export { default as Login } from './Login';

// ============================================================================
// DASHBOARD FEATURES
// ============================================================================

export { default as Dashboard } from './Dashboard';

// ============================================================================
// USER MANAGEMENT FEATURES
// ============================================================================

export { default as UserCreditManagement } from './UserCreditManagement';
export { default as CreditManagement } from './CreditManagement';

// ============================================================================
// CONTENT MANAGEMENT FEATURES
// ============================================================================

export { default as ContentManagement } from './ContentManagement';

// ============================================================================
// ANALYTICS FEATURES
// ============================================================================

export { default as Analytics } from './Analytics';

// ============================================================================
// NOTIFICATION FEATURES
// ============================================================================

export { default as NotificationManagement } from './NotificationManagement';

// ============================================================================
// SYSTEM FEATURES
// ============================================================================

export { default as SystemSettings } from './SystemSettings';

// ============================================================================
// FEATURE COMPONENTS METADATA
// ============================================================================

/**
 * Feature components metadata for debugging and documentation
 */
export const FEATURE_COMPONENTS_INFO = {
  version: '1.0.0',
  description: 'Feature components for business domains',
  architecture: '3-layer MVI',

  domains: {
    userManagement: ['UserCreditManagement'],
    contentManagement: [], // TODO: Add when migrated
    analytics: [], // TODO: Add when migrated
    notifications: [], // TODO: Add when migrated
    system: [], // TODO: Add when migrated
  },

  patterns: [
    'Use data hooks for data management',
    'Implement responsive design',
    'Handle loading and error states',
    'Use Zod validation through hooks',
    'Follow consistent UI patterns',
  ],

  migration: {
    completed: [
      'UserCreditManagement',
      'ContentManagement',
      'Analytics',
      'NotificationManagement',
      'SystemSettings',
    ],
    pending: [],
  },
} as const;
