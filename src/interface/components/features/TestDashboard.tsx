import { useState } from 'react';
import { Users } from 'lucide-react';
import Sidebar from '../layout/Sidebar';
import EnhancedStatsCard from '../ui/EnhancedStatsCard';
import UserGrowthChart from '../ui/UserGrowthChart';

interface TestDashboardProps {
  onLogout: () => void;
}

const TestDashboard = ({ onLogout }: TestDashboardProps) => {
  const [activeSection, setActiveSection] = useState('dashboard');

  const testStat = {
    title: 'Test Stat',
    value: '1,234',
    change: '+12.5%',
    changeType: 'positive' as const,
    icon: Users,
    gradient: 'bg-gradient-to-r from-blue-500 to-blue-600',
    subtitle: 'Test subtitle',
    trend: [20, 35, 25, 40, 30, 45, 50]
  };

  return (
    <div className="flex min-h-screen bg-slate-50">
      <Sidebar
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        onLogout={onLogout}
      />

      <div className="flex-1 p-6">
        <h1 className="text-2xl font-bold text-slate-900 mb-4">
          Test Dashboard - {activeSection}
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <EnhancedStatsCard {...testStat} />
        </div>

        <UserGrowthChart />

        <p className="text-slate-600">
          Dashboard is working with Sidebar, EnhancedStatsCard, and UserGrowthChart!
        </p>
      </div>
    </div>
  );
};

export default TestDashboard;
