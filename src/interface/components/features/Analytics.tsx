/**
 * Analytics Component
 * 
 * Feature component for analytics and reporting in the Interface Layer.
 * Uses data hooks from Interface Layer for React Query integration.
 * Follows 3-layer MVI architecture principles.
 * 
 * Architecture Rules:
 * - Feature component belongs to Interface Layer
 * - Uses data hooks (useAnalytics, useDashboardStats) for data management
 * - Uses Zod validation through data hooks
 * - No direct repository access (goes through hooks)
 * - Responsive design with Tailwind CSS
 */

import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { Button } from '@/interface/components/ui/button';
import { Badge } from '@/interface/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  CreditCard,
  Video,
  Download,
  Calendar,
  Filter,
  Eye,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { BarChart, Bar, LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

// Import data hooks from Interface Layer
import {
  useDashboardStats,
  useQuickStats,
  useAnalyticsData,
  useUserGrowthData,
  useCreditUsageData,
  useTopPerformers,
} from '@/interface/hooks/data';

import type { TimePeriod, AnalyticsQuery } from '@/models';

const Analytics = () => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const [dateRange, setDateRange] = useState<TimePeriod>('week');
  const [selectedMetric, setSelectedMetric] = useState<'revenue' | 'users' | 'credits' | 'content'>('revenue');

  // ============================================================================
  // DATA HOOKS
  // ============================================================================

  // Dashboard statistics
  const {
    stats: dashboardStats,
    isLoading: statsLoading,
    isError: statsError,
    error: statsErrorMessage,
    refetch: refetchStats,
  } = useDashboardStats(dateRange, {
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  // Quick stats (real-time)
  const {
    stats: quickStats,
    isLoading: quickStatsLoading,
  } = useQuickStats({
    refetchInterval: 30 * 1000, // Refresh every 30 seconds
  });

  // Analytics data with filters
  const analyticsQuery: AnalyticsQuery = {
    period: dateRange,
    metrics: [selectedMetric],
    groupBy: 'day',
  };

  const {
    data: analyticsData,
    isLoading: analyticsLoading,
  } = useAnalyticsData(analyticsQuery, {
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // User growth data
  const {
    data: userGrowthData,
    isLoading: userGrowthLoading,
  } = useUserGrowthData(dateRange, {
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // Credit usage data
  const {
    data: creditUsageData,
    isLoading: creditUsageLoading,
  } = useCreditUsageData(dateRange, {
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Top performers
  const {
    data: topPerformers,
    isLoading: topPerformersLoading,
  } = useTopPerformers({
    period: dateRange,
    limit: 10,
    sortBy: 'creditsUsed',
  }, {
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleDateRangeChange = (range: TimePeriod) => {
    setDateRange(range);
  };

  const handleMetricChange = (metric: 'revenue' | 'users' | 'credits' | 'content') => {
    setSelectedMetric(metric);
  };

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log('Exporting report...');
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(1)}%`;
  };

  const getPercentageColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const getPercentageIcon = (value: number) => {
    return value >= 0 ? ArrowUp : ArrowDown;
  };

  // ============================================================================
  // LOADING AND ERROR STATES
  // ============================================================================

  if (statsLoading) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-slate-600">Đang tải dữ liệu phân tích...</p>
          </div>
        </div>
      </div>
    );
  }

  if (statsError) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Lỗi khi tải dữ liệu: {statsErrorMessage?.message}</p>
            <Button 
              onClick={() => refetchStats()} 
              className="mt-4"
              variant="outline"
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Báo cáo & Phân tích</h2>
          <p className="text-sm sm:text-base text-slate-600">
            Thống kê chi tiết về hiệu suất platform
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select 
            value={dateRange} 
            onChange={(e) => handleDateRangeChange(e.target.value as TimePeriod)}
            className="px-3 py-2 border border-slate-200 rounded-lg text-sm"
          >
            <option value="day">Hôm nay</option>
            <option value="week">7 ngày qua</option>
            <option value="month">30 ngày qua</option>
            <option value="quarter">3 tháng qua</option>
            <option value="year">1 năm qua</option>
          </select>
          <Button variant="outline" onClick={handleExportReport}>
            <Download className="w-4 h-4 mr-2" />
            <span className="hidden sm:inline">Xuất báo cáo</span>
            <span className="sm:hidden">Xuất</span>
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
        {dashboardStats?.map((stat, index) => {
          const Icon = getPercentageIcon(stat.changePercentage || 0);
          return (
            <Card key={index} className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-lg flex items-center justify-center ${
                  stat.type === 'revenue' ? 'bg-green-100' :
                  stat.type === 'users' ? 'bg-blue-100' :
                  stat.type === 'credits' ? 'bg-purple-100' : 'bg-orange-100'
                }`}>
                  {stat.type === 'revenue' && <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />}
                  {stat.type === 'users' && <Users className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />}
                  {stat.type === 'credits' && <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />}
                  {stat.type === 'content' && <Video className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />}
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">{stat.label}</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {stat.type === 'revenue' ? formatCurrency(stat.value) : stat.value.toLocaleString()}
                  </p>
                  {stat.changePercentage !== undefined && (
                    <div className={`flex items-center space-x-1 text-xs sm:text-sm ${getPercentageColor(stat.changePercentage)}`}>
                      <Icon className="w-3 h-3" />
                      <span>{formatPercentage(stat.changePercentage)} từ kỳ trước</span>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-slate-900">Xu hướng theo thời gian</h3>
            <div className="flex space-x-2">
              {(['revenue', 'users', 'credits', 'content'] as const).map((metric) => (
                <Button
                  key={metric}
                  variant={selectedMetric === metric ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleMetricChange(metric)}
                >
                  {metric === 'revenue' ? 'Doanh thu' :
                   metric === 'users' ? 'Người dùng' :
                   metric === 'credits' ? 'Credits' : 'Nội dung'}
                </Button>
              ))}
            </div>
          </div>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={analyticsData?.chartData || []}>
              <defs>
                <linearGradient id="colorMetric" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
              <XAxis dataKey="date" stroke="#64748b" />
              <YAxis stroke="#64748b" />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px'
                }}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke="#10b981"
                fillOpacity={1}
                fill="url(#colorMetric)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>

        <Card className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold text-slate-900 mb-4">Phân bố sử dụng Credits</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={creditUsageData || []}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              >
                {(creditUsageData || []).map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </Card>
      </div>

      {/* Top Performers */}
      <Card className="p-4 sm:p-6">
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Top người dùng theo Credits</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-200">
                <th className="text-left py-3 px-4 font-medium text-slate-700">Người dùng</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Credits đã dùng</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Nội dung tạo</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Doanh thu</th>
                <th className="text-left py-3 px-4 font-medium text-slate-700">Hạng</th>
              </tr>
            </thead>
            <tbody>
              {topPerformersLoading ? (
                <tr>
                  <td colSpan={5} className="py-8 text-center text-slate-500">
                    Đang tải dữ liệu...
                  </td>
                </tr>
              ) : (
                (topPerformers?.users || []).map((user, index) => (
                  <tr key={user.id} className="border-b border-slate-100 hover:bg-slate-50">
                    <td className="py-4 px-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                          <span className="text-white text-sm font-medium">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <span className="font-medium text-slate-900">{user.name}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className="bg-purple-100 text-purple-800">
                        {user.creditsUsed?.toLocaleString() || 0}
                      </Badge>
                    </td>
                    <td className="py-4 px-4 text-slate-600">
                      {(user.videosCreated || 0) + (user.photosCreated || 0)}
                    </td>
                    <td className="py-4 px-4 text-slate-600">
                      {formatCurrency(user.revenue || 0)}
                    </td>
                    <td className="py-4 px-4">
                      <Badge variant="outline">#{index + 1}</Badge>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;
