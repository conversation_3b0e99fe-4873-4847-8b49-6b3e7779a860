/**
 * UserCreditManagement Component
 * 
 * Feature component for managing users and credits in the Interface Layer.
 * Uses data hooks from Interface Layer for React Query integration.
 * Follows 3-layer MVI architecture principles.
 * 
 * Architecture Rules:
 * - Feature component belongs to Interface Layer
 * - Uses data hooks (useUsers, useCredits) for data management
 * - Uses Zod validation through data hooks
 * - No direct repository access (goes through hooks)
 * - Responsive design with Tailwind CSS
 */

import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { Button } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/interface/components/ui/tabs';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  UserPlus, 
  Mail, 
  Phone,
  CreditCard,
  Video,
  Image,
  Plus,
  Minus,
  TrendingUp,
  TrendingDown,
  Activity,
  Calendar,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';

// Import data hooks from Interface Layer
import {
  useUsers,
  useUserMutations,
  useCreditMutations,
  useUserCreditTransactions,
} from '@/interface/hooks/data';

import type { UserSearchParams } from '@/models';

const UserCreditManagement = () => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [creditAmount, setCreditAmount] = useState('');
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedUserDetails, setSelectedUserDetails] = useState<number | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'premium' | 'inactive'>('all');

  // ============================================================================
  // DATA HOOKS
  // ============================================================================

  // Search parameters for users
  const searchParams: UserSearchParams = {
    searchTerm: searchTerm || undefined,
    status: statusFilter === 'all' ? undefined : statusFilter,
    page: currentPage,
    limit: 10,
    sortBy: 'lastActivity',
    sortOrder: 'desc',
  };

  // Fetch users with React Query
  const {
    users,
    total,
    page,
    totalPages,
    isLoading: usersLoading,
    isError: usersError,
    error: usersErrorMessage,
    refetch: refetchUsers,
    hasNextPage,
    hasPreviousPage,
  } = useUsers(searchParams, {
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 30 * 1000, // Refresh every 30 seconds
  });

  // User mutations
  const {
    createUser,
    updateUser,
    deleteUser,
  } = useUserMutations();

  // Credit mutations
  const {
    addCredits,
    subtractCredits,
    useCredits,
    refundCredits,
  } = useCreditMutations();

  // Credit transactions for selected user
  const {
    transactions: userTransactions,
    isLoading: transactionsLoading,
  } = useUserCreditTransactions(
    selectedUserDetails || 0,
    { page: 1, limit: 10 },
    { enabled: !!selectedUserDetails }
  );

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleStatusFilter = (status: 'all' | 'active' | 'premium' | 'inactive') => {
    setStatusFilter(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleAddCredits = async () => {
    if (!selectedUser || !creditAmount) return;

    try {
      await addCredits.mutateAsync({
        userId: selectedUser,
        amount: parseInt(creditAmount),
        type: 'add',
        description: 'Credits added by admin',
        context: 'other',
      });
      
      setCreditAmount('');
      setSelectedUser(null);
      
      // Show success message (you can implement toast notifications)
      console.log('Credits added successfully');
    } catch (error) {
      console.error('Failed to add credits:', error);
      // Show error message
    }
  };

  const handleSubtractCredits = async () => {
    if (!selectedUser || !creditAmount) return;

    try {
      await subtractCredits.mutateAsync({
        userId: selectedUser,
        amount: parseInt(creditAmount),
        type: 'subtract',
        description: 'Credits subtracted by admin',
        context: 'other',
      });
      
      setCreditAmount('');
      setSelectedUser(null);
      
      console.log('Credits subtracted successfully');
    } catch (error) {
      console.error('Failed to subtract credits:', error);
    }
  };

  const handleViewUserDetails = (userId: number) => {
    setSelectedUserDetails(userId);
    setActiveTab('details');
  };

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================

  const totalCredits = users.reduce((sum, user) => sum + user.credits, 0);
  const activeUsers = users.filter(user => user.status === 'active').length;
  const premiumUsers = users.filter(user => user.status === 'premium').length;

  // ============================================================================
  // LOADING AND ERROR STATES
  // ============================================================================

  if (usersLoading) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
            <p className="mt-2 text-slate-600">Đang tải dữ liệu người dùng...</p>
          </div>
        </div>
      </div>
    );
  }

  if (usersError) {
    return (
      <div className="spacing-responsive">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600">Lỗi khi tải dữ liệu: {usersErrorMessage?.message}</p>
            <Button 
              onClick={() => refetchUsers()} 
              className="mt-4"
              variant="outline"
            >
              Thử lại
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="spacing-responsive">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-xl sm:text-2xl font-bold text-slate-900">Quản lý người dùng & Credits</h2>
          <p className="text-sm sm:text-base text-slate-600">
            Quản lý người dùng, credits và thống kê chi tiết theo tháng
          </p>
        </div>
        <Button 
          className="bg-indigo-600 hover:bg-indigo-700 flex-shrink-0"
          onClick={() => {/* Handle create user */}}
          disabled={createUser.isLoading}
        >
          <UserPlus className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Thêm người dùng</span>
          <span className="sm:hidden">Thêm</span>
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Tổng quan</TabsTrigger>
          <TabsTrigger value="details">Chi tiết</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Stats Overview */}
          <div className="grid-responsive-1-2-4 gap-4 sm:gap-6">
            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Tổng Credits hiện tại</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">
                    {totalCredits.toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Activity className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Người dùng hoạt động</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">{activeUsers}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Người dùng Premium</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">{premiumUsers}</p>
                </div>
              </div>
            </Card>

            <Card className="p-4 sm:p-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs sm:text-sm text-slate-600">Tổng người dùng</p>
                  <p className="text-lg sm:text-2xl font-bold text-slate-900">{total}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Search and Filter */}
          <Card className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                  <Input
                    placeholder="Tìm kiếm theo tên hoặc email..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={statusFilter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilter('all')}
                >
                  Tất cả
                </Button>
                <Button
                  variant={statusFilter === 'active' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilter('active')}
                >
                  Hoạt động
                </Button>
                <Button
                  variant={statusFilter === 'premium' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilter('premium')}
                >
                  Premium
                </Button>
                <Button
                  variant={statusFilter === 'inactive' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleStatusFilter('inactive')}
                >
                  Không hoạt động
                </Button>
              </div>
            </div>

            {/* Users Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Người dùng</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Credits</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Nội dung</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Trạng thái</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                            <span className="text-indigo-600 font-medium text-sm">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="min-w-0 flex-1">
                            <p className="font-medium text-slate-900 truncate">{user.name}</p>
                            <div className="flex items-center space-x-2 text-sm text-slate-500">
                              <Mail className="w-3 h-3" />
                              <span className="truncate">{user.email}</span>
                            </div>
                            {user.phone && (
                              <div className="flex items-center space-x-2 text-sm text-slate-500">
                                <Phone className="w-3 h-3" />
                                <span>{user.phone}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="text-center">
                          <p className="text-lg font-bold text-slate-900">{user.credits}</p>
                          <div className="flex items-center justify-center space-x-4 mt-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setSelectedUser(user.id)}
                              className="text-green-600 border-green-200 hover:bg-green-50"
                            >
                              <Plus className="w-3 h-3 mr-1" />
                              Thêm
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setSelectedUser(user.id)}
                              className="text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <Minus className="w-3 h-3 mr-1" />
                              Trừ
                            </Button>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <Video className="w-4 h-4 text-blue-600" />
                            <span>{user.videosCreated || 0}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Image className="w-4 h-4 text-green-600" />
                            <span>{user.photosCreated || 0}</span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge
                          variant={
                            user.status === 'active' ? 'default' :
                            user.status === 'premium' ? 'secondary' : 'outline'
                          }
                        >
                          {user.status === 'active' ? 'Hoạt động' :
                           user.status === 'premium' ? 'Premium' : 'Không hoạt động'}
                        </Badge>
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleViewUserDetails(user.id)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {/* Handle edit */}}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {/* Handle delete */}}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <p className="text-sm text-slate-600">
                  Hiển thị {((page - 1) * 10) + 1} - {Math.min(page * 10, total)} của {total} người dùng
                </p>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(page - 1)}
                    disabled={!hasPreviousPage}
                  >
                    Trước
                  </Button>
                  <span className="text-sm text-slate-600">
                    Trang {page} / {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(page + 1)}
                    disabled={!hasNextPage}
                  >
                    Sau
                  </Button>
                </div>
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="details" className="space-y-6">
          <p className="text-slate-600">Chi tiết người dùng sẽ được hiển thị ở đây</p>
        </TabsContent>
      </Tabs>

      {/* Credit Management Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              Quản lý Credits
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Số lượng Credits
                </label>
                <Input
                  type="number"
                  placeholder="Nhập số credits..."
                  value={creditAmount}
                  onChange={(e) => setCreditAmount(e.target.value)}
                  min="1"
                />
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={handleAddCredits}
                  disabled={!creditAmount || addCredits.isLoading}
                  className="flex-1 bg-green-600 hover:bg-green-700"
                >
                  {addCredits.isLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Plus className="w-4 h-4 mr-2" />
                  )}
                  Thêm Credits
                </Button>

                <Button
                  onClick={handleSubtractCredits}
                  disabled={!creditAmount || subtractCredits.isLoading}
                  variant="outline"
                  className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                >
                  {subtractCredits.isLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                  ) : (
                    <Minus className="w-4 h-4 mr-2" />
                  )}
                  Trừ Credits
                </Button>
              </div>

              <div className="flex space-x-3 pt-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedUser(null);
                    setCreditAmount('');
                  }}
                  className="flex-1"
                >
                  Hủy
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserCreditManagement;
