
import { useState } from 'react';
import { Card } from '@/interface/components/ui/card';
import { Button } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Badge } from '@/interface/components/ui/badge';
import { 
  CreditCard, 
  Plus, 
  Minus, 
  Search, 
  Filter,
  Calendar,
  User,
  TrendingUp,
  TrendingDown,
  History,
  DollarSign
} from 'lucide-react';

const CreditManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<number | null>(null);
  const [creditAmount, setCreditAmount] = useState('');
  const [activeTab, setActiveTab] = useState('overview');

  const users = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      currentCredits: 150,
      totalUsed: 450,
      totalAdded: 600
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: '<EMAIL>',
      currentCredits: 50,
      totalUsed: 200,
      totalAdded: 250
    },
    {
      id: 3,
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      currentCredits: 200,
      totalUsed: 800,
      totalAdded: 1000
    }
  ];

  const creditHistory = [
    {
      id: 1,
      userId: 1,
      userName: 'Nguyễn Văn A',
      type: 'add',
      amount: 100,
      reason: 'Nâng cấp gói Premium',
      date: '2024-01-15 10:30',
      adminUser: 'Admin'
    },
    {
      id: 2,
      userId: 1,
      userName: 'Nguyễn Văn A',
      type: 'subtract',
      amount: 50,
      reason: 'Tạo video marketing',
      date: '2024-01-15 14:20',
      adminUser: 'System'
    },
    {
      id: 3,
      userId: 2,
      userName: 'Trần Thị B',
      type: 'add',
      amount: 75,
      reason: 'Thêm credits thủ công',
      date: '2024-01-14 16:45',
      adminUser: 'Admin'
    },
    {
      id: 4,
      userId: 3,
      userName: 'Lê Văn C',
      type: 'subtract',
      amount: 25,
      reason: 'Tạo ảnh social media',
      date: '2024-01-14 09:15',
      adminUser: 'System'
    }
  ];

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddCredits = (userId: number, amount: number) => {
    console.log(`Adding ${amount} credits to user ${userId}`);
    // Implement add credits logic
  };

  const handleSubtractCredits = (userId: number, amount: number) => {
    console.log(`Subtracting ${amount} credits from user ${userId}`);
    // Implement subtract credits logic
  };

  const getTransactionIcon = (type: string) => {
    return type === 'add' ? 
      <TrendingUp className="w-4 h-4 text-green-600" /> : 
      <TrendingDown className="w-4 h-4 text-red-600" />;
  };

  const getTransactionColor = (type: string) => {
    return type === 'add' ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-slate-900">Quản lý Credits</h2>
        <p className="text-slate-600">Quản lý credits cá nhân của từng người dùng</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b border-slate-200">
        <button
          onClick={() => setActiveTab('overview')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'overview' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Tổng quan Credits
        </button>
        <button
          onClick={() => setActiveTab('history')}
          className={`pb-2 px-1 border-b-2 transition-colors ${
            activeTab === 'history' 
              ? 'border-indigo-600 text-indigo-600' 
              : 'border-transparent text-slate-500 hover:text-slate-700'
          }`}
        >
          Lịch sử giao dịch
        </button>
      </div>

      {activeTab === 'overview' && (
        <>
          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Tổng Credits hiện tại</p>
                  <p className="text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.currentCredits, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Plus className="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Credits đã thêm</p>
                  <p className="text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.totalAdded, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                  <Minus className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Credits đã sử dụng</p>
                  <p className="text-2xl font-bold text-slate-900">
                    {users.reduce((sum, user) => sum + user.totalUsed, 0).toLocaleString()}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <User className="w-6 h-6 text-purple-600" />
                </div>
                <div>
                  <p className="text-sm text-slate-600">Người dùng active</p>
                  <p className="text-2xl font-bold text-slate-900">{users.length}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* User Credits Management */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-slate-900">Quản lý Credits người dùng</h3>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                  <Input
                    placeholder="Tìm kiếm người dùng..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 w-80"
                  />
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-slate-200">
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Người dùng</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Credits hiện tại</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Đã sử dụng</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Đã thêm</th>
                    <th className="text-left py-3 px-4 font-medium text-slate-700">Thao tác</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b border-slate-100 hover:bg-slate-50">
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                            <span className="text-white font-medium">
                              {user.name.split(' ').map(n => n[0]).join('').substring(0, 2)}
                            </span>
                          </div>
                          <div>
                            <p className="font-medium text-slate-900">{user.name}</p>
                            <p className="text-sm text-slate-500">{user.email}</p>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-4">
                        <Badge className="bg-blue-100 text-blue-800">
                          {user.currentCredits.toLocaleString()} credits
                        </Badge>
                      </td>
                      <td className="py-4 px-4 text-slate-600">
                        {user.totalUsed.toLocaleString()}
                      </td>
                      <td className="py-4 px-4 text-slate-600">
                        {user.totalAdded.toLocaleString()}
                      </td>
                      <td className="py-4 px-4">
                        <div className="flex items-center space-x-2">
                          {selectedUser === user.id ? (
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                placeholder="Số credits"
                                value={creditAmount}
                                onChange={(e) => setCreditAmount(e.target.value)}
                                className="w-24"
                              />
                              <Button
                                size="sm"
                                onClick={() => {
                                  handleAddCredits(user.id, parseInt(creditAmount));
                                  setSelectedUser(null);
                                  setCreditAmount('');
                                }}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <Plus className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  handleSubtractCredits(user.id, parseInt(creditAmount));
                                  setSelectedUser(null);
                                  setCreditAmount('');
                                }}
                                className="border-red-200 text-red-600 hover:bg-red-50"
                              >
                                <Minus className="w-3 h-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedUser(null);
                                  setCreditAmount('');
                                }}
                              >
                                Hủy
                              </Button>
                            </div>
                          ) : (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setSelectedUser(user.id)}
                            >
                              <CreditCard className="w-3 h-3 mr-1" />
                              Quản lý
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </Card>
        </>
      )}

      {activeTab === 'history' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-slate-900">Lịch sử giao dịch Credits</h3>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Lọc theo thời gian
            </Button>
          </div>

          <div className="space-y-3">
            {creditHistory.map((transaction) => (
              <div
                key={transaction.id}
                className="flex items-center justify-between p-4 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors"
              >
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-sm">
                    {getTransactionIcon(transaction.type)}
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-slate-900">{transaction.userName}</p>
                      <Badge variant="outline" className="text-xs">
                        {transaction.type === 'add' ? 'Thêm' : 'Sử dụng'}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-600">{transaction.reason}</p>
                    <p className="text-xs text-slate-500">
                      Bởi {transaction.adminUser} • {transaction.date}
                    </p>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className={`text-lg font-semibold ${getTransactionColor(transaction.type)}`}>
                    {transaction.type === 'add' ? '+' : '-'}{transaction.amount} credits
                  </p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default CreditManagement;
