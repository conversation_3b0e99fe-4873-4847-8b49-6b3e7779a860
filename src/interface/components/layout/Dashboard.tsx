/**
 * Dashboard Layout Component
 * 
 * Main layout component for the dashboard in the Interface Layer.
 * Integrates all feature components and provides navigation.
 * Follows 3-layer MVI architecture principles.
 * 
 * Architecture Rules:
 * - Layout component belongs to Interface Layer
 * - Uses feature components from Interface Layer
 * - Manages navigation and layout state
 * - Responsive design with Tailwind CSS
 */

import { useState } from 'react';
import { 
  Users, 
  Search,
  Bell,
  User,
  CreditCard,
  Video
} from 'lucide-react';
import { Button } from '@/interface/components/ui/button';
import { Input } from '@/interface/components/ui/input';
import { Card } from '@/interface/components/ui/card';

// Import layout components
import Sidebar from './Sidebar';

// Import feature components from Interface Layer (temporarily commented out)
// import {
//   UserCreditManagement,
//   ContentManagement,
//   Analytics,
//   NotificationManagement,
//   SystemSettings,
// } from '@/interface/components/features';

// Import legacy components (to be migrated)
import EnhancedStatsCard from '@/interface/components/ui/EnhancedStatsCard';
import UserGrowthChart from '@/interface/components/ui/UserGrowthChart';

interface DashboardProps {
  onLogout: () => void;
}

const Dashboard = ({ onLogout }: DashboardProps) => {
  // ============================================================================
  // STATE MANAGEMENT
  // ============================================================================
  
  const [activeSection, setActiveSection] = useState('dashboard');
  const [searchQuery, setSearchQuery] = useState('');

  // ============================================================================
  // MOCK DATA (TO BE REPLACED WITH DATA HOOKS)
  // ============================================================================
  
  const statsData = [
    {
      title: 'Tổng người dùng',
      value: '2,847',
      change: '+12.5%',
      trend: 'up' as const,
      icon: Users,
      color: 'blue' as const
    },
    {
      title: 'Credits đã sử dụng',
      value: '45,672',
      change: '+8.2%',
      trend: 'up' as const,
      icon: CreditCard,
      color: 'green' as const
    },
    {
      title: 'Videos tạo ra',
      value: '1,234',
      change: '+15.8%',
      trend: 'up' as const,
      icon: Video,
      color: 'purple' as const
    }
  ];

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  const handleSectionChange = (section: string) => {
    setActiveSection(section);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Searching for:', searchQuery);
  };

  // ============================================================================
  // RENDER CONTENT
  // ============================================================================

  const renderContent = () => {
    switch (activeSection) {
      case 'users':
        return <div className="p-6"><h2 className="text-xl font-bold">Quản lý người dùng</h2><p>Tính năng đang phát triển...</p></div>;
      case 'content':
        return <div className="p-6"><h2 className="text-xl font-bold">Quản lý nội dung</h2><p>Tính năng đang phát triển...</p></div>;
      case 'analytics':
        return <div className="p-6"><h2 className="text-xl font-bold">Báo cáo & Phân tích</h2><p>Tính năng đang phát triển...</p></div>;
      case 'notifications':
        return <div className="p-6"><h2 className="text-xl font-bold">Quản lý thông báo</h2><p>Tính năng đang phát triển...</p></div>;
      case 'settings':
        return <div className="p-6"><h2 className="text-xl font-bold">Cài đặt hệ thống</h2><p>Tính năng đang phát triển...</p></div>;
      default:
        return (
          <div className="spacing-responsive">
            {/* Enhanced Stats Grid */}
            <div className="grid-responsive-1-2-3 gap-4 sm:gap-6">
              {statsData.map((stat, index) => (
                <EnhancedStatsCard key={index} {...stat} />
              ))}
            </div>

            {/* User Growth Chart */}
            <Card className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                Tăng trưởng người dùng
              </h3>
              <UserGrowthChart />
            </Card>

            {/* Quick Actions */}
            <Card className="p-4 sm:p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">
                Thao tác nhanh
              </h3>
              <div className="grid-responsive-1-2-4 gap-4">
                <Button 
                  onClick={() => setActiveSection('users')}
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  variant="outline"
                >
                  <Users className="w-6 h-6" />
                  <span>Quản lý người dùng</span>
                </Button>
                <Button 
                  onClick={() => setActiveSection('content')}
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  variant="outline"
                >
                  <Video className="w-6 h-6" />
                  <span>Quản lý nội dung</span>
                </Button>
                <Button 
                  onClick={() => setActiveSection('analytics')}
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  variant="outline"
                >
                  <CreditCard className="w-6 h-6" />
                  <span>Báo cáo</span>
                </Button>
                <Button 
                  onClick={() => setActiveSection('settings')}
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  variant="outline"
                >
                  <User className="w-6 h-6" />
                  <span>Cài đặt</span>
                </Button>
              </div>
            </Card>
          </div>
        );
    }
  };

  // ============================================================================
  // RENDER COMPONENT
  // ============================================================================

  return (
    <div className="flex h-screen bg-slate-50">
      {/* Sidebar */}
      <Sidebar 
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
        onLogout={onLogout}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white border-b border-slate-200 px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              <form onSubmit={handleSearch} className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                  <Input
                    type="text"
                    placeholder="Tìm kiếm..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-full"
                  />
                </div>
              </form>
            </div>
            
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Bell className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <User className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 overflow-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
