/**
 * Layout Components - Central Export
 *
 * This module exports all layout components for the Interface Layer.
 * Layout components handle structure, navigation, and page organization.
 *
 * Architecture Rules:
 * - Layout components manage application structure
 * - Handle navigation and routing
 * - Provide consistent layout patterns
 * - Responsive design implementation
 */

// ============================================================================
// MAIN LAYOUT COMPONENTS
// ============================================================================

export { default as Dashboard } from './Dashboard';
export { default as Sidebar } from './Sidebar';

// ============================================================================
// LAYOUT COMPONENTS METADATA
// ============================================================================

/**
 * Layout components metadata for debugging and documentation
 */
export const LAYOUT_COMPONENTS_INFO = {
  version: '1.0.0',
  description: 'Layout components for 3-layer MVI architecture',
  architecture: '3-layer MVI',

  components: {
    main: ['Dashboard', 'Sidebar'],
    future: ['Header', 'Footer', 'Navigation'],
  },

  features: [
    'Responsive navigation',
    'Collapsible sidebar',
    'Mobile-first design',
    'Consistent layout patterns',
    'Accessibility support',
  ],

  patterns: [
    'Layout components manage structure',
    'Feature components handle business logic',
    'Consistent navigation patterns',
    'Mobile responsive design',
  ],
} as const;
export type * from './MainLayout';
export type * from './LoadingLayout';
