/**
 * UI Components - Central Export
 *
 * This module exports all UI components for the Interface Layer.
 * UI components are reusable, generic components (shadcn/ui based).
 *
 * Architecture Rules:
 * - UI components are generic and reusable
 * - Based on shadcn/ui design system
 * - No business logic, only presentation
 * - Consistent styling and behavior
 */

// ============================================================================
// SHADCN/UI COMPONENTS
// ============================================================================

// Form Components
export * from './button';
export * from './input';
export * from './label';
export * from './textarea';
export * from './select';
export * from './checkbox';
export * from './radio-group';
export * from './switch';
export * from './slider';
export * from './form';

// Layout Components
export * from './card';
export * from './separator';
export * from './aspect-ratio';
export * from './scroll-area';
export * from './resizable';

// Navigation Components
export * from './navigation-menu';
export * from './menubar';
export * from './breadcrumb';
export * from './pagination';
export * from './tabs';

// Feedback Components
export * from './alert';
export * from './alert-dialog';
export * from './toast';
export * from './toaster';
export * from './sonner';
export * from './progress';
export * from './skeleton';
export * from './badge';

// Overlay Components
export * from './dialog';
export * from './sheet';
export * from './popover';
export * from './hover-card';
export * from './tooltip';
export * from './context-menu';
export * from './dropdown-menu';
export * from './command';

// Data Display Components
export * from './table';
export * from './avatar';
export * from './calendar';
export * from './carousel';
export * from './chart';
export * from './collapsible';

// Utility Components
export * from './accordion';
export * from './toggle';
export * from './toggle-group';
export * from './drawer';
export * from './sidebar';
export * from './input-otp';

// ============================================================================
// CUSTOM UI COMPONENTS
// ============================================================================

export { default as EnhancedStatsCard } from './EnhancedStatsCard';
export { default as StatsCard } from './StatsCard';
export { default as Chart } from './Chart';
export { default as UserGrowthChart } from './UserGrowthChart';

// ============================================================================
// HOOKS
// ============================================================================

export * from './use-toast';

// ============================================================================
// UI COMPONENTS METADATA
// ============================================================================

/**
 * UI components metadata for debugging and documentation
 */
export const UI_COMPONENTS_INFO = {
  version: '1.0.0',
  description: 'UI components based on shadcn/ui design system',
  architecture: '3-layer MVI',

  categories: {
    form: ['button', 'input', 'label', 'textarea', 'select', 'checkbox', 'radio-group', 'switch', 'slider', 'form'],
    layout: ['card', 'separator', 'aspect-ratio', 'scroll-area', 'resizable'],
    navigation: ['navigation-menu', 'menubar', 'breadcrumb', 'pagination', 'tabs'],
    feedback: ['alert', 'alert-dialog', 'toast', 'toaster', 'sonner', 'progress', 'skeleton', 'badge'],
    overlay: ['dialog', 'sheet', 'popover', 'hover-card', 'tooltip', 'context-menu', 'dropdown-menu', 'command'],
    dataDisplay: ['table', 'avatar', 'calendar', 'carousel', 'chart', 'collapsible'],
    utility: ['accordion', 'toggle', 'toggle-group', 'drawer', 'sidebar', 'input-otp'],
    custom: ['EnhancedStatsCard', 'StatsCard', 'Chart', 'UserGrowthChart'],
  },

  features: [
    'shadcn/ui design system',
    'Tailwind CSS styling',
    'Radix UI primitives',
    'Accessibility support',
    'Dark mode support',
    'Responsive design',
    'TypeScript support',
  ],

  patterns: [
    'Compound component pattern',
    'Forwarded refs',
    'Variant-based styling',
    'Consistent API design',
    'Accessibility first',
  ],
} as const;
