/**
 * Mock Data Source
 * 
 * Comprehensive mock data matching EXACTLY current component data structures
 * from UserCreditManagement.tsx and Dashboard.tsx
 * 
 * Architecture Rules:
 * - Data Sources belong in Data Layer
 * - Mock data must match existing component data exactly
 * - Realistic and comprehensive data for testing
 * - Uses types from Model Layer
 */

import type {
  User,
  DashboardStat,
  QuickStat,
  CreditTransaction,
  Content,
  Notification,
  AnalyticsData,
  UserGrowthData,
  CreditUsageData,
  ContentCreationData,
  SystemStatus,
} from '@/models';

// ============================================================================
// USER MOCK DATA - EXACT MATCH FROM UserCreditManagement.tsx
// ============================================================================

/**
 * Mock users data - matches exactly with users array from UserCreditManagement.tsx
 */
export const MOCK_USERS: User[] = [
  {
    id: 1,
    name: 'Nguyễn Văn A',
    email: '<EMAIL>',
    phone: '+84 123 456 789',
    credits: 150,
    videosCreated: 25,
    photosCreated: 80,
    totalUsed: 450,
    totalAdded: 600,
    monthlyStats: {
      videosCompleted: 12,
      videosErrors: 2,
      photosCompleted: 45,
      photosErrors: 3,
      creditsUsed: 180,
      creditsAdded: 200
    },
    lastActivity: '2024-01-15T14:30:00.000Z',
    joinDate: '2023-06-15T00:00:00.000Z',
    status: 'active'
  },
  {
    id: 2,
    name: 'Trần Thị B',
    email: '<EMAIL>',
    phone: '+84 987 654 321',
    credits: 50,
    videosCreated: 8,
    photosCreated: 30,
    totalUsed: 200,
    totalAdded: 250,
    monthlyStats: {
      videosCompleted: 5,
      videosErrors: 1,
      photosCompleted: 20,
      photosErrors: 2,
      creditsUsed: 75,
      creditsAdded: 100
    },
    lastActivity: '2024-01-14T09:15:00.000Z',
    joinDate: '2023-08-20T00:00:00.000Z',
    status: 'active'
  },
  {
    id: 3,
    name: 'Lê Văn C',
    email: '<EMAIL>',
    phone: '+84 555 666 777',
    credits: 200,
    videosCreated: 45,
    photosCreated: 120,
    totalUsed: 800,
    totalAdded: 1000,
    monthlyStats: {
      videosCompleted: 18,
      videosErrors: 3,
      photosCompleted: 65,
      photosErrors: 1,
      creditsUsed: 280,
      creditsAdded: 300
    },
    lastActivity: '2024-01-15T16:45:00.000Z',
    joinDate: '2023-03-10T00:00:00.000Z',
    status: 'premium'
  },
  // Additional users for comprehensive testing
  {
    id: 4,
    name: 'Phạm Thị D',
    email: '<EMAIL>',
    phone: '+84 111 222 333',
    credits: 75,
    videosCreated: 15,
    photosCreated: 50,
    totalUsed: 300,
    totalAdded: 375,
    monthlyStats: {
      videosCompleted: 8,
      videosErrors: 1,
      photosCompleted: 25,
      photosErrors: 2,
      creditsUsed: 120,
      creditsAdded: 150
    },
    lastActivity: '2024-01-13T11:20:00.000Z',
    joinDate: '2023-09-05T00:00:00.000Z',
    status: 'active'
  },
  {
    id: 5,
    name: 'Hoàng Văn E',
    email: '<EMAIL>',
    phone: '+84 444 555 666',
    credits: 0,
    videosCreated: 2,
    photosCreated: 5,
    totalUsed: 50,
    totalAdded: 50,
    monthlyStats: {
      videosCompleted: 1,
      videosErrors: 1,
      photosCompleted: 3,
      photosErrors: 1,
      creditsUsed: 25,
      creditsAdded: 0
    },
    lastActivity: '2024-01-10T08:30:00.000Z',
    joinDate: '2024-01-01T00:00:00.000Z',
    status: 'inactive'
  }
];

// ============================================================================
// DASHBOARD STATS - EXACT MATCH FROM Dashboard.tsx
// ============================================================================

/**
 * Mock dashboard stats - matches exactly with statsData from Dashboard.tsx
 */
export const MOCK_DASHBOARD_STATS: DashboardStat[] = [
  {
    title: 'Tổng người dùng cá nhân',
    value: '12,450',
    change: '+12.5%',
    changeType: 'positive',
    icon: 'Users' as any, // Will be replaced with actual icon in components
    gradient: 'bg-gradient-to-r from-blue-500 to-blue-600',
    subtitle: '89 đang hoạt động',
    trend: [20, 35, 25, 40, 30, 45, 50]
  },
  {
    title: 'Credits cá nhân đã dùng',
    value: '28,400',
    change: '+23.5%',
    changeType: 'positive',
    icon: 'CreditCard' as any,
    gradient: 'bg-gradient-to-r from-green-500 to-green-600',
    subtitle: '456 hôm nay',
    trend: [30, 20, 45, 35, 50, 40, 60]
  },
  {
    title: 'Videos cá nhân',
    value: '8,942',
    change: '+18.2%',
    changeType: 'positive',
    icon: 'Video' as any,
    gradient: 'bg-gradient-to-r from-purple-500 to-purple-600',
    subtitle: '342 hôm nay',
    trend: [15, 25, 20, 35, 25, 40, 45]
  }
];

/**
 * Mock quick stats - matches exactly with quickStats from Dashboard.tsx
 */
export const MOCK_QUICK_STATS: QuickStat[] = [
  { 
    label: 'Credits còn lại hệ thống', 
    value: '2.4M', 
    color: 'text-green-600' 
  },
  { 
    label: 'Người dùng mới hôm nay', 
    value: '12', 
    color: 'text-blue-600' 
  },
  { 
    label: 'Credits đã phát hành', 
    value: '3.2M', 
    color: 'text-purple-600' 
  }
];

// ============================================================================
// CREDIT TRANSACTIONS - EXACT MATCH FROM CreditManagement.tsx
// ============================================================================

/**
 * Mock credit transactions - matches exactly with creditHistory from CreditManagement.tsx
 */
export const MOCK_CREDIT_TRANSACTIONS: CreditTransaction[] = [
  {
    id: '1',
    userId: 1,
    type: 'add',
    amount: 100,
    balanceAfter: 250,
    status: 'completed',
    context: 'premium_feature',
    description: 'Nâng cấp gói Premium',
    createdAt: '2024-01-15T10:30:00.000Z',
    adminId: 1
  },
  {
    id: '2',
    userId: 1,
    type: 'used',
    amount: -50,
    balanceAfter: 200,
    status: 'completed',
    context: 'video_creation',
    description: 'Tạo video marketing',
    createdAt: '2024-01-15T14:20:00.000Z',
    contentId: 'video_001'
  },
  {
    id: '3',
    userId: 2,
    type: 'add',
    amount: 75,
    balanceAfter: 125,
    status: 'completed',
    context: 'other',
    description: 'Thêm credits thủ công',
    createdAt: '2024-01-14T16:45:00.000Z',
    adminId: 1
  },
  {
    id: '4',
    userId: 3,
    type: 'used',
    amount: -25,
    balanceAfter: 175,
    status: 'completed',
    context: 'photo_creation',
    description: 'Tạo ảnh social media',
    createdAt: '2024-01-14T09:15:00.000Z',
    contentId: 'photo_001'
  }
];

// ============================================================================
// CONTENT MOCK DATA
// ============================================================================

/**
 * Mock content data
 */
export const MOCK_CONTENT: Content[] = [
  {
    id: 'video_001',
    userId: 1,
    type: 'video',
    title: 'Video Marketing Campaign',
    description: 'Promotional video for new product launch',
    status: 'completed',
    quality: 'high',
    visibility: 'public',
    creditsCost: 50,
    createdAt: '2024-01-15T14:20:00.000Z',
    updatedAt: '2024-01-15T14:25:00.000Z',
    processingStartedAt: '2024-01-15T14:20:30.000Z',
    processingCompletedAt: '2024-01-15T14:24:45.000Z',
    fileSize: 15728640, // 15MB
    originalFileName: 'marketing_video.mp4',
    fileUrl: 'https://cdn.example.com/videos/video_001.mp4',
    thumbnailUrl: 'https://cdn.example.com/thumbnails/video_001.jpg',
    tags: ['marketing', 'product', 'campaign'],
    metadata: {
      duration: 120,
      width: 1920,
      height: 1080,
      frameRate: 30,
      codec: 'h264',
      bitrate: 5000
    }
  },
  {
    id: 'photo_001',
    userId: 3,
    type: 'photo',
    title: 'Social Media Banner',
    description: 'Instagram story banner for promotion',
    status: 'completed',
    quality: 'medium',
    visibility: 'public',
    creditsCost: 25,
    createdAt: '2024-01-14T09:15:00.000Z',
    updatedAt: '2024-01-14T09:18:00.000Z',
    processingStartedAt: '2024-01-14T09:15:30.000Z',
    processingCompletedAt: '2024-01-14T09:17:45.000Z',
    fileSize: 2097152, // 2MB
    originalFileName: 'social_banner.png',
    fileUrl: 'https://cdn.example.com/photos/photo_001.png',
    thumbnailUrl: 'https://cdn.example.com/thumbnails/photo_001.jpg',
    tags: ['social', 'banner', 'instagram'],
    metadata: {
      width: 1080,
      height: 1920,
      format: 'png',
      colorSpace: 'sRGB',
      dpi: 300
    }
  }
];

// ============================================================================
// ANALYTICS MOCK DATA
// ============================================================================

/**
 * Mock analytics data
 */
export const MOCK_ANALYTICS_DATA: AnalyticsData = {
  totalUsers: 12450,
  activeUsers: 8932,
  premiumUsers: 1245,
  inactiveUsers: 2273,
  totalCreditsUsed: 28400,
  totalCreditsAdded: 32000,
  remainingCredits: 2400000,
  totalVideos: 8942,
  totalPhotos: 15678,
  newUsersToday: 12,
  creditsUsedToday: 456,
  videosToday: 342,
  photosToday: 128
};

/**
 * Mock user growth data
 */
export const MOCK_USER_GROWTH_DATA: UserGrowthData[] = [
  { date: '2024-01-01', newUsers: 45, activeUsers: 8500, totalUsers: 12000 },
  { date: '2024-01-02', newUsers: 52, activeUsers: 8600, totalUsers: 12052 },
  { date: '2024-01-03', newUsers: 38, activeUsers: 8550, totalUsers: 12090 },
  { date: '2024-01-04', newUsers: 67, activeUsers: 8700, totalUsers: 12157 },
  { date: '2024-01-05', newUsers: 43, activeUsers: 8650, totalUsers: 12200 },
  { date: '2024-01-06', newUsers: 59, activeUsers: 8750, totalUsers: 12259 },
  { date: '2024-01-07', newUsers: 71, activeUsers: 8800, totalUsers: 12330 },
  { date: '2024-01-08', newUsers: 48, activeUsers: 8720, totalUsers: 12378 },
  { date: '2024-01-09', newUsers: 55, activeUsers: 8780, totalUsers: 12433 },
  { date: '2024-01-10', newUsers: 62, activeUsers: 8850, totalUsers: 12495 }
];

/**
 * Mock credit usage data
 */
export const MOCK_CREDIT_USAGE_DATA: CreditUsageData[] = [
  { date: '2024-01-01', creditsUsed: 1200, creditsAdded: 1500, netChange: 300 },
  { date: '2024-01-02', creditsUsed: 1350, creditsAdded: 1200, netChange: -150 },
  { date: '2024-01-03', creditsUsed: 980, creditsAdded: 1800, netChange: 820 },
  { date: '2024-01-04', creditsUsed: 1450, creditsAdded: 1100, netChange: -350 },
  { date: '2024-01-05', creditsUsed: 1100, creditsAdded: 1600, netChange: 500 },
  { date: '2024-01-06', creditsUsed: 1300, creditsAdded: 1400, netChange: 100 },
  { date: '2024-01-07', creditsUsed: 1550, creditsAdded: 1300, netChange: -250 },
  { date: '2024-01-08', creditsUsed: 1250, creditsAdded: 1700, netChange: 450 },
  { date: '2024-01-09', creditsUsed: 1400, creditsAdded: 1200, netChange: -200 },
  { date: '2024-01-10', creditsUsed: 1600, creditsAdded: 1500, netChange: -100 }
];

/**
 * Mock content creation data
 */
export const MOCK_CONTENT_CREATION_DATA: ContentCreationData[] = [
  { date: '2024-01-01', videos: 45, photos: 78, total: 123, videoSuccessRate: 92, photoSuccessRate: 96 },
  { date: '2024-01-02', videos: 52, photos: 85, total: 137, videoSuccessRate: 94, photoSuccessRate: 95 },
  { date: '2024-01-03', videos: 38, photos: 62, total: 100, videoSuccessRate: 89, photoSuccessRate: 97 },
  { date: '2024-01-04', videos: 67, photos: 92, total: 159, videoSuccessRate: 91, photoSuccessRate: 94 },
  { date: '2024-01-05', videos: 43, photos: 71, total: 114, videoSuccessRate: 93, photoSuccessRate: 96 },
  { date: '2024-01-06', videos: 59, photos: 88, total: 147, videoSuccessRate: 90, photoSuccessRate: 95 },
  { date: '2024-01-07', videos: 71, photos: 105, total: 176, videoSuccessRate: 92, photoSuccessRate: 97 },
  { date: '2024-01-08', videos: 48, photos: 76, total: 124, videoSuccessRate: 94, photoSuccessRate: 96 },
  { date: '2024-01-09', videos: 55, photos: 83, total: 138, videoSuccessRate: 91, photoSuccessRate: 95 },
  { date: '2024-01-10', videos: 62, photos: 94, total: 156, videoSuccessRate: 93, photoSuccessRate: 96 }
];

// ============================================================================
// NOTIFICATIONS MOCK DATA
// ============================================================================

/**
 * Mock notifications data
 */
export const MOCK_NOTIFICATIONS: Notification[] = [
  {
    id: 'notif_001',
    title: 'Hệ thống bảo trì',
    message: 'Hệ thống sẽ được bảo trì vào 2:00 AM ngày mai. Thời gian dự kiến: 2 giờ.',
    type: 'info',
    priority: 'medium',
    status: 'sent',
    target: 'all_users',
    deliveryMethods: ['in_app', 'email'],
    createdBy: 1,
    createdAt: '2024-01-15T10:00:00.000Z',
    updatedAt: '2024-01-15T10:00:00.000Z',
    sentAt: '2024-01-15T10:05:00.000Z',
    expiresAt: '2024-01-16T10:00:00.000Z'
  },
  {
    id: 'notif_002',
    title: 'Tính năng mới',
    message: 'Chúng tôi đã thêm tính năng tạo video HD. Hãy thử ngay!',
    type: 'announcement',
    priority: 'high',
    status: 'delivered',
    target: 'all_users',
    deliveryMethods: ['in_app', 'email', 'push'],
    createdBy: 1,
    createdAt: '2024-01-14T15:30:00.000Z',
    updatedAt: '2024-01-14T15:30:00.000Z',
    sentAt: '2024-01-14T15:35:00.000Z'
  }
];

// ============================================================================
// SYSTEM STATUS MOCK DATA
// ============================================================================

/**
 * Mock system status data
 */
export const MOCK_SYSTEM_STATUS: SystemStatus = {
  status: 'healthy',
  version: '1.0.0',
  uptime: 2592000, // 30 days in seconds
  services: [
    {
      name: 'API Server',
      status: 'up',
      responseTime: 45,
      lastCheck: '2024-01-15T16:00:00.000Z'
    },
    {
      name: 'Database',
      status: 'up',
      responseTime: 12,
      lastCheck: '2024-01-15T16:00:00.000Z'
    },
    {
      name: 'File Storage',
      status: 'up',
      responseTime: 23,
      lastCheck: '2024-01-15T16:00:00.000Z'
    },
    {
      name: 'Processing Queue',
      status: 'up',
      responseTime: 8,
      lastCheck: '2024-01-15T16:00:00.000Z'
    }
  ],
  metrics: {
    cpu: 35,
    memory: 68,
    disk: 42,
    network: 15,
    activeUsers: 89,
    totalRequests: 15420,
    errorRate: 0.2
  },
  database: {
    status: 'connected',
    responseTime: 12,
    connections: 45,
    lastBackup: '2024-01-15T02:00:00.000Z'
  },
  lastUpdated: '2024-01-15T16:00:00.000Z'
};

// ============================================================================
// MOCK DATA UTILITIES
// ============================================================================

/**
 * Mock data utility functions
 */
export const MockDataUtils = {
  /**
   * Get user by ID
   */
  getUserById: (id: number): User | undefined => {
    return MOCK_USERS.find(user => user.id === id);
  },

  /**
   * Get users by status
   */
  getUsersByStatus: (status: 'active' | 'premium' | 'inactive'): User[] => {
    return MOCK_USERS.filter(user => user.status === status);
  },

  /**
   * Get credit transactions for user
   */
  getCreditTransactionsByUserId: (userId: number): CreditTransaction[] => {
    return MOCK_CREDIT_TRANSACTIONS.filter(transaction => transaction.userId === userId);
  },

  /**
   * Get content by user ID
   */
  getContentByUserId: (userId: number): Content[] => {
    return MOCK_CONTENT.filter(content => content.userId === userId);
  },

  /**
   * Calculate total credits in system
   */
  getTotalSystemCredits: (): number => {
    return MOCK_USERS.reduce((total, user) => total + user.credits, 0);
  },

  /**
   * Calculate total content created
   */
  getTotalContentCreated: (): { videos: number; photos: number } => {
    return MOCK_USERS.reduce(
      (totals, user) => ({
        videos: totals.videos + user.videosCreated,
        photos: totals.photos + user.photosCreated
      }),
      { videos: 0, photos: 0 }
    );
  },

  /**
   * Generate random delay for simulating API calls
   */
  getRandomDelay: (min: number = 100, max: number = 500): number => {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Simulate API response with delay
   */
  simulateApiCall: async <T>(data: T, delay?: number): Promise<T> => {
    const actualDelay = delay || MockDataUtils.getRandomDelay();
    await new Promise(resolve => setTimeout(resolve, actualDelay));
    return data;
  },

  /**
   * Simulate API error
   */
  simulateApiError: async (errorMessage: string = 'Mock API Error', delay?: number): Promise<never> => {
    const actualDelay = delay || MockDataUtils.getRandomDelay();
    await new Promise(resolve => setTimeout(resolve, actualDelay));
    throw new Error(errorMessage);
  }
};

// ============================================================================
// EXPORT ALL MOCK DATA
// ============================================================================

/**
 * All mock data combined for easy access
 */
export const ALL_MOCK_DATA = {
  USERS: MOCK_USERS,
  DASHBOARD_STATS: MOCK_DASHBOARD_STATS,
  QUICK_STATS: MOCK_QUICK_STATS,
  CREDIT_TRANSACTIONS: MOCK_CREDIT_TRANSACTIONS,
  CONTENT: MOCK_CONTENT,
  ANALYTICS: MOCK_ANALYTICS_DATA,
  USER_GROWTH: MOCK_USER_GROWTH_DATA,
  CREDIT_USAGE: MOCK_CREDIT_USAGE_DATA,
  CONTENT_CREATION: MOCK_CONTENT_CREATION_DATA,
  NOTIFICATIONS: MOCK_NOTIFICATIONS,
  SYSTEM_STATUS: MOCK_SYSTEM_STATUS,
  UTILS: MockDataUtils
} as const;
