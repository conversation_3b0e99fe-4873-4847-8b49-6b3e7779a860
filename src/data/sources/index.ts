/**
 * Data Sources - Central Export
 * 
 * This module exports all data sources used in the Data Layer.
 * Data sources handle different types of data storage and retrieval.
 * 
 * Architecture Rules:
 * - Data Sources belong in Data Layer
 * - Handle data storage, caching, and mock data
 * - Can import from Model Layer for types
 * - NO business logic (belongs in repositories)
 */

// ============================================================================
// MOCK DATA SOURCE
// ============================================================================

export {
  // Mock data arrays
  MOCK_USERS,
  MOCK_DASHBOARD_STATS,
  MOCK_QUICK_STATS,
  MOCK_CREDIT_TRANSACTIONS,
  MOCK_CONTENT,
  MOCK_ANALYTICS_DATA,
  MOCK_USER_GROWTH_DATA,
  MOCK_CREDIT_USAGE_DATA,
  MOCK_CONTENT_CREATION_DATA,
  MOCK_NOTIFICATIONS,
  MOCK_SYSTEM_STATUS,
  
  // Mock data utilities
  MockDataUtils,
  ALL_MOCK_DATA,
} from './mockDataSource';

// ============================================================================
// LOCAL STORAGE SOURCE
// ============================================================================

export {
  // Storage classes
  LocalStorageSource,
  SessionStorageSource,
  CookieSource,
  
  // Specific storage operations
  AuthStorage,
  PreferencesStorage,
  DashboardStorage,
  CacheStorage,
  
  // Storage utilities
  StorageUtils,
} from './localStorageSource';

// ============================================================================
// CACHE SOURCE
// ============================================================================

export {
  // Cache classes
  MemoryCache,
  PersistentCache,
  CacheManager,
  
  // Global cache instance
  globalCache,
  
  // Cache utilities
  CacheUtils,
} from './cacheSource';

// ============================================================================
// DATA SOURCE UTILITIES
// ============================================================================

/**
 * Data source utility functions
 */
export const DataSourceUtils = {
  /**
   * Check if running in browser environment
   */
  isBrowser: (): boolean => {
    return typeof window !== 'undefined' && typeof localStorage !== 'undefined';
  },

  /**
   * Check if running in development mode
   */
  isDevelopment: (): boolean => {
    return process.env.NODE_ENV === 'development';
  },

  /**
   * Check if mock data should be used
   */
  shouldUseMockData: (): boolean => {
    return (
      DataSourceUtils.isDevelopment() ||
      process.env.REACT_APP_USE_MOCK_DATA === 'true'
    );
  },

  /**
   * Get environment-specific configuration
   */
  getEnvironmentConfig: () => {
    const env = process.env.NODE_ENV || 'development';
    
    return {
      useMockData: DataSourceUtils.shouldUseMockData(),
      enableCaching: env !== 'test',
      enablePersistentCache: env === 'production',
      cachePrefix: `mega_ai_admin_${env}_`,
      storagePrefix: `mega_ai_admin_${env}_`,
    };
  },

  /**
   * Initialize data sources
   */
  initializeDataSources: (): {
    mockDataAvailable: boolean;
    storageAvailable: boolean;
    cacheAvailable: boolean;
  } => {
    const config = DataSourceUtils.getEnvironmentConfig();
    
    return {
      mockDataAvailable: true, // Mock data is always available
      storageAvailable: DataSourceUtils.isBrowser() && StorageUtils.checkAvailability().localStorage,
      cacheAvailable: config.enableCaching,
    };
  },

  /**
   * Cleanup data sources
   */
  cleanupDataSources: (): boolean => {
    try {
      // Cleanup cache
      globalCache.cleanup();
      
      // Clear expired storage items
      if (DataSourceUtils.isBrowser()) {
        CacheStorage.clearCache();
      }
      
      return true;
    } catch (error) {
      console.error('Data source cleanup error:', error);
      return false;
    }
  },

  /**
   * Get data source health status
   */
  getHealthStatus: (): {
    mockData: 'healthy' | 'error';
    storage: 'healthy' | 'unavailable' | 'error';
    cache: 'healthy' | 'error';
    overall: 'healthy' | 'degraded' | 'error';
  } => {
    const status = {
      mockData: 'healthy' as const,
      storage: 'healthy' as const,
      cache: 'healthy' as const,
      overall: 'healthy' as const,
    };

    try {
      // Check storage
      if (!DataSourceUtils.isBrowser()) {
        status.storage = 'unavailable';
      } else {
        const storageCheck = StorageUtils.checkAvailability();
        if (!storageCheck.localStorage) {
          status.storage = 'error';
        }
      }

      // Check cache
      try {
        globalCache.getStats();
      } catch {
        status.cache = 'error';
      }

      // Determine overall status
      if (status.storage === 'error' || status.cache === 'error') {
        status.overall = 'error';
      } else if (status.storage === 'unavailable') {
        status.overall = 'degraded';
      }

    } catch (error) {
      console.error('Health check error:', error);
      status.overall = 'error';
    }

    return status;
  },

  /**
   * Reset all data sources
   */
  resetDataSources: (): boolean => {
    try {
      // Clear cache
      globalCache.clear();
      
      // Clear storage (if available)
      if (DataSourceUtils.isBrowser()) {
        StorageUtils.clearAllStorage();
      }
      
      return true;
    } catch (error) {
      console.error('Data source reset error:', error);
      return false;
    }
  },

  /**
   * Export data sources configuration
   */
  exportConfiguration: (): Record<string, any> => {
    return {
      environment: process.env.NODE_ENV,
      config: DataSourceUtils.getEnvironmentConfig(),
      health: DataSourceUtils.getHealthStatus(),
      initialization: DataSourceUtils.initializeDataSources(),
      timestamp: new Date().toISOString(),
    };
  },
};

// ============================================================================
// DATA SOURCE CONFIGURATION
// ============================================================================

/**
 * Data source configuration
 */
export const DATA_SOURCE_CONFIG = {
  // Mock data settings
  MOCK_DATA: {
    ENABLED: DataSourceUtils.shouldUseMockData(),
    DELAY_MIN: 100,
    DELAY_MAX: 500,
    ERROR_RATE: 0.05, // 5% error rate for testing
  },

  // Storage settings
  STORAGE: {
    PREFIX: DataSourceUtils.getEnvironmentConfig().storagePrefix,
    ENABLE_COMPRESSION: false,
    ENABLE_ENCRYPTION: false,
    MAX_SIZE_WARNING: 4 * 1024 * 1024, // 4MB
  },

  // Cache settings
  CACHE: {
    PREFIX: DataSourceUtils.getEnvironmentConfig().cachePrefix,
    MEMORY_MAX_SIZE: 100,
    PERSISTENT_ENABLED: DataSourceUtils.getEnvironmentConfig().enablePersistentCache,
    AUTO_CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes
  },
} as const;

// ============================================================================
// DATA SOURCE INITIALIZATION
// ============================================================================

/**
 * Initialize data sources on module load
 */
const initializeOnLoad = (): void => {
  try {
    const status = DataSourceUtils.initializeDataSources();
    
    if (DataSourceUtils.isDevelopment()) {
      console.log('Data sources initialized:', status);
      console.log('Configuration:', DataSourceUtils.exportConfiguration());
    }

    // Setup auto-cleanup for cache
    if (status.cacheAvailable) {
      setInterval(() => {
        globalCache.cleanup();
      }, DATA_SOURCE_CONFIG.CACHE.AUTO_CLEANUP_INTERVAL);
    }

  } catch (error) {
    console.error('Data source initialization error:', error);
  }
};

// Initialize on module load
if (DataSourceUtils.isBrowser()) {
  initializeOnLoad();
}

// ============================================================================
// EXPORT ALL DATA SOURCES
// ============================================================================

/**
 * All data sources combined for easy access
 */
export const ALL_DATA_SOURCES = {
  // Mock data
  MOCK: ALL_MOCK_DATA,
  
  // Storage
  STORAGE: {
    LOCAL: LocalStorageSource,
    SESSION: SessionStorageSource,
    COOKIES: CookieSource,
    AUTH: AuthStorage,
    PREFERENCES: PreferencesStorage,
    DASHBOARD: DashboardStorage,
    CACHE_STORAGE: CacheStorage,
  },
  
  // Cache
  CACHE: {
    MEMORY: MemoryCache,
    PERSISTENT: PersistentCache,
    MANAGER: CacheManager,
    GLOBAL: globalCache,
  },
  
  // Utilities
  UTILS: {
    DATA_SOURCE: DataSourceUtils,
    STORAGE: StorageUtils,
    CACHE: CacheUtils,
    MOCK: MockDataUtils,
  },
  
  // Configuration
  CONFIG: DATA_SOURCE_CONFIG,
} as const;

// ============================================================================
// DATA SOURCE METADATA
// ============================================================================

/**
 * Data source metadata for debugging and documentation
 */
export const DATA_SOURCE_INFO = {
  version: '1.0.0',
  description: 'Data Sources for Mega AI Admin - Handles data storage, caching, and mock data',
  architecture: '3-layer MVI',
  dependencies: ['Model Layer'],
  exports: {
    sources: [
      'Mock Data Source', 'Local Storage Source', 'Cache Source'
    ],
    utilities: [
      'Storage utilities', 'Cache utilities', 'Mock data utilities'
    ]
  },
  rules: [
    'Data storage and retrieval only',
    'No business logic',
    'Can import from Model Layer for types',
    'Provides data to repositories'
  ]
} as const;
