/**
 * Analytics Repository
 * 
 * Analytics data aggregation with dashboard stats from existing statsData
 * 
 * Architecture Rules:
 * - Repository belongs in Data Layer
 * - Contains business logic for analytics operations
 * - Uses types from Model Layer
 * - Uses mock data sources for data access
 * - Prepares for React Query integration
 */

import type {
  DashboardStat,
  QuickStat,
  AnalyticsData,
  AnalyticsSummary,
  UserGrowthData,
  CreditUsageData,
  ContentCreationData,
  SystemPerformanceData,
  AnalyticsQuery,
  MetricComparison,
  TopPerformers,
  AnalyticsExportConfig,
  ApiResponse,
} from '@/models';
import { BaseRepository, RepositoryError } from './baseRepository';
import {
  MOCK_DASHBOARD_STATS,
  MOCK_QUICK_STATS,
  MOCK_ANALYTICS_DATA,
  MOCK_USER_GROWTH_DATA,
  MOCK_CREDIT_USAGE_DATA,
  MOCK_CONTENT_CREATION_DATA,
  MOCK_USERS,
  MockDataUtils,
} from '@/data/sources';
import { CacheUtils } from '@/data/sources';

// ============================================================================
// ANALYTICS REPOSITORY CLASS
// ============================================================================

/**
 * Analytics repository for managing analytics data operations
 */
export class AnalyticsRepository extends BaseRepository {
  constructor() {
    super('analytics', {
      cacheTTL: 1 * 60 * 1000, // 1 minute (analytics data changes frequently)
      cachePrefix: 'analytics_repo_',
    });
  }

  // ============================================================================
  // DASHBOARD ANALYTICS
  // ============================================================================

  /**
   * Get dashboard statistics - EXACT match from Dashboard.tsx
   */
  async getDashboardStats(): Promise<ApiResponse<DashboardStat[]>> {
    try {
      const cacheKey = 'dashboard_stats';
      
      // Try cache first
      const cached = await this.getFromCache<DashboardStat[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Dashboard stats retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        // Return exact mock data from Dashboard.tsx
        return [...MOCK_DASHBOARD_STATS];
      });

      // Cache the result with short TTL
      await this.setCache(cacheKey, result, {
        ttl: 30 * 1000, // 30 seconds
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Dashboard stats retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getDashboardStats');
    }
  }

  /**
   * Get quick statistics - EXACT match from Dashboard.tsx
   */
  async getQuickStats(): Promise<ApiResponse<QuickStat[]>> {
    try {
      const cacheKey = 'quick_stats';
      
      // Try cache first
      const cached = await this.getFromCache<QuickStat[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Quick stats retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        // Return exact mock data from Dashboard.tsx
        return [...MOCK_QUICK_STATS];
      });

      // Cache the result with short TTL
      await this.setCache(cacheKey, result, {
        ttl: 30 * 1000, // 30 seconds
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Quick stats retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getQuickStats');
    }
  }

  /**
   * Get complete analytics summary
   */
  async getAnalyticsSummary(): Promise<ApiResponse<AnalyticsSummary>> {
    try {
      const cacheKey = 'analytics_summary';
      
      // Try cache first
      const cached = await this.getFromCache<AnalyticsSummary>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Analytics summary retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const summary: AnalyticsSummary = {
          dashboardStats: [...MOCK_DASHBOARD_STATS],
          quickStats: [...MOCK_QUICK_STATS],
          userGrowth: [...MOCK_USER_GROWTH_DATA],
          creditUsage: [...MOCK_CREDIT_USAGE_DATA],
          contentCreation: [...MOCK_CONTENT_CREATION_DATA],
          systemPerformance: this.generateSystemPerformanceData(),
          lastUpdated: new Date().toISOString(),
        };

        return summary;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        ttl: 1 * 60 * 1000, // 1 minute
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Analytics summary retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getAnalyticsSummary');
    }
  }

  // ============================================================================
  // GROWTH ANALYTICS
  // ============================================================================

  /**
   * Get user growth data
   */
  async getUserGrowthData(query?: AnalyticsQuery): Promise<ApiResponse<UserGrowthData[]>> {
    try {
      const cacheKey = CacheUtils.createQueryKey('user_growth', query || {});
      
      // Try cache first
      const cached = await this.getFromCache<UserGrowthData[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'User growth data retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        let data = [...MOCK_USER_GROWTH_DATA];
        
        // Apply date filtering if specified
        if (query?.startDate || query?.endDate) {
          data = this.filterDataByDateRange(data, query.startDate, query.endDate);
        }

        return data;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'User growth data retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUserGrowthData');
    }
  }

  /**
   * Get credit usage data
   */
  async getCreditUsageData(query?: AnalyticsQuery): Promise<ApiResponse<CreditUsageData[]>> {
    try {
      const cacheKey = CacheUtils.createQueryKey('credit_usage', query || {});
      
      // Try cache first
      const cached = await this.getFromCache<CreditUsageData[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Credit usage data retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        let data = [...MOCK_CREDIT_USAGE_DATA];
        
        // Apply date filtering if specified
        if (query?.startDate || query?.endDate) {
          data = this.filterDataByDateRange(data, query.startDate, query.endDate);
        }

        return data;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Credit usage data retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getCreditUsageData');
    }
  }

  /**
   * Get content creation data
   */
  async getContentCreationData(query?: AnalyticsQuery): Promise<ApiResponse<ContentCreationData[]>> {
    try {
      const cacheKey = CacheUtils.createQueryKey('content_creation', query || {});
      
      // Try cache first
      const cached = await this.getFromCache<ContentCreationData[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Content creation data retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        let data = [...MOCK_CONTENT_CREATION_DATA];
        
        // Apply date filtering if specified
        if (query?.startDate || query?.endDate) {
          data = this.filterDataByDateRange(data, query.startDate, query.endDate);
        }

        return data;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Content creation data retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getContentCreationData');
    }
  }

  // ============================================================================
  // PERFORMANCE ANALYTICS
  // ============================================================================

  /**
   * Get system performance data
   */
  async getSystemPerformanceData(query?: AnalyticsQuery): Promise<ApiResponse<SystemPerformanceData[]>> {
    try {
      const cacheKey = CacheUtils.createQueryKey('system_performance', query || {});
      
      // Try cache first
      const cached = await this.getFromCache<SystemPerformanceData[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'System performance data retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        return this.generateSystemPerformanceData();
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'System performance data retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getSystemPerformanceData');
    }
  }

  // ============================================================================
  // COMPARISON AND TOP PERFORMERS
  // ============================================================================

  /**
   * Get metric comparisons
   */
  async getMetricComparisons(metrics: string[]): Promise<ApiResponse<MetricComparison[]>> {
    try {
      const cacheKey = `metric_comparisons_${metrics.join('_')}`;
      
      // Try cache first
      const cached = await this.getFromCache<MetricComparison[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Metric comparisons retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        return this.generateMetricComparisons(metrics);
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Metric comparisons retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getMetricComparisons');
    }
  }

  /**
   * Get top performers
   */
  async getTopPerformers(): Promise<ApiResponse<TopPerformers>> {
    try {
      const cacheKey = 'top_performers';
      
      // Try cache first
      const cached = await this.getFromCache<TopPerformers>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Top performers retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        // Sort users by different metrics
        const topCreditUsers = [...MOCK_USERS]
          .sort((a, b) => b.totalUsed - a.totalUsed)
          .slice(0, 5)
          .map(user => ({
            userId: user.id,
            name: user.name,
            creditsUsed: user.totalUsed,
          }));

        const topContentCreators = [...MOCK_USERS]
          .sort((a, b) => (b.videosCreated + b.photosCreated) - (a.videosCreated + a.photosCreated))
          .slice(0, 5)
          .map(user => ({
            userId: user.id,
            name: user.name,
            contentCount: user.videosCreated + user.photosCreated,
          }));

        const mostActiveUsers = [...MOCK_USERS]
          .sort((a, b) => new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime())
          .slice(0, 5)
          .map(user => ({
            userId: user.id,
            name: user.name,
            activityScore: this.calculateActivityScore(user),
          }));

        const topPerformers: TopPerformers = {
          topCreditUsers,
          topContentCreators,
          mostActiveUsers,
        };

        return topPerformers;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Top performers retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getTopPerformers');
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Filter data by date range
   */
  private filterDataByDateRange<T extends { date: string }>(
    data: T[],
    startDate?: string,
    endDate?: string
  ): T[] {
    let filtered = [...data];

    if (startDate) {
      filtered = filtered.filter(item => item.date >= startDate);
    }

    if (endDate) {
      filtered = filtered.filter(item => item.date <= endDate);
    }

    return filtered;
  }

  /**
   * Generate system performance data
   */
  private generateSystemPerformanceData(): SystemPerformanceData[] {
    const data: SystemPerformanceData[] = [];
    const now = new Date();

    for (let i = 9; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      data.push({
        date: date.toISOString().split('T')[0],
        avgResponseTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
        errorRate: Math.random() * 2, // 0-2%
        uptime: 99 + Math.random(), // 99-100%
        apiCalls: Math.floor(Math.random() * 5000) + 10000, // 10k-15k calls
      });
    }

    return data;
  }

  /**
   * Generate metric comparisons
   */
  private generateMetricComparisons(metrics: string[]): MetricComparison[] {
    return metrics.map(metric => {
      const current = Math.floor(Math.random() * 1000) + 500;
      const previous = Math.floor(Math.random() * 1000) + 500;
      const change = current - previous;
      const changePercent = (change / previous) * 100;

      return {
        metric,
        current,
        previous,
        change,
        changePercent: Math.round(changePercent * 100) / 100,
        changeType: change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral',
      };
    });
  }

  /**
   * Calculate activity score for user
   */
  private calculateActivityScore(user: any): number {
    const daysSinceLastActivity = Math.floor(
      (Date.now() - new Date(user.lastActivity).getTime()) / (1000 * 60 * 60 * 24)
    );
    
    const contentScore = (user.videosCreated + user.photosCreated) * 10;
    const creditScore = user.totalUsed / 10;
    const activityScore = Math.max(0, 100 - daysSinceLastActivity * 5);
    
    return Math.round(contentScore + creditScore + activityScore);
  }

  /**
   * Get real-time analytics data
   */
  async getRealTimeData(): Promise<ApiResponse<AnalyticsData>> {
    try {
      const cacheKey = 'real_time_analytics';
      
      // Very short cache for real-time data
      const cached = await this.getFromCache<AnalyticsData>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Real-time analytics retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        // Add some randomness to simulate real-time changes
        const baseData = { ...MOCK_ANALYTICS_DATA };
        
        // Simulate small changes
        baseData.newUsersToday += Math.floor(Math.random() * 3);
        baseData.creditsUsedToday += Math.floor(Math.random() * 50);
        baseData.videosToday += Math.floor(Math.random() * 10);
        baseData.photosToday += Math.floor(Math.random() * 20);

        return baseData;
      });

      // Cache with very short TTL
      await this.setCache(cacheKey, result, {
        ttl: 10 * 1000, // 10 seconds
        tags: CacheUtils.createTags('analytics'),
      });

      return this.formatSuccessResponse(result, 'Real-time analytics retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getRealTimeData');
    }
  }
}

// ============================================================================
// EXPORT ANALYTICS REPOSITORY
// ============================================================================

/**
 * Analytics repository instance
 */
export const analyticsRepository = new AnalyticsRepository();
