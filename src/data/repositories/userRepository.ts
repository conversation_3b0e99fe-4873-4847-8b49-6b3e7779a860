/**
 * User Repository
 * 
 * User CRUD operations with mock data matching existing users array from UserCreditManagement.tsx
 * 
 * Architecture Rules:
 * - Repository belongs in Data Layer
 * - Contains business logic for user operations
 * - Uses types from Model Layer
 * - Uses mock data sources for data access
 * - Prepares for React Query integration
 */

import type {
  User,
  UserProfile,
  UserCreditSummary,
  UserContentSummary,
  CreateUserPayload,
  UpdateUserPayload,
  UserSearchParams,
  UserListResponse,
  ApiResponse,
  PaginatedResponse,
  OperationResult,
} from '@/models';
import { USER_LIMITS } from '@/models';
import { BaseRepository, RepositoryError } from './baseRepository';
import { MOCK_USERS, MockDataUtils } from '@/data/sources';
import { CacheUtils } from '@/data/sources';

// ============================================================================
// USER REPOSITORY CLASS
// ============================================================================

/**
 * User repository for managing user data operations
 */
export class UserRepository extends BaseRepository {
  constructor() {
    super('user', {
      cacheTTL: 5 * 60 * 1000, // 5 minutes
      cachePrefix: 'user_repo_',
    });
  }

  // ============================================================================
  // USER CRUD OPERATIONS
  // ============================================================================

  /**
   * Get all users with optional filtering and pagination
   */
  async getUsers(
    searchParams?: UserSearchParams,
    options?: { skipCache?: boolean }
  ): Promise<ApiResponse<UserListResponse>> {
    try {
      const cacheKey = CacheUtils.createQueryKey('users_list', searchParams || {});
      
      // Try cache first
      if (!options?.skipCache) {
        const cached = await this.getFromCache<UserListResponse>(cacheKey);
        if (cached) {
          return this.formatSuccessResponse(cached, 'Users retrieved from cache');
        }
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        let users = [...MOCK_USERS];
        
        // Apply search filters
        if (searchParams) {
          users = this.applyUserFilters(users, searchParams);
        }

        // Calculate pagination
        const pagination = this.validatePagination({
          page: searchParams?.page || 1,
          limit: searchParams?.limit || 10,
        });

        const total = users.length;
        const paginatedUsers = this.applyPagination(users, pagination);

        const response: UserListResponse = {
          users: paginatedUsers,
          total,
          page: pagination.page,
          limit: pagination.limit,
          totalPages: Math.ceil(total / pagination.limit),
        };

        return response;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('user'),
      });

      return this.formatSuccessResponse(result, 'Users retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUsers');
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: number, options?: { skipCache?: boolean }): Promise<ApiResponse<User>> {
    try {
      const cacheKey = `user_${id}`;
      
      // Try cache first
      if (!options?.skipCache) {
        const cached = await this.getFromCache<User>(cacheKey);
        if (cached) {
          return this.formatSuccessResponse(cached, 'User retrieved from cache');
        }
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(id);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${id} not found`,
            'USER_NOT_FOUND'
          );
        }

        return user;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('user', id),
      });

      return this.formatSuccessResponse(result, 'User retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUserById');
    }
  }

  /**
   * Create new user
   */
  async createUser(payload: CreateUserPayload): Promise<ApiResponse<User>> {
    try {
      // Validate payload
      this.validateCreateUserPayload(payload);

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        // Generate new user ID
        const maxId = Math.max(...MOCK_USERS.map(u => u.id));
        const newId = maxId + 1;

        // Create new user object
        const newUser: User = {
          id: newId,
          name: payload.name,
          email: payload.email,
          phone: payload.phone,
          credits: payload.initialCredits || USER_LIMITS.DEFAULT_NEW_USER_CREDITS,
          videosCreated: 0,
          photosCreated: 0,
          totalUsed: 0,
          totalAdded: payload.initialCredits || USER_LIMITS.DEFAULT_NEW_USER_CREDITS,
          monthlyStats: {
            videosCompleted: 0,
            videosErrors: 0,
            photosCompleted: 0,
            photosErrors: 0,
            creditsUsed: 0,
            creditsAdded: payload.initialCredits || USER_LIMITS.DEFAULT_NEW_USER_CREDITS,
          },
          lastActivity: new Date().toISOString(),
          joinDate: new Date().toISOString(),
          status: payload.status || 'active',
        };

        // Add to mock data (in real app, this would be API call)
        MOCK_USERS.push(newUser);

        return newUser;
      });

      // Invalidate users list cache
      await this.invalidateCacheByTags(['user']);

      return this.formatSuccessResponse(result, 'User created successfully');
    } catch (error) {
      throw this.handleError(error, 'createUser');
    }
  }

  /**
   * Update user
   */
  async updateUser(id: number, payload: UpdateUserPayload): Promise<ApiResponse<User>> {
    try {
      // Validate payload
      this.validateUpdateUserPayload(payload);

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const userIndex = MOCK_USERS.findIndex(u => u.id === id);
        
        if (userIndex === -1) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${id} not found`,
            'USER_NOT_FOUND'
          );
        }

        // Update user object
        const updatedUser: User = {
          ...MOCK_USERS[userIndex],
          ...payload,
          lastActivity: new Date().toISOString(),
        };

        // Update in mock data
        MOCK_USERS[userIndex] = updatedUser;

        return updatedUser;
      });

      // Invalidate cache
      await this.invalidateCache(`user_${id}`);
      await this.invalidateCacheByTags(['user']);

      return this.formatSuccessResponse(result, 'User updated successfully');
    } catch (error) {
      throw this.handleError(error, 'updateUser');
    }
  }

  /**
   * Delete user
   */
  async deleteUser(id: number): Promise<ApiResponse<{ id: number }>> {
    try {
      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const userIndex = MOCK_USERS.findIndex(u => u.id === id);
        
        if (userIndex === -1) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${id} not found`,
            'USER_NOT_FOUND'
          );
        }

        // Remove from mock data
        MOCK_USERS.splice(userIndex, 1);

        return { id };
      });

      // Invalidate cache
      await this.invalidateCache(`user_${id}`);
      await this.invalidateCacheByTags(['user']);

      return this.formatSuccessResponse(result, 'User deleted successfully');
    } catch (error) {
      throw this.handleError(error, 'deleteUser');
    }
  }

  // ============================================================================
  // USER STATISTICS AND ANALYTICS
  // ============================================================================

  /**
   * Get user profile summary
   */
  async getUserProfile(id: number): Promise<ApiResponse<UserProfile>> {
    try {
      const cacheKey = `user_profile_${id}`;
      
      // Try cache first
      const cached = await this.getFromCache<UserProfile>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'User profile retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(id);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${id} not found`,
            'USER_NOT_FOUND'
          );
        }

        const profile: UserProfile = {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          joinDate: user.joinDate,
          lastActivity: user.lastActivity,
          status: user.status,
        };

        return profile;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('user', id),
      });

      return this.formatSuccessResponse(result, 'User profile retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUserProfile');
    }
  }

  /**
   * Get user credit summary
   */
  async getUserCreditSummary(id: number): Promise<ApiResponse<UserCreditSummary>> {
    try {
      const cacheKey = `user_credit_summary_${id}`;
      
      // Try cache first
      const cached = await this.getFromCache<UserCreditSummary>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'User credit summary retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(id);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${id} not found`,
            'USER_NOT_FOUND'
          );
        }

        const summary: UserCreditSummary = {
          id: user.id,
          name: user.name,
          credits: user.credits,
          totalUsed: user.totalUsed,
          totalAdded: user.totalAdded,
          monthlyCreditsUsed: user.monthlyStats.creditsUsed,
          monthlyCreditsAdded: user.monthlyStats.creditsAdded,
        };

        return summary;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('user', id),
      });

      return this.formatSuccessResponse(result, 'User credit summary retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUserCreditSummary');
    }
  }

  /**
   * Get user content summary
   */
  async getUserContentSummary(id: number): Promise<ApiResponse<UserContentSummary>> {
    try {
      const cacheKey = `user_content_summary_${id}`;
      
      // Try cache first
      const cached = await this.getFromCache<UserContentSummary>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'User content summary retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(id);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${id} not found`,
            'USER_NOT_FOUND'
          );
        }

        // Calculate success rates
        const videoTotal = user.monthlyStats.videosCompleted + user.monthlyStats.videosErrors;
        const photoTotal = user.monthlyStats.photosCompleted + user.monthlyStats.photosErrors;
        
        const videoSuccessRate = videoTotal > 0 ? (user.monthlyStats.videosCompleted / videoTotal) * 100 : 0;
        const photoSuccessRate = photoTotal > 0 ? (user.monthlyStats.photosCompleted / photoTotal) * 100 : 0;

        const summary: UserContentSummary = {
          id: user.id,
          name: user.name,
          videosCreated: user.videosCreated,
          photosCreated: user.photosCreated,
          monthlyVideosCompleted: user.monthlyStats.videosCompleted,
          monthlyVideosErrors: user.monthlyStats.videosErrors,
          monthlyPhotosCompleted: user.monthlyStats.photosCompleted,
          monthlyPhotosErrors: user.monthlyStats.photosErrors,
          videoSuccessRate: Math.round(videoSuccessRate * 100) / 100,
          photoSuccessRate: Math.round(photoSuccessRate * 100) / 100,
        };

        return summary;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('user', id),
      });

      return this.formatSuccessResponse(result, 'User content summary retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUserContentSummary');
    }
  }

  // ============================================================================
  // BULK OPERATIONS
  // ============================================================================

  /**
   * Get users by status
   */
  async getUsersByStatus(status: 'active' | 'premium' | 'inactive'): Promise<ApiResponse<User[]>> {
    try {
      const cacheKey = `users_by_status_${status}`;
      
      // Try cache first
      const cached = await this.getFromCache<User[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Users by status retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        return MockDataUtils.getUsersByStatus(status);
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('user'),
      });

      return this.formatSuccessResponse(result, 'Users by status retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUsersByStatus');
    }
  }

  // ============================================================================
  // VALIDATION METHODS
  // ============================================================================

  /**
   * Validate create user payload
   */
  private validateCreateUserPayload(payload: CreateUserPayload): void {
    if (!payload.name || payload.name.trim().length === 0) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'User name is required',
        'INVALID_NAME'
      );
    }

    if (!payload.email || !this.isValidEmail(payload.email)) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Valid email is required',
        'INVALID_EMAIL'
      );
    }

    if (!payload.phone || !this.isValidPhone(payload.phone)) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Valid phone number is required',
        'INVALID_PHONE'
      );
    }

    // Check if email already exists
    const existingUser = MOCK_USERS.find(u => u.email === payload.email);
    if (existingUser) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Email already exists',
        'EMAIL_EXISTS'
      );
    }
  }

  /**
   * Validate update user payload
   */
  private validateUpdateUserPayload(payload: UpdateUserPayload): void {
    if (payload.name !== undefined && payload.name.trim().length === 0) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'User name cannot be empty',
        'INVALID_NAME'
      );
    }

    if (payload.email !== undefined && !this.isValidEmail(payload.email)) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Valid email is required',
        'INVALID_EMAIL'
      );
    }

    if (payload.phone !== undefined && !this.isValidPhone(payload.phone)) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Valid phone number is required',
        'INVALID_PHONE'
      );
    }
  }

  /**
   * Apply user-specific filters
   */
  private applyUserFilters(users: User[], searchParams: UserSearchParams): User[] {
    let filteredUsers = [...users];

    // Apply text search
    if (searchParams.searchTerm) {
      const query = searchParams.searchTerm.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.name.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.phone.includes(query)
      );
    }

    // Apply status filter
    if (searchParams.status && searchParams.status !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.status === searchParams.status);
    }

    // Apply sorting
    if (searchParams.sortBy) {
      filteredUsers.sort((a, b) => {
        const aValue = (a as any)[searchParams.sortBy!];
        const bValue = (b as any)[searchParams.sortBy!];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
        
        return searchParams.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return filteredUsers;
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^(\+84|0)[0-9]{9,10}$/;
    return phoneRegex.test(phone);
  }
}

// ============================================================================
// EXPORT USER REPOSITORY
// ============================================================================

/**
 * User repository instance
 */
export const userRepository = new UserRepository();
