/**
 * Base Repository
 * 
 * Shared repository logic including error handling, response formatting, and mock data integration
 * 
 * Architecture Rules:
 * - Repositories belong in Data Layer
 * - Contains business logic for data operations
 * - Uses types from Model Layer
 * - Uses data sources for actual data access
 * - Prepares for React Query integration
 */

import type {
  ApiResponse,
  PaginatedResponse,
  PaginationParams,
  SearchParams,
  LoadingState,
  ErrorState,
  OperationResult,
} from '@/models';
import { MockDataUtils, globalCache, CacheUtils } from '@/data/sources';
import { DATA_LAYER_CONFIG } from '@/data/constants';

// ============================================================================
// BASE REPOSITORY TYPES
// ============================================================================

/**
 * Repository configuration
 */
export interface RepositoryConfig {
  /** Enable caching for this repository */
  enableCache: boolean;
  /** Default cache TTL in milliseconds */
  cacheTTL: number;
  /** Cache key prefix */
  cachePrefix: string;
  /** Enable mock data */
  useMockData: boolean;
  /** Mock API delay range */
  mockDelay: { min: number; max: number };
  /** Mock error rate (0-1) */
  mockErrorRate: number;
}

/**
 * Repository operation options
 */
export interface RepositoryOptions {
  /** Skip cache for this operation */
  skipCache?: boolean;
  /** Custom cache TTL */
  cacheTTL?: number;
  /** Cache tags for invalidation */
  cacheTags?: string[];
  /** Force refresh from source */
  forceRefresh?: boolean;
  /** Custom timeout */
  timeout?: number;
}

/**
 * Repository error types
 */
export type RepositoryErrorType = 
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'VALIDATION_ERROR'
  | 'NOT_FOUND_ERROR'
  | 'PERMISSION_ERROR'
  | 'SERVER_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Repository error class
 */
export class RepositoryError extends Error {
  public readonly type: RepositoryErrorType;
  public readonly code: string;
  public readonly details?: any;
  public readonly timestamp: string;

  constructor(
    type: RepositoryErrorType,
    message: string,
    code: string = 'UNKNOWN',
    details?: any
  ) {
    super(message);
    this.name = 'RepositoryError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = new Date().toISOString();
  }
}

// ============================================================================
// BASE REPOSITORY CLASS
// ============================================================================

/**
 * Base repository class with shared functionality
 */
export abstract class BaseRepository {
  protected config: RepositoryConfig;
  protected entityName: string;

  constructor(entityName: string, config?: Partial<RepositoryConfig>) {
    this.entityName = entityName;
    this.config = {
      enableCache: true,
      cacheTTL: DATA_LAYER_CONFIG.CACHE.DEFAULT_TTL,
      cachePrefix: `repo_${entityName}_`,
      useMockData: process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK_DATA === 'true',
      mockDelay: { min: 100, max: 500 },
      mockErrorRate: 0.02, // 2% error rate
      ...config,
    };
  }

  // ============================================================================
  // CACHE OPERATIONS
  // ============================================================================

  /**
   * Get data from cache
   */
  protected async getFromCache<T>(key: string): Promise<T | null> {
    if (!this.config.enableCache) return null;

    try {
      const cacheKey = this.getCacheKey(key);
      return globalCache.get<T>(cacheKey);
    } catch (error) {
      console.warn(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * Set data in cache
   */
  protected async setCache<T>(
    key: string,
    data: T,
    options?: { ttl?: number; tags?: string[] }
  ): Promise<boolean> {
    if (!this.config.enableCache) return false;

    try {
      const cacheKey = this.getCacheKey(key);
      const ttl = options?.ttl || this.config.cacheTTL;
      const tags = options?.tags || CacheUtils.createTags(this.entityName);

      return globalCache.set(cacheKey, data, {
        memoryTTL: ttl,
        persistentTTL: ttl * 2,
        tags,
      });
    } catch (error) {
      console.warn(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Invalidate cache by key
   */
  protected async invalidateCache(key: string): Promise<boolean> {
    if (!this.config.enableCache) return false;

    try {
      const cacheKey = this.getCacheKey(key);
      return globalCache.delete(cacheKey);
    } catch (error) {
      console.warn(`Cache invalidation error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * Invalidate cache by tags
   */
  protected async invalidateCacheByTags(tags: string[]): Promise<number> {
    if (!this.config.enableCache) return 0;

    try {
      return globalCache.clearByTags(tags);
    } catch (error) {
      console.warn(`Cache tag invalidation error:`, error);
      return 0;
    }
  }

  /**
   * Get cache key with prefix
   */
  protected getCacheKey(key: string): string {
    return `${this.config.cachePrefix}${key}`;
  }

  // ============================================================================
  // MOCK DATA OPERATIONS
  // ============================================================================

  /**
   * Simulate API call with mock data
   */
  protected async simulateApiCall<T>(
    operation: () => T | Promise<T>,
    options?: RepositoryOptions
  ): Promise<T> {
    // Simulate network delay
    const delay = this.getRandomDelay();
    await new Promise(resolve => setTimeout(resolve, delay));

    // Simulate random errors
    if (Math.random() < this.config.mockErrorRate) {
      throw new RepositoryError(
        'NETWORK_ERROR',
        'Simulated network error',
        'MOCK_ERROR',
        { delay, timestamp: new Date().toISOString() }
      );
    }

    // Execute operation
    const result = await operation();
    return result;
  }

  /**
   * Get random delay for mock API calls
   */
  protected getRandomDelay(): number {
    const { min, max } = this.config.mockDelay;
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }

  // ============================================================================
  // RESPONSE FORMATTING
  // ============================================================================

  /**
   * Format successful API response
   */
  protected formatSuccessResponse<T>(data: T, message?: string): ApiResponse<T> {
    return {
      data,
      success: true,
      message: message || 'Operation completed successfully',
      meta: {
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId(),
        version: '1.0.0',
      },
    };
  }

  /**
   * Format error API response
   */
  protected formatErrorResponse(error: Error | RepositoryError): ApiResponse<null> {
    const repositoryError = error instanceof RepositoryError ? error : new RepositoryError(
      'UNKNOWN_ERROR',
      error.message,
      'UNKNOWN',
      { originalError: error }
    );

    return {
      data: null,
      success: false,
      message: repositoryError.message,
      error: {
        code: repositoryError.code,
        message: repositoryError.message,
        details: repositoryError.details,
      },
      meta: {
        timestamp: repositoryError.timestamp,
        requestId: this.generateRequestId(),
        version: '1.0.0',
      },
    };
  }

  /**
   * Format paginated response
   */
  protected formatPaginatedResponse<T>(
    items: T[],
    pagination: PaginationParams,
    total: number
  ): PaginatedResponse<T> {
    const totalPages = Math.ceil(total / pagination.limit);
    
    return {
      items,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasPrevious: pagination.page > 1,
        hasNext: pagination.page < totalPages,
        previousPage: pagination.page > 1 ? pagination.page - 1 : undefined,
        nextPage: pagination.page < totalPages ? pagination.page + 1 : undefined,
      },
    };
  }

  /**
   * Format operation result
   */
  protected formatOperationResult<T>(
    success: boolean,
    data?: T,
    error?: string,
    message?: string
  ): OperationResult<T> {
    return {
      success,
      data,
      error,
      message: message || (success ? 'Operation completed successfully' : 'Operation failed'),
    };
  }

  // ============================================================================
  // ERROR HANDLING
  // ============================================================================

  /**
   * Handle repository errors
   */
  protected handleError(error: unknown, operation: string): RepositoryError {
    console.error(`Repository error in ${this.entityName}.${operation}:`, error);

    if (error instanceof RepositoryError) {
      return error;
    }

    if (error instanceof Error) {
      // Determine error type based on error message
      let type: RepositoryErrorType = 'UNKNOWN_ERROR';
      let code = 'UNKNOWN';

      if (error.message.includes('network') || error.message.includes('fetch')) {
        type = 'NETWORK_ERROR';
        code = 'NETWORK_FAILED';
      } else if (error.message.includes('timeout')) {
        type = 'TIMEOUT_ERROR';
        code = 'REQUEST_TIMEOUT';
      } else if (error.message.includes('validation')) {
        type = 'VALIDATION_ERROR';
        code = 'VALIDATION_FAILED';
      } else if (error.message.includes('not found')) {
        type = 'NOT_FOUND_ERROR';
        code = 'RESOURCE_NOT_FOUND';
      } else if (error.message.includes('permission') || error.message.includes('unauthorized')) {
        type = 'PERMISSION_ERROR';
        code = 'ACCESS_DENIED';
      }

      return new RepositoryError(type, error.message, code, { originalError: error });
    }

    return new RepositoryError(
      'UNKNOWN_ERROR',
      'An unknown error occurred',
      'UNKNOWN',
      { originalError: error }
    );
  }

  /**
   * Retry operation with exponential backoff
   */
  protected async retryOperation<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw this.handleError(lastError!, 'retryOperation');
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Generate unique request ID
   */
  protected generateRequestId(): string {
    return `${this.entityName}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Validate pagination parameters
   */
  protected validatePagination(params: Partial<PaginationParams>): PaginationParams {
    return {
      page: Math.max(1, params.page || 1),
      limit: Math.min(100, Math.max(1, params.limit || 10)),
      total: params.total || 0,
      totalPages: params.totalPages || 0,
    };
  }

  /**
   * Apply search filters to data
   */
  protected applySearchFilters<T>(
    data: T[],
    searchParams: SearchParams,
    searchFields: (keyof T)[]
  ): T[] {
    let filteredData = [...data];

    // Apply text search
    if (searchParams.query && searchFields.length > 0) {
      const query = searchParams.query.toLowerCase();
      filteredData = filteredData.filter(item =>
        searchFields.some(field => {
          const value = item[field];
          return value && String(value).toLowerCase().includes(query);
        })
      );
    }

    // Apply sorting
    if (searchParams.sort && searchParams.sort.length > 0) {
      const sortConfig = searchParams.sort[0];
      filteredData.sort((a, b) => {
        const aValue = (a as any)[sortConfig.field];
        const bValue = (b as any)[sortConfig.field];
        
        if (aValue < bValue) return sortConfig.direction === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortConfig.direction === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filteredData;
  }

  /**
   * Apply pagination to data
   */
  protected applyPagination<T>(data: T[], pagination: PaginationParams): T[] {
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    return data.slice(startIndex, endIndex);
  }

  /**
   * Get repository configuration
   */
  public getConfig(): RepositoryConfig {
    return { ...this.config };
  }

  /**
   * Update repository configuration
   */
  public updateConfig(config: Partial<RepositoryConfig>): void {
    this.config = { ...this.config, ...config };
  }
}
