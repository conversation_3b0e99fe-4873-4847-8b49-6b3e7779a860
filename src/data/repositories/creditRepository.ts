/**
 * Credit Repository
 * 
 * Credit management with transaction history, balance calculations
 * 
 * Architecture Rules:
 * - Repository belongs in Data Layer
 * - Contains business logic for credit operations
 * - Uses types from Model Layer
 * - Uses mock data sources for data access
 * - Prepares for React Query integration
 */

import type {
  CreditTransaction,
  CreditBalance,
  CreditUsageStats,
  CreditOperationPayload,
  CreditTransactionQuery,
  CreditTransactionHistoryResponse,
  CreditPackage,
  ApiResponse,
  OperationResult,
} from '@/models';
import { CREDIT_TRANSACTION_TYPE, CREDIT_LIMITS, DEFAULT_CREDIT_COSTS } from '@/models';
import { BaseRepository, RepositoryError } from './baseRepository';
import { MOCK_CREDIT_TRANSACTIONS, MOCK_USERS, MockDataUtils } from '@/data/sources';
import { CacheUtils } from '@/data/sources';

// ============================================================================
// CREDIT REPOSITORY CLASS
// ============================================================================

/**
 * Credit repository for managing credit operations
 */
export class CreditRepository extends BaseRepository {
  constructor() {
    super('credit', {
      cacheTTL: 2 * 60 * 1000, // 2 minutes (credits change frequently)
      cachePrefix: 'credit_repo_',
    });
  }

  // ============================================================================
  // CREDIT BALANCE OPERATIONS
  // ============================================================================

  /**
   * Get user credit balance
   */
  async getCreditBalance(userId: number): Promise<ApiResponse<CreditBalance>> {
    try {
      const cacheKey = `balance_${userId}`;
      
      // Try cache first
      const cached = await this.getFromCache<CreditBalance>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Credit balance retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(userId);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${userId} not found`,
            'USER_NOT_FOUND'
          );
        }

        const balance: CreditBalance = {
          userId: user.id,
          current: user.credits,
          totalAdded: user.totalAdded,
          totalUsed: user.totalUsed,
          monthlyUsed: user.monthlyStats.creditsUsed,
          monthlyAdded: user.monthlyStats.creditsAdded,
          lastUpdated: user.lastActivity,
        };

        return balance;
      });

      // Cache the result with shorter TTL
      await this.setCache(cacheKey, result, {
        ttl: 1 * 60 * 1000, // 1 minute
        tags: CacheUtils.createTags('credit', userId),
      });

      return this.formatSuccessResponse(result, 'Credit balance retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getCreditBalance');
    }
  }

  /**
   * Add credits to user account
   */
  async addCredits(payload: CreditOperationPayload): Promise<ApiResponse<CreditTransaction>> {
    try {
      // Validate payload
      this.validateCreditOperation(payload);

      if (payload.amount <= 0) {
        throw new RepositoryError(
          'VALIDATION_ERROR',
          'Credit amount must be positive',
          'INVALID_AMOUNT'
        );
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(payload.userId);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${payload.userId} not found`,
            'USER_NOT_FOUND'
          );
        }

        // Check maximum balance limit
        if (user.credits + payload.amount > CREDIT_LIMITS.MAX_BALANCE) {
          throw new RepositoryError(
            'VALIDATION_ERROR',
            `Adding ${payload.amount} credits would exceed maximum balance limit of ${CREDIT_LIMITS.MAX_BALANCE}`,
            'BALANCE_LIMIT_EXCEEDED'
          );
        }

        // Create transaction
        const transaction: CreditTransaction = {
          id: this.generateTransactionId(),
          userId: payload.userId,
          type: payload.type,
          amount: payload.amount,
          balanceAfter: user.credits + payload.amount,
          status: 'completed',
          context: payload.context,
          description: payload.description || `Added ${payload.amount} credits`,
          createdAt: new Date().toISOString(),
          adminId: payload.adminId,
        };

        // Update user balance in mock data
        user.credits += payload.amount;
        user.totalAdded += payload.amount;
        user.monthlyStats.creditsAdded += payload.amount;
        user.lastActivity = new Date().toISOString();

        // Add transaction to mock data
        MOCK_CREDIT_TRANSACTIONS.push(transaction);

        return transaction;
      });

      // Invalidate related caches
      await this.invalidateCache(`balance_${payload.userId}`);
      await this.invalidateCacheByTags(['credit', 'user']);

      return this.formatSuccessResponse(result, 'Credits added successfully');
    } catch (error) {
      throw this.handleError(error, 'addCredits');
    }
  }

  /**
   * Use credits from user account
   */
  async useCredits(payload: CreditOperationPayload): Promise<ApiResponse<CreditTransaction>> {
    try {
      // Validate payload
      this.validateCreditOperation(payload);

      if (payload.amount <= 0) {
        throw new RepositoryError(
          'VALIDATION_ERROR',
          'Credit amount must be positive',
          'INVALID_AMOUNT'
        );
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(payload.userId);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${payload.userId} not found`,
            'USER_NOT_FOUND'
          );
        }

        // Check sufficient balance
        if (user.credits < payload.amount) {
          throw new RepositoryError(
            'VALIDATION_ERROR',
            `Insufficient credits. Available: ${user.credits}, Required: ${payload.amount}`,
            'INSUFFICIENT_CREDITS'
          );
        }

        // Create transaction
        const transaction: CreditTransaction = {
          id: this.generateTransactionId(),
          userId: payload.userId,
          type: 'used',
          amount: -payload.amount, // Negative for usage
          balanceAfter: user.credits - payload.amount,
          status: 'completed',
          context: payload.context,
          description: payload.description || `Used ${payload.amount} credits`,
          createdAt: new Date().toISOString(),
          contentId: payload.contentId,
        };

        // Update user balance in mock data
        user.credits -= payload.amount;
        user.totalUsed += payload.amount;
        user.monthlyStats.creditsUsed += payload.amount;
        user.lastActivity = new Date().toISOString();

        // Add transaction to mock data
        MOCK_CREDIT_TRANSACTIONS.push(transaction);

        return transaction;
      });

      // Invalidate related caches
      await this.invalidateCache(`balance_${payload.userId}`);
      await this.invalidateCacheByTags(['credit', 'user']);

      return this.formatSuccessResponse(result, 'Credits used successfully');
    } catch (error) {
      throw this.handleError(error, 'useCredits');
    }
  }

  /**
   * Refund credits to user account
   */
  async refundCredits(payload: CreditOperationPayload): Promise<ApiResponse<CreditTransaction>> {
    try {
      // Validate payload
      this.validateCreditOperation(payload);

      if (payload.amount <= 0) {
        throw new RepositoryError(
          'VALIDATION_ERROR',
          'Refund amount must be positive',
          'INVALID_AMOUNT'
        );
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        const user = MockDataUtils.getUserById(payload.userId);
        
        if (!user) {
          throw new RepositoryError(
            'NOT_FOUND_ERROR',
            `User with ID ${payload.userId} not found`,
            'USER_NOT_FOUND'
          );
        }

        // Create transaction
        const transaction: CreditTransaction = {
          id: this.generateTransactionId(),
          userId: payload.userId,
          type: 'refund',
          amount: payload.amount,
          balanceAfter: user.credits + payload.amount,
          status: 'completed',
          context: payload.context,
          description: payload.description || `Refunded ${payload.amount} credits`,
          createdAt: new Date().toISOString(),
          adminId: payload.adminId,
          contentId: payload.contentId,
        };

        // Update user balance in mock data
        user.credits += payload.amount;
        user.totalAdded += payload.amount;
        user.lastActivity = new Date().toISOString();

        // Add transaction to mock data
        MOCK_CREDIT_TRANSACTIONS.push(transaction);

        return transaction;
      });

      // Invalidate related caches
      await this.invalidateCache(`balance_${payload.userId}`);
      await this.invalidateCacheByTags(['credit', 'user']);

      return this.formatSuccessResponse(result, 'Credits refunded successfully');
    } catch (error) {
      throw this.handleError(error, 'refundCredits');
    }
  }

  // ============================================================================
  // TRANSACTION HISTORY
  // ============================================================================

  /**
   * Get credit transaction history
   */
  async getTransactionHistory(
    query: CreditTransactionQuery
  ): Promise<ApiResponse<CreditTransactionHistoryResponse>> {
    try {
      const cacheKey = CacheUtils.createQueryKey('transaction_history', query);
      
      // Try cache first
      const cached = await this.getFromCache<CreditTransactionHistoryResponse>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Transaction history retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        let transactions = [...MOCK_CREDIT_TRANSACTIONS];

        // Apply filters
        transactions = this.applyTransactionFilters(transactions, query);

        // Calculate pagination
        const pagination = this.validatePagination({
          page: query.page || 1,
          limit: query.limit || 10,
        });

        const total = transactions.length;
        const paginatedTransactions = this.applyPagination(transactions, pagination);

        const response: CreditTransactionHistoryResponse = {
          transactions: paginatedTransactions,
          total,
          page: pagination.page,
          limit: pagination.limit,
          totalPages: Math.ceil(total / pagination.limit),
        };

        return response;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('credit'),
      });

      return this.formatSuccessResponse(result, 'Transaction history retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getTransactionHistory');
    }
  }

  /**
   * Get user transaction history
   */
  async getUserTransactionHistory(
    userId: number,
    query?: Partial<CreditTransactionQuery>
  ): Promise<ApiResponse<CreditTransaction[]>> {
    try {
      const cacheKey = `user_transactions_${userId}_${JSON.stringify(query || {})}`;
      
      // Try cache first
      const cached = await this.getFromCache<CreditTransaction[]>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'User transaction history retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        return MockDataUtils.getCreditTransactionsByUserId(userId);
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('credit', userId),
      });

      return this.formatSuccessResponse(result, 'User transaction history retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getUserTransactionHistory');
    }
  }

  // ============================================================================
  // CREDIT STATISTICS
  // ============================================================================

  /**
   * Get credit usage statistics
   */
  async getCreditUsageStats(userId?: number): Promise<ApiResponse<CreditUsageStats>> {
    try {
      const cacheKey = userId ? `usage_stats_${userId}` : 'usage_stats_system';
      
      // Try cache first
      const cached = await this.getFromCache<CreditUsageStats>(cacheKey);
      if (cached) {
        return this.formatSuccessResponse(cached, 'Credit usage stats retrieved from cache');
      }

      // Simulate API call with mock data
      const result = await this.simulateApiCall(async () => {
        let transactions = [...MOCK_CREDIT_TRANSACTIONS];
        
        if (userId) {
          transactions = transactions.filter(t => t.userId === userId);
        }

        // Calculate usage by context
        const stats: CreditUsageStats = {
          videoCreation: this.calculateUsageByContext(transactions, 'video_creation'),
          photoCreation: this.calculateUsageByContext(transactions, 'photo_creation'),
          premiumFeatures: this.calculateUsageByContext(transactions, 'premium_feature'),
          apiCalls: this.calculateUsageByContext(transactions, 'api_call'),
          other: this.calculateUsageByContext(transactions, 'other'),
          monthlyBreakdown: this.calculateMonthlyBreakdown(transactions),
        };

        return stats;
      });

      // Cache the result
      await this.setCache(cacheKey, result, {
        tags: CacheUtils.createTags('credit', userId),
      });

      return this.formatSuccessResponse(result, 'Credit usage stats retrieved successfully');
    } catch (error) {
      throw this.handleError(error, 'getCreditUsageStats');
    }
  }

  // ============================================================================
  // VALIDATION AND UTILITY METHODS
  // ============================================================================

  /**
   * Validate credit operation payload
   */
  private validateCreditOperation(payload: CreditOperationPayload): void {
    if (!payload.userId || payload.userId <= 0) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Valid user ID is required',
        'INVALID_USER_ID'
      );
    }

    if (!payload.amount || payload.amount <= 0) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Credit amount must be positive',
        'INVALID_AMOUNT'
      );
    }

    if (!payload.type || !Object.values(CREDIT_TRANSACTION_TYPE).includes(payload.type as any)) {
      throw new RepositoryError(
        'VALIDATION_ERROR',
        'Valid transaction type is required',
        'INVALID_TYPE'
      );
    }
  }

  /**
   * Apply transaction filters
   */
  private applyTransactionFilters(
    transactions: CreditTransaction[],
    query: CreditTransactionQuery
  ): CreditTransaction[] {
    let filtered = [...transactions];

    // Filter by user ID
    if (query.userId) {
      filtered = filtered.filter(t => t.userId === query.userId);
    }

    // Filter by type
    if (query.type) {
      filtered = filtered.filter(t => t.type === query.type);
    }

    // Filter by status
    if (query.status) {
      filtered = filtered.filter(t => t.status === query.status);
    }

    // Filter by context
    if (query.context) {
      filtered = filtered.filter(t => t.context === query.context);
    }

    // Filter by date range
    if (query.startDate) {
      filtered = filtered.filter(t => new Date(t.createdAt) >= new Date(query.startDate!));
    }

    if (query.endDate) {
      filtered = filtered.filter(t => new Date(t.createdAt) <= new Date(query.endDate!));
    }

    // Apply sorting
    if (query.sortBy) {
      filtered.sort((a, b) => {
        const aValue = (a as any)[query.sortBy!];
        const bValue = (b as any)[query.sortBy!];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        if (aValue > bValue) comparison = 1;
        
        return query.sortOrder === 'desc' ? -comparison : comparison;
      });
    }

    return filtered;
  }

  /**
   * Calculate usage by context
   */
  private calculateUsageByContext(
    transactions: CreditTransaction[],
    context: string
  ): number {
    return transactions
      .filter(t => t.context === context && t.type === 'used')
      .reduce((total, t) => total + Math.abs(t.amount), 0);
  }

  /**
   * Calculate monthly breakdown
   */
  private calculateMonthlyBreakdown(transactions: CreditTransaction[]): any[] {
    const monthlyData: Record<string, any> = {};

    transactions.forEach(transaction => {
      if (transaction.type !== 'used') return;

      const month = transaction.createdAt.substring(0, 7); // YYYY-MM
      
      if (!monthlyData[month]) {
        monthlyData[month] = {
          month,
          videoCreation: 0,
          photoCreation: 0,
          premiumFeatures: 0,
          apiCalls: 0,
          other: 0,
          total: 0,
        };
      }

      const amount = Math.abs(transaction.amount);
      monthlyData[month].total += amount;

      switch (transaction.context) {
        case 'video_creation':
          monthlyData[month].videoCreation += amount;
          break;
        case 'photo_creation':
          monthlyData[month].photoCreation += amount;
          break;
        case 'premium_feature':
          monthlyData[month].premiumFeatures += amount;
          break;
        case 'api_call':
          monthlyData[month].apiCalls += amount;
          break;
        default:
          monthlyData[month].other += amount;
          break;
      }
    });

    return Object.values(monthlyData).sort((a: any, b: any) => a.month.localeCompare(b.month));
  }

  /**
   * Generate unique transaction ID
   */
  private generateTransactionId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// ============================================================================
// EXPORT CREDIT REPOSITORY
// ============================================================================

/**
 * Credit repository instance
 */
export const creditRepository = new CreditRepository();
