/**
 * Repositories - Central Export
 * 
 * This module exports all repositories used in the Data Layer.
 * Repositories contain business logic for data operations and use data sources.
 * 
 * Architecture Rules:
 * - Repositories belong in Data Layer
 * - Contain business logic for data operations
 * - Use types from Model Layer
 * - Use data sources for actual data access
 * - Prepare for React Query integration
 * - NO UI logic (belongs in Interface Layer)
 */

// ============================================================================
// BASE REPOSITORY
// ============================================================================

export {
  BaseRepository,
  RepositoryError,
} from './baseRepository';

export type {
  RepositoryConfig,
  RepositoryOptions,
  RepositoryErrorType,
} from './baseRepository';

// ============================================================================
// REPOSITORY CLASSES
// ============================================================================

export {
  UserRepository,
  userRepository,
} from './userRepository';

export {
  CreditRepository,
  creditRepository,
} from './creditRepository';

export {
  AnalyticsRepository,
  analyticsRepository,
} from './analyticsRepository';

export {
  ContentRepository,
  contentRepository,
} from './contentRepository';

export {
  NotificationRepository,
  notificationRepository,
} from './notificationRepository';

export {
  SystemRepository,
  systemRepository,
} from './systemRepository';

// ============================================================================
// REPOSITORY INSTANCES COLLECTION
// ============================================================================

/**
 * All repository instances for easy access
 */
export const repositories = {
  user: userRepository,
  credit: creditRepository,
  analytics: analyticsRepository,
  content: contentRepository,
  notification: notificationRepository,
  system: systemRepository,
} as const;

// ============================================================================
// REPOSITORY UTILITIES
// ============================================================================

/**
 * Repository utility functions
 */
export const RepositoryUtils = {
  /**
   * Get all repository instances
   */
  getAllRepositories: () => repositories,

  /**
   * Get repository by name
   */
  getRepository: <K extends keyof typeof repositories>(name: K): typeof repositories[K] => {
    return repositories[name];
  },

  /**
   * Initialize all repositories
   */
  initializeRepositories: (): {
    initialized: string[];
    errors: string[];
  } => {
    const initialized: string[] = [];
    const errors: string[] = [];

    try {
      Object.entries(repositories).forEach(([name, repository]) => {
        try {
          // Test repository configuration
          const config = repository.getConfig();
          if (config) {
            initialized.push(name);
          }
        } catch (error) {
          errors.push(`${name}: ${error}`);
        }
      });
    } catch (error) {
      errors.push(`General initialization error: ${error}`);
    }

    return { initialized, errors };
  },

  /**
   * Update configuration for all repositories
   */
  updateAllRepositoryConfigs: (config: Partial<RepositoryConfig>): boolean => {
    try {
      Object.values(repositories).forEach(repository => {
        repository.updateConfig(config);
      });
      return true;
    } catch (error) {
      console.error('Failed to update repository configs:', error);
      return false;
    }
  },

  /**
   * Get repository health status
   */
  getRepositoryHealth: async (): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    repositories: Record<string, 'healthy' | 'error'>;
  }> => {
    const health: Record<string, 'healthy' | 'error'> = {};
    let errorCount = 0;

    for (const [name, repository] of Object.entries(repositories)) {
      try {
        // Test repository by getting its config
        repository.getConfig();
        health[name] = 'healthy';
      } catch (error) {
        health[name] = 'error';
        errorCount++;
      }
    }

    const totalRepositories = Object.keys(repositories).length;
    let overall: 'healthy' | 'degraded' | 'unhealthy';

    if (errorCount === 0) {
      overall = 'healthy';
    } else if (errorCount < totalRepositories / 2) {
      overall = 'degraded';
    } else {
      overall = 'unhealthy';
    }

    return { overall, repositories: health };
  },

  /**
   * Clear all repository caches
   */
  clearAllCaches: async (): Promise<{
    success: boolean;
    clearedRepositories: string[];
    errors: string[];
  }> => {
    const clearedRepositories: string[] = [];
    const errors: string[] = [];

    for (const [name, repository] of Object.entries(repositories)) {
      try {
        // Clear cache by invalidating all tags for this repository
        await (repository as any).invalidateCacheByTags([name]);
        clearedRepositories.push(name);
      } catch (error) {
        errors.push(`${name}: ${error}`);
      }
    }

    return {
      success: errors.length === 0,
      clearedRepositories,
      errors,
    };
  },

  /**
   * Get repository statistics
   */
  getRepositoryStats: (): {
    totalRepositories: number;
    repositoryNames: string[];
    configSummary: Record<string, any>;
  } => {
    const repositoryNames = Object.keys(repositories);
    const configSummary: Record<string, any> = {};

    repositoryNames.forEach(name => {
      try {
        const repository = repositories[name as keyof typeof repositories];
        const config = repository.getConfig();
        configSummary[name] = {
          cacheTTL: config.cacheTTL,
          cachePrefix: config.cachePrefix,
          useMockData: config.useMockData,
          enableCache: config.enableCache,
        };
      } catch (error) {
        configSummary[name] = { error: String(error) };
      }
    });

    return {
      totalRepositories: repositoryNames.length,
      repositoryNames,
      configSummary,
    };
  },

  /**
   * Validate repository setup
   */
  validateRepositorySetup: (): {
    valid: boolean;
    issues: string[];
    recommendations: string[];
  } => {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check if all expected repositories are present
    const expectedRepositories = ['user', 'credit', 'analytics', 'content', 'notification', 'system'];
    const actualRepositories = Object.keys(repositories);

    expectedRepositories.forEach(expected => {
      if (!actualRepositories.includes(expected)) {
        issues.push(`Missing repository: ${expected}`);
      }
    });

    // Check repository configurations
    Object.entries(repositories).forEach(([name, repository]) => {
      try {
        const config = repository.getConfig();
        
        // Check cache configuration
        if (!config.enableCache) {
          recommendations.push(`Consider enabling cache for ${name} repository for better performance`);
        }

        if (config.cacheTTL < 60000) { // Less than 1 minute
          recommendations.push(`Cache TTL for ${name} repository is very short (${config.cacheTTL}ms)`);
        }

        // Check mock data configuration
        if (config.useMockData && process.env.NODE_ENV === 'production') {
          issues.push(`${name} repository is using mock data in production environment`);
        }

      } catch (error) {
        issues.push(`Failed to validate ${name} repository configuration: ${error}`);
      }
    });

    return {
      valid: issues.length === 0,
      issues,
      recommendations,
    };
  },
};

// ============================================================================
// REPOSITORY CONFIGURATION
// ============================================================================

/**
 * Default repository configuration
 */
export const DEFAULT_REPOSITORY_CONFIG: RepositoryConfig = {
  enableCache: true,
  cacheTTL: 5 * 60 * 1000, // 5 minutes
  cachePrefix: 'repo_',
  useMockData: process.env.NODE_ENV === 'development',
  mockDelay: { min: 100, max: 500 },
  mockErrorRate: 0.02, // 2%
};

/**
 * Repository configuration for different environments
 */
export const REPOSITORY_CONFIGS = {
  development: {
    ...DEFAULT_REPOSITORY_CONFIG,
    useMockData: true,
    mockErrorRate: 0.05, // 5% for testing error handling
  },
  staging: {
    ...DEFAULT_REPOSITORY_CONFIG,
    useMockData: false,
    cacheTTL: 2 * 60 * 1000, // 2 minutes
    mockErrorRate: 0.01, // 1%
  },
  production: {
    ...DEFAULT_REPOSITORY_CONFIG,
    useMockData: false,
    cacheTTL: 10 * 60 * 1000, // 10 minutes
    mockErrorRate: 0, // No mock errors in production
  },
} as const;

/**
 * Get repository configuration for current environment
 */
export const getRepositoryConfig = (): RepositoryConfig => {
  const env = process.env.NODE_ENV || 'development';
  return REPOSITORY_CONFIGS[env as keyof typeof REPOSITORY_CONFIGS] || REPOSITORY_CONFIGS.development;
};

// ============================================================================
// REPOSITORY INITIALIZATION
// ============================================================================

/**
 * Initialize repositories on module load
 */
const initializeOnLoad = (): void => {
  try {
    const result = RepositoryUtils.initializeRepositories();
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Repositories initialized:', result.initialized);
      if (result.errors.length > 0) {
        console.warn('Repository initialization errors:', result.errors);
      }
    }

    // Validate setup in development
    if (process.env.NODE_ENV === 'development') {
      const validation = RepositoryUtils.validateRepositorySetup();
      if (!validation.valid) {
        console.warn('Repository setup issues:', validation.issues);
      }
      if (validation.recommendations.length > 0) {
        console.info('Repository recommendations:', validation.recommendations);
      }
    }

  } catch (error) {
    console.error('Repository initialization failed:', error);
  }
};

// Initialize on module load
initializeOnLoad();

// ============================================================================
// REPOSITORY METADATA
// ============================================================================

/**
 * Repository layer metadata for debugging and documentation
 */
export const REPOSITORY_LAYER_INFO = {
  version: '1.0.0',
  description: 'Repository Layer for Mega AI Admin - Contains business logic for data operations',
  architecture: '3-layer MVI',
  dependencies: ['Model Layer', 'Data Sources'],
  exports: {
    repositories: [
      'UserRepository', 'CreditRepository', 'AnalyticsRepository', 
      'ContentRepository', 'NotificationRepository', 'SystemRepository'
    ],
    utilities: [
      'Repository utilities', 'Configuration management', 'Health monitoring'
    ]
  },
  rules: [
    'Contains business logic for data operations',
    'Uses types from Model Layer',
    'Uses data sources for actual data access',
    'Prepares for React Query integration',
    'No UI logic (belongs in Interface Layer)'
  ]
} as const;

// ============================================================================
// EXPORT ALL REPOSITORIES
// ============================================================================

/**
 * All repositories and utilities combined for easy access
 */
export const ALL_REPOSITORIES = {
  // Repository instances
  INSTANCES: repositories,
  
  // Repository classes
  CLASSES: {
    BaseRepository,
    UserRepository,
    CreditRepository,
    AnalyticsRepository,
    ContentRepository,
    NotificationRepository,
    SystemRepository,
  },
  
  // Utilities
  UTILS: RepositoryUtils,
  
  // Configuration
  CONFIG: {
    DEFAULT: DEFAULT_REPOSITORY_CONFIG,
    ENVIRONMENTS: REPOSITORY_CONFIGS,
    CURRENT: getRepositoryConfig(),
  },
  
  // Metadata
  INFO: REPOSITORY_LAYER_INFO,
} as const;
