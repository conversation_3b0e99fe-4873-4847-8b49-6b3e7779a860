/**
 * Data Layer Constants - Central Export
 * 
 * This module exports all constants used in the Data Layer.
 * Data Layer constants include API endpoints, cache keys, storage keys, and query keys.
 * 
 * Architecture Rules:
 * - Data Layer constants are for data access and caching only
 * - NO business logic constants (those belong in Model Layer)
 * - NO UI-specific constants (those belong in Interface Layer)
 * - NO validation schemas (those belong in Interface Layer utils)
 */

import { ALL_ENDPOINTS, API_BASE } from './apiEndpoints';
import { CACHE_KEYS, CONFIG_KEYS, COOKIE_KEYS, ERROR_KEYS, EVENT_KEYS, INDEXED_DB_CONFIG, LOCAL_STORAGE_KEYS, METRIC_KEYS, SESSION_STORAGE_KEYS } from './apiKeys';
import { ALL_QUERY_KEYS, USER_QUERY_KEYS } from './queryKeys';

// ============================================================================
// API ENDPOINTS
// ============================================================================

export {
  // API Base Configuration
  API_BASE,
  
  // Endpoint Groups
  AUTH_ENDPOINTS,
  USER_ENDPOINTS,
  CREDIT_ENDPOINTS,
  CONTENT_ENDPOINTS,
  ANALYTICS_ENDPOINTS,
  NOTIFICATION_ENDPOINTS,
  SYSTEM_ENDPOINTS,
  ADMIN_ENDPOINTS,
  FILE_ENDPOINTS,
  SEARCH_ENDPOINTS,
  EXPORT_ENDPOINTS,
  IMPORT_ENDPOINTS,
  WEBSOCKET_ENDPOINTS,
  EXTERNAL_ENDPOINTS,
  
  // Combined Endpoints
  ALL_ENDPOINTS,
  
  // Utility Functions
  buildApiUrl,
  getEndpointUrl,
  isValidEndpoint,
  getEndpoint,
} from './apiEndpoints';

// ============================================================================
// API KEYS AND STORAGE KEYS
// ============================================================================

export {
  // Storage Keys
  LOCAL_STORAGE_KEYS,
  SESSION_STORAGE_KEYS,
  COOKIE_KEYS,
  
  // Database Configuration
  INDEXED_DB_CONFIG,
  
  // API Headers
  API_HEADER_KEYS,
  
  // WebSocket Events
  WEBSOCKET_EVENT_KEYS,
  
  // Cache Keys
  CACHE_KEYS,
  
  // Application Events
  EVENT_KEYS,
  
  // Error Keys
  ERROR_KEYS,
  
  // Metrics Keys
  METRIC_KEYS,
  
  // Configuration Keys
  CONFIG_KEYS,
  
  // Utility Functions
  KeyUtils,
} from './apiKeys';

// ============================================================================
// REACT QUERY KEYS
// ============================================================================

export {
  // Query Key Factories
  createQueryKey,
  
  // Entity Query Keys
  USER_QUERY_KEYS,
  CREDIT_QUERY_KEYS,
  CONTENT_QUERY_KEYS,
  ANALYTICS_QUERY_KEYS,
  NOTIFICATION_QUERY_KEYS,
  SYSTEM_QUERY_KEYS,
  ADMIN_QUERY_KEYS,
  FILE_QUERY_KEYS,
  SEARCH_QUERY_KEYS,
  
  // Combined Query Keys
  ALL_QUERY_KEYS,
  
  // Utility Functions
  QueryKeyUtils,
} from './queryKeys';

// ============================================================================
// DATA LAYER UTILITIES
// ============================================================================

/**
 * Data Layer utility functions
 */
export const DataLayerUtils = {
  /**
   * Build cache key with prefix
   */
  buildCacheKey: (prefix: string, ...parts: (string | number)[]): string => {
    return [prefix, ...parts].join(':');
  },

  /**
   * Parse cache key
   */
  parseCacheKey: (key: string): { prefix: string; parts: string[] } => {
    const [prefix, ...parts] = key.split(':');
    return { prefix, parts };
  },

  /**
   * Generate API request ID
   */
  generateRequestId: (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  },

  /**
   * Build query string from params
   */
  buildQueryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(v => searchParams.append(key, String(v)));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });
    
    return searchParams.toString();
  },

  /**
   * Parse query string to params
   */
  parseQueryString: (queryString: string): Record<string, any> => {
    const params: Record<string, any> = {};
    const searchParams = new URLSearchParams(queryString);
    
    for (const [key, value] of searchParams.entries()) {
      if (params[key]) {
        // Handle multiple values for same key
        if (Array.isArray(params[key])) {
          params[key].push(value);
        } else {
          params[key] = [params[key], value];
        }
      } else {
        params[key] = value;
      }
    }
    
    return params;
  },

  /**
   * Validate API response structure
   */
  isValidApiResponse: (response: any): boolean => {
    return (
      response &&
      typeof response === 'object' &&
      'success' in response &&
      'data' in response
    );
  },

  /**
   * Extract error message from API response
   */
  extractErrorMessage: (error: any): string => {
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return 'An unknown error occurred';
  },

  /**
   * Check if error is network related
   */
  isNetworkError: (error: any): boolean => {
    return (
      error?.code === 'NETWORK_ERROR' ||
      error?.message?.includes('Network Error') ||
      error?.message?.includes('fetch')
    );
  },

  /**
   * Check if error is timeout related
   */
  isTimeoutError: (error: any): boolean => {
    return (
      error?.code === 'TIMEOUT' ||
      error?.message?.includes('timeout') ||
      error?.message?.includes('TIMEOUT')
    );
  },

  /**
   * Format file size for display
   */
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Validate file type
   */
  isValidFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  /**
   * Generate unique filename
   */
  generateUniqueFilename: (originalName: string): string => {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    const extension = originalName.split('.').pop();
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '');
    
    return `${nameWithoutExt}_${timestamp}_${random}.${extension}`;
  },
} as const;

// ============================================================================
// DATA LAYER CONFIGURATION
// ============================================================================

/**
 * Data Layer configuration
 */
export const DATA_LAYER_CONFIG = {
  // Cache settings
  CACHE: {
    DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
    LONG_TTL: 60 * 60 * 1000, // 1 hour
    SHORT_TTL: 1 * 60 * 1000, // 1 minute
    MAX_SIZE: 100, // Maximum number of cached items
  },

  // API settings
  API: {
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
    MAX_CONCURRENT_REQUESTS: 10,
  },

  // WebSocket settings
  WEBSOCKET: {
    RECONNECT_ATTEMPTS: 5,
    RECONNECT_DELAY: 1000, // 1 second
    HEARTBEAT_INTERVAL: 30000, // 30 seconds
  },

  // Storage settings
  STORAGE: {
    QUOTA_WARNING_THRESHOLD: 0.8, // 80% of quota
    CLEANUP_THRESHOLD: 0.9, // 90% of quota
    MAX_ITEM_SIZE: 5 * 1024 * 1024, // 5MB
  },
} as const;

// ============================================================================
// DATA LAYER METADATA
// ============================================================================

/**
 * Data Layer metadata for debugging and documentation
 */
export const DATA_LAYER_INFO = {
  version: '1.0.0',
  description: 'Data Layer for Mega AI Admin - Handles data access, caching, and API communication',
  architecture: '3-layer MVI',
  dependencies: ['Model Layer'],
  exports: {
    constants: [
      'API Endpoints', 'Storage Keys', 'Query Keys', 'Cache Keys', 'Event Keys'
    ],
    utilities: [
      'API utilities', 'Cache utilities', 'Storage utilities', 'Query utilities'
    ]
  },
  rules: [
    'Data access and caching constants only',
    'No business logic constants',
    'No UI-specific constants',
    'No validation schemas',
    'Can import from Model Layer only'
  ]
} as const;

// ============================================================================
// DEVELOPMENT HELPERS
// ============================================================================

/**
 * Development helper to validate Data Layer integrity
 * Only available in development mode
 */
export const validateDataLayer = () => {
  if (process.env.NODE_ENV !== 'development') {
    return { valid: true, message: 'Validation only available in development' };
  }

  const checks = [
    // Check that all required exports are available
    typeof API_BASE !== 'undefined',
    typeof LOCAL_STORAGE_KEYS !== 'undefined',
    typeof USER_QUERY_KEYS !== 'undefined',
    typeof CACHE_KEYS !== 'undefined',
    // Add more checks as needed
  ];

  const allValid = checks.every(check => check === true);

  return {
    valid: allValid,
    message: allValid 
      ? 'Data Layer validation passed' 
      : 'Data Layer validation failed - some exports are missing',
    timestamp: new Date().toISOString()
  };
};

// ============================================================================
// EXPORT ALL FOR CONVENIENCE
// ============================================================================

/**
 * All Data Layer constants combined for easy access
 */
export const ALL_DATA_CONSTANTS = {
  ENDPOINTS: ALL_ENDPOINTS,
  STORAGE: {
    LOCAL: LOCAL_STORAGE_KEYS,
    SESSION: SESSION_STORAGE_KEYS,
    COOKIES: COOKIE_KEYS,
    INDEXED_DB: INDEXED_DB_CONFIG,
  },
  CACHE: CACHE_KEYS,
  QUERIES: ALL_QUERY_KEYS,
  EVENTS: EVENT_KEYS,
  ERRORS: ERROR_KEYS,
  METRICS: METRIC_KEYS,
  CONFIG: CONFIG_KEYS,
} as const;
