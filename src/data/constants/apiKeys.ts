/**
 * API Keys and Storage Keys Configuration
 * Defines all keys used for API, caching, and storage throughout the application
 */

/**
 * Local Storage Keys
 */
export const LOCAL_STORAGE_KEYS = {
  // Authentication
  AUTH_TOKEN: 'mega-ai-admin-auth-token',
  REFRESH_TOKEN: 'mega-ai-admin-refresh-token',
  USER_SESSION: 'mega-ai-admin-user-session',
  
  // User Preferences
  USER_PREFERENCES: 'mega-ai-admin-user-preferences',
  THEME: 'mega-ai-admin-theme',
  LANGUAGE: 'mega-ai-admin-language',
  SIDEBAR_STATE: 'mega-ai-admin-sidebar-state',
  
  // Dashboard Settings
  DASHBOARD_LAYOUT: 'mega-ai-admin-dashboard-layout',
  TABLE_PREFERENCES: 'mega-ai-admin-table-preferences',
  FILTER_PREFERENCES: 'mega-ai-admin-filter-preferences',
  
  // Cache
  CACHE_VERSION: 'mega-ai-admin-cache-version',
  LAST_SYNC: 'mega-ai-admin-last-sync',
  OFFLINE_DATA: 'mega-ai-admin-offline-data',
  
  // Form Data
  DRAFT_NOTIFICATIONS: 'mega-ai-admin-draft-notifications',
  UNSAVED_CHANGES: 'mega-ai-admin-unsaved-changes',
  
  // Analytics
  ANALYTICS_PREFERENCES: 'mega-ai-admin-analytics-preferences',
  EXPORT_SETTINGS: 'mega-ai-admin-export-settings',
} as const;

/**
 * Session Storage Keys
 */
export const SESSION_STORAGE_KEYS = {
  // Navigation
  CURRENT_PAGE: 'mega-ai-admin-current-page',
  NAVIGATION_HISTORY: 'mega-ai-admin-navigation-history',
  
  // Search
  SEARCH_HISTORY: 'mega-ai-admin-search-history',
  RECENT_SEARCHES: 'mega-ai-admin-recent-searches',
  
  // Temporary Data
  TEMP_UPLOADS: 'mega-ai-admin-temp-uploads',
  FORM_BACKUP: 'mega-ai-admin-form-backup',
  
  // UI State
  MODAL_STATE: 'mega-ai-admin-modal-state',
  TOAST_QUEUE: 'mega-ai-admin-toast-queue',
} as const;

/**
 * IndexedDB Keys and Database Configuration
 */
export const INDEXED_DB_CONFIG = {
  DATABASE_NAME: 'MegaAIAdminDB',
  VERSION: 1,
  STORES: {
    USERS: 'users',
    CONTENT: 'content',
    ANALYTICS: 'analytics',
    NOTIFICATIONS: 'notifications',
    CACHE: 'cache',
    SETTINGS: 'settings',
  },
} as const;

/**
 * Cookie Keys
 */
export const COOKIE_KEYS = {
  SESSION_ID: 'mega-ai-admin-session-id',
  CSRF_TOKEN: 'mega-ai-admin-csrf-token',
  REMEMBER_ME: 'mega-ai-admin-remember-me',
  ANALYTICS_CONSENT: 'mega-ai-admin-analytics-consent',
  COOKIE_CONSENT: 'mega-ai-admin-cookie-consent',
} as const;

/**
 * API Header Keys
 */
export const API_HEADER_KEYS = {
  AUTHORIZATION: 'Authorization',
  CONTENT_TYPE: 'Content-Type',
  ACCEPT: 'Accept',
  CSRF_TOKEN: 'X-CSRF-Token',
  REQUEST_ID: 'X-Request-ID',
  CLIENT_VERSION: 'X-Client-Version',
  USER_AGENT: 'User-Agent',
  ACCEPT_LANGUAGE: 'Accept-Language',
  CACHE_CONTROL: 'Cache-Control',
  IF_NONE_MATCH: 'If-None-Match',
  ETAG: 'ETag',
} as const;

/**
 * WebSocket Event Keys
 */
export const WEBSOCKET_EVENT_KEYS = {
  // Connection
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  RECONNECT: 'reconnect',
  ERROR: 'error',
  
  // Notifications
  NOTIFICATION_RECEIVED: 'notification:received',
  NOTIFICATION_READ: 'notification:read',
  NOTIFICATION_DELETED: 'notification:deleted',
  
  // System Updates
  SYSTEM_STATUS_UPDATE: 'system:status:update',
  MAINTENANCE_MODE: 'system:maintenance',
  
  // Real-time Data
  USER_ACTIVITY_UPDATE: 'user:activity:update',
  CONTENT_PROCESSING_UPDATE: 'content:processing:update',
  ANALYTICS_UPDATE: 'analytics:update',
  
  // Admin Events
  ADMIN_ACTION: 'admin:action',
  USER_SESSION_UPDATE: 'user:session:update',
} as const;

/**
 * Cache Keys for different data types
 */
export const CACHE_KEYS = {
  // User Data
  USER_LIST: 'cache:users:list',
  USER_DETAILS: (id: number) => `cache:users:details:${id}`,
  USER_STATS: 'cache:users:stats',
  USER_ACTIVITY: (id: number) => `cache:users:activity:${id}`,
  
  // Credit Data
  CREDIT_BALANCE: (userId: number) => `cache:credits:balance:${userId}`,
  CREDIT_TRANSACTIONS: (userId: number) => `cache:credits:transactions:${userId}`,
  CREDIT_STATS: 'cache:credits:stats',
  CREDIT_PACKAGES: 'cache:credits:packages',
  
  // Content Data
  CONTENT_LIST: 'cache:content:list',
  CONTENT_DETAILS: (id: string) => `cache:content:details:${id}`,
  CONTENT_STATS: 'cache:content:stats',
  USER_CONTENT: (userId: number) => `cache:content:user:${userId}`,
  
  // Analytics Data
  DASHBOARD_STATS: 'cache:analytics:dashboard',
  USER_GROWTH: 'cache:analytics:user-growth',
  CREDIT_USAGE: 'cache:analytics:credit-usage',
  CONTENT_CREATION: 'cache:analytics:content-creation',
  SYSTEM_PERFORMANCE: 'cache:analytics:system-performance',
  
  // Notification Data
  NOTIFICATION_LIST: 'cache:notifications:list',
  NOTIFICATION_TEMPLATES: 'cache:notifications:templates',
  NOTIFICATION_PREFERENCES: (userId: number) => `cache:notifications:preferences:${userId}`,
  
  // System Data
  SYSTEM_STATUS: 'cache:system:status',
  SYSTEM_CONFIG: 'cache:system:config',
  FEATURE_FLAGS: 'cache:system:feature-flags',
  ADMIN_USERS: 'cache:admin:users',
} as const;

/**
 * Event Keys for application events
 */
export const EVENT_KEYS = {
  // User Events
  USER_LOGIN: 'user:login',
  USER_LOGOUT: 'user:logout',
  USER_CREATED: 'user:created',
  USER_UPDATED: 'user:updated',
  USER_DELETED: 'user:deleted',
  
  // Credit Events
  CREDITS_ADDED: 'credits:added',
  CREDITS_USED: 'credits:used',
  CREDITS_REFUNDED: 'credits:refunded',
  
  // Content Events
  CONTENT_CREATED: 'content:created',
  CONTENT_PROCESSING_STARTED: 'content:processing:started',
  CONTENT_PROCESSING_COMPLETED: 'content:processing:completed',
  CONTENT_PROCESSING_FAILED: 'content:processing:failed',
  
  // System Events
  SYSTEM_MAINTENANCE_START: 'system:maintenance:start',
  SYSTEM_MAINTENANCE_END: 'system:maintenance:end',
  SYSTEM_ERROR: 'system:error',
  
  // UI Events
  THEME_CHANGED: 'ui:theme:changed',
  LANGUAGE_CHANGED: 'ui:language:changed',
  SIDEBAR_TOGGLED: 'ui:sidebar:toggled',
} as const;

/**
 * Error Keys for error tracking
 */
export const ERROR_KEYS = {
  // API Errors
  NETWORK_ERROR: 'error:network',
  TIMEOUT_ERROR: 'error:timeout',
  AUTHENTICATION_ERROR: 'error:authentication',
  AUTHORIZATION_ERROR: 'error:authorization',
  VALIDATION_ERROR: 'error:validation',
  SERVER_ERROR: 'error:server',
  
  // Application Errors
  COMPONENT_ERROR: 'error:component',
  ROUTING_ERROR: 'error:routing',
  STATE_ERROR: 'error:state',
  
  // File Errors
  UPLOAD_ERROR: 'error:upload',
  DOWNLOAD_ERROR: 'error:download',
  FILE_SIZE_ERROR: 'error:file:size',
  FILE_TYPE_ERROR: 'error:file:type',
} as const;

/**
 * Metric Keys for analytics and monitoring
 */
export const METRIC_KEYS = {
  // Performance Metrics
  PAGE_LOAD_TIME: 'metric:performance:page_load_time',
  API_RESPONSE_TIME: 'metric:performance:api_response_time',
  COMPONENT_RENDER_TIME: 'metric:performance:component_render_time',
  
  // User Metrics
  USER_SESSION_DURATION: 'metric:user:session_duration',
  USER_ACTIONS_PER_SESSION: 'metric:user:actions_per_session',
  USER_PAGE_VIEWS: 'metric:user:page_views',
  
  // System Metrics
  MEMORY_USAGE: 'metric:system:memory_usage',
  CPU_USAGE: 'metric:system:cpu_usage',
  ERROR_RATE: 'metric:system:error_rate',
  
  // Business Metrics
  DAILY_ACTIVE_USERS: 'metric:business:daily_active_users',
  CONTENT_CREATION_RATE: 'metric:business:content_creation_rate',
  CREDIT_USAGE_RATE: 'metric:business:credit_usage_rate',
} as const;

/**
 * Configuration Keys
 */
export const CONFIG_KEYS = {
  API_BASE_URL: 'REACT_APP_API_BASE_URL',
  WS_BASE_URL: 'REACT_APP_WS_BASE_URL',
  CDN_BASE_URL: 'REACT_APP_CDN_BASE_URL',
  GOOGLE_ANALYTICS_ID: 'REACT_APP_GA_ID',
  SENTRY_DSN: 'REACT_APP_SENTRY_DSN',
  ENVIRONMENT: 'NODE_ENV',
  VERSION: 'REACT_APP_VERSION',
  BUILD_DATE: 'REACT_APP_BUILD_DATE',
} as const;

/**
 * Utility functions for key management
 */
export const KeyUtils = {
  /**
   * Generate cache key with timestamp
   */
  generateCacheKey: (baseKey: string, timestamp?: number): string => {
    const ts = timestamp || Date.now();
    return `${baseKey}:${ts}`;
  },

  /**
   * Generate user-specific key
   */
  generateUserKey: (baseKey: string, userId: number): string => {
    return `${baseKey}:user:${userId}`;
  },

  /**
   * Generate time-based key
   */
  generateTimeKey: (baseKey: string, date?: Date): string => {
    const dateStr = (date || new Date()).toISOString().split('T')[0];
    return `${baseKey}:${dateStr}`;
  },

  /**
   * Parse cache key to extract components
   */
  parseCacheKey: (key: string): { base: string; params: string[] } => {
    const parts = key.split(':');
    return {
      base: parts[0],
      params: parts.slice(1),
    };
  },

  /**
   * Check if key is expired
   */
  isKeyExpired: (key: string, maxAge: number): boolean => {
    const parts = key.split(':');
    const timestamp = parseInt(parts[parts.length - 1]);
    if (isNaN(timestamp)) return false;
    
    return Date.now() - timestamp > maxAge;
  },
} as const;
