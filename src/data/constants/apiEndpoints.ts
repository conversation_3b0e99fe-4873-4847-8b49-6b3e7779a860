/**
 * API Endpoints Configuration
 * Defines all API endpoints used throughout the application
 */

/**
 * Base API configuration
 */
export const API_BASE = {
  URL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api',
  VERSION: 'v1',
  TIMEOUT: 30000,
} as const;

/**
 * Authentication endpoints
 */
export const AUTH_ENDPOINTS = {
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH: '/auth/refresh',
  VERIFY: '/auth/verify',
  FORGOT_PASSWORD: '/auth/forgot-password',
  RESET_PASSWORD: '/auth/reset-password',
  CHAN<PERSON>_PASSWORD: '/auth/change-password',
  PROFILE: '/auth/profile',
} as const;

/**
 * User management endpoints
 */
export const USER_ENDPOINTS = {
  BASE: '/users',
  LIST: '/users',
  CREATE: '/users',
  GET_BY_ID: (id: number) => `/users/${id}`,
  UPDATE: (id: number) => `/users/${id}`,
  DELETE: (id: number) => `/users/${id}`,
  SEARCH: '/users/search',
  BULK_UPDATE: '/users/bulk',
  BULK_DELETE: '/users/bulk/delete',
  EXPORT: '/users/export',
  IMPORT: '/users/import',
  STATS: '/users/stats',
  ACTIVITY: (id: number) => `/users/${id}/activity`,
  PREFERENCES: (id: number) => `/users/${id}/preferences`,
} as const;

/**
 * Credit management endpoints
 */
export const CREDIT_ENDPOINTS = {
  BASE: '/credits',
  BALANCE: (userId: number) => `/credits/balance/${userId}`,
  TRANSACTIONS: '/credits/transactions',
  ADD_CREDITS: '/credits/add',
  SUBTRACT_CREDITS: '/credits/subtract',
  TRANSFER: '/credits/transfer',
  HISTORY: (userId: number) => `/credits/history/${userId}`,
  PACKAGES: '/credits/packages',
  PURCHASE: '/credits/purchase',
  REFUND: '/credits/refund',
  STATS: '/credits/stats',
  USAGE_STATS: (userId: number) => `/credits/usage/${userId}`,
  BULK_OPERATIONS: '/credits/bulk',
} as const;

/**
 * Content management endpoints
 */
export const CONTENT_ENDPOINTS = {
  BASE: '/content',
  LIST: '/content',
  CREATE: '/content',
  GET_BY_ID: (id: string) => `/content/${id}`,
  UPDATE: (id: string) => `/content/${id}`,
  DELETE: (id: string) => `/content/${id}`,
  SEARCH: '/content/search',
  UPLOAD: '/content/upload',
  PROCESS: (id: string) => `/content/${id}/process`,
  CANCEL: (id: string) => `/content/${id}/cancel`,
  DOWNLOAD: (id: string) => `/content/${id}/download`,
  THUMBNAIL: (id: string) => `/content/${id}/thumbnail`,
  BULK_OPERATIONS: '/content/bulk',
  STATS: '/content/stats',
  USER_CONTENT: (userId: number) => `/content/user/${userId}`,
  PROCESSING_JOBS: '/content/jobs',
  JOB_STATUS: (jobId: string) => `/content/jobs/${jobId}`,
} as const;

/**
 * Analytics endpoints
 */
export const ANALYTICS_ENDPOINTS = {
  BASE: '/analytics',
  DASHBOARD: '/analytics/dashboard',
  USER_GROWTH: '/analytics/user-growth',
  CREDIT_USAGE: '/analytics/credit-usage',
  CONTENT_CREATION: '/analytics/content-creation',
  SYSTEM_PERFORMANCE: '/analytics/system-performance',
  TOP_PERFORMERS: '/analytics/top-performers',
  EXPORT: '/analytics/export',
  REAL_TIME: '/analytics/real-time',
  CUSTOM_REPORT: '/analytics/custom-report',
  METRICS: '/analytics/metrics',
  TRENDS: '/analytics/trends',
} as const;

/**
 * Notification endpoints
 */
export const NOTIFICATION_ENDPOINTS = {
  BASE: '/notifications',
  LIST: '/notifications',
  CREATE: '/notifications',
  GET_BY_ID: (id: string) => `/notifications/${id}`,
  UPDATE: (id: string) => `/notifications/${id}`,
  DELETE: (id: string) => `/notifications/${id}`,
  SEND: (id: string) => `/notifications/${id}/send`,
  CANCEL: (id: string) => `/notifications/${id}/cancel`,
  TEMPLATES: '/notifications/templates',
  CAMPAIGNS: '/notifications/campaigns',
  DELIVERY_LOG: '/notifications/delivery-log',
  PREFERENCES: (userId: number) => `/notifications/preferences/${userId}`,
  MARK_READ: (id: string) => `/notifications/${id}/read`,
  BULK_SEND: '/notifications/bulk-send',
  ANALYTICS: '/notifications/analytics',
} as const;

/**
 * System management endpoints
 */
export const SYSTEM_ENDPOINTS = {
  BASE: '/system',
  STATUS: '/system/status',
  HEALTH: '/system/health',
  CONFIG: '/system/config',
  UPDATE_CONFIG: '/system/config',
  MAINTENANCE: '/system/maintenance',
  LOGS: '/system/logs',
  METRICS: '/system/metrics',
  BACKUP: '/system/backup',
  RESTORE: '/system/restore',
  AUDIT_LOG: '/system/audit-log',
  FEATURE_FLAGS: '/system/feature-flags',
  CACHE: '/system/cache',
  CLEAR_CACHE: '/system/cache/clear',
} as const;

/**
 * Admin management endpoints
 */
export const ADMIN_ENDPOINTS = {
  BASE: '/admin',
  LIST: '/admin/users',
  CREATE: '/admin/users',
  GET_BY_ID: (id: number) => `/admin/users/${id}`,
  UPDATE: (id: number) => `/admin/users/${id}`,
  DELETE: (id: number) => `/admin/users/${id}`,
  PERMISSIONS: '/admin/permissions',
  ROLES: '/admin/roles',
  ACTIVITY: '/admin/activity',
  SESSIONS: '/admin/sessions',
  REVOKE_SESSION: (sessionId: string) => `/admin/sessions/${sessionId}/revoke`,
} as const;

/**
 * File management endpoints
 */
export const FILE_ENDPOINTS = {
  BASE: '/files',
  UPLOAD: '/files/upload',
  DOWNLOAD: (id: string) => `/files/${id}/download`,
  DELETE: (id: string) => `/files/${id}`,
  INFO: (id: string) => `/files/${id}/info`,
  THUMBNAIL: (id: string) => `/files/${id}/thumbnail`,
  PREVIEW: (id: string) => `/files/${id}/preview`,
  BULK_DELETE: '/files/bulk/delete',
  STORAGE_STATS: '/files/storage/stats',
  CLEANUP: '/files/cleanup',
} as const;

/**
 * Search endpoints
 */
export const SEARCH_ENDPOINTS = {
  BASE: '/search',
  GLOBAL: '/search/global',
  USERS: '/search/users',
  CONTENT: '/search/content',
  NOTIFICATIONS: '/search/notifications',
  SUGGESTIONS: '/search/suggestions',
  AUTOCOMPLETE: '/search/autocomplete',
  ADVANCED: '/search/advanced',
} as const;

/**
 * Export endpoints
 */
export const EXPORT_ENDPOINTS = {
  BASE: '/export',
  USERS: '/export/users',
  CONTENT: '/export/content',
  ANALYTICS: '/export/analytics',
  CREDITS: '/export/credits',
  NOTIFICATIONS: '/export/notifications',
  SYSTEM_LOGS: '/export/system-logs',
  AUDIT_LOGS: '/export/audit-logs',
} as const;

/**
 * Import endpoints
 */
export const IMPORT_ENDPOINTS = {
  BASE: '/import',
  USERS: '/import/users',
  CONTENT: '/import/content',
  CREDITS: '/import/credits',
  VALIDATE: '/import/validate',
  STATUS: (jobId: string) => `/import/status/${jobId}`,
} as const;

/**
 * WebSocket endpoints
 */
export const WEBSOCKET_ENDPOINTS = {
  BASE: process.env.REACT_APP_WS_BASE_URL || 'ws://localhost:3001',
  NOTIFICATIONS: '/ws/notifications',
  SYSTEM_STATUS: '/ws/system-status',
  PROCESSING_UPDATES: '/ws/processing',
  REAL_TIME_ANALYTICS: '/ws/analytics',
  ADMIN_ACTIVITY: '/ws/admin-activity',
} as const;

/**
 * External service endpoints
 */
export const EXTERNAL_ENDPOINTS = {
  PAYMENT_GATEWAY: process.env.REACT_APP_PAYMENT_GATEWAY_URL || '',
  EMAIL_SERVICE: process.env.REACT_APP_EMAIL_SERVICE_URL || '',
  SMS_SERVICE: process.env.REACT_APP_SMS_SERVICE_URL || '',
  STORAGE_SERVICE: process.env.REACT_APP_STORAGE_SERVICE_URL || '',
  CDN_BASE: process.env.REACT_APP_CDN_BASE_URL || '',
} as const;

/**
 * API endpoint builder utility
 */
export const buildApiUrl = (endpoint: string, params?: Record<string, any>): string => {
  let url = `${API_BASE.URL}${endpoint}`;
  
  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    const queryString = searchParams.toString();
    if (queryString) {
      url += `?${queryString}`;
    }
  }
  
  return url;
};

/**
 * Get full endpoint URL
 */
export const getEndpointUrl = (endpoint: string): string => {
  return `${API_BASE.URL}${endpoint}`;
};

/**
 * All endpoints combined for easy access
 */
export const ALL_ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  USER: USER_ENDPOINTS,
  CREDIT: CREDIT_ENDPOINTS,
  CONTENT: CONTENT_ENDPOINTS,
  ANALYTICS: ANALYTICS_ENDPOINTS,
  NOTIFICATION: NOTIFICATION_ENDPOINTS,
  SYSTEM: SYSTEM_ENDPOINTS,
  ADMIN: ADMIN_ENDPOINTS,
  FILE: FILE_ENDPOINTS,
  SEARCH: SEARCH_ENDPOINTS,
  EXPORT: EXPORT_ENDPOINTS,
  IMPORT: IMPORT_ENDPOINTS,
  WEBSOCKET: WEBSOCKET_ENDPOINTS,
  EXTERNAL: EXTERNAL_ENDPOINTS,
} as const;

/**
 * Endpoint validation
 */
export const isValidEndpoint = (endpoint: string): boolean => {
  return endpoint.startsWith('/') && endpoint.length > 1;
};

/**
 * Get endpoint by category and name
 */
export const getEndpoint = (category: keyof typeof ALL_ENDPOINTS, name: string): string | undefined => {
  const categoryEndpoints = ALL_ENDPOINTS[category];
  return (categoryEndpoints as any)[name];
};
