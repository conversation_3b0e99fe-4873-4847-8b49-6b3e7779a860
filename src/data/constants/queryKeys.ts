/**
 * React Query Key Definitions
 * Defines all query keys used for React Query caching and invalidation
 */

/**
 * Base query key factory
 */
export const createQueryKey = (entity: string, ...params: (string | number | undefined)[]): string[] => {
  return [entity, ...params.filter(p => p !== undefined).map(String)];
};

/**
 * User-related query keys
 */
export const USER_QUERY_KEYS = {
  // Base keys
  all: ['users'] as const,
  lists: () => [...USER_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: Record<string, any>) => [...USER_QUERY_KEYS.lists(), filters] as const,
  details: () => [...USER_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...USER_QUERY_KEYS.details(), id] as const,
  
  // Specific queries
  search: (query: string) => [...USER_QUERY_KEYS.all, 'search', query] as const,
  stats: () => [...USER_QUERY_KEYS.all, 'stats'] as const,
  activity: (id: number) => [...USER_QUERY_KEYS.detail(id), 'activity'] as const,
  preferences: (id: number) => [...USER_QUERY_KEYS.detail(id), 'preferences'] as const,
  
  // Credit-related
  credits: (id: number) => [...USER_QUERY_KEYS.detail(id), 'credits'] as const,
  creditHistory: (id: number) => [...USER_QUERY_KEYS.credits(id), 'history'] as const,
  creditStats: (id: number) => [...USER_QUERY_KEYS.credits(id), 'stats'] as const,
  
  // Content-related
  content: (id: number) => [...USER_QUERY_KEYS.detail(id), 'content'] as const,
  contentStats: (id: number) => [...USER_QUERY_KEYS.content(id), 'stats'] as const,
} as const;

/**
 * Credit-related query keys
 */
export const CREDIT_QUERY_KEYS = {
  // Base keys
  all: ['credits'] as const,
  transactions: () => [...CREDIT_QUERY_KEYS.all, 'transactions'] as const,
  transaction: (id: string) => [...CREDIT_QUERY_KEYS.transactions(), id] as const,
  
  // Balance queries
  balances: () => [...CREDIT_QUERY_KEYS.all, 'balances'] as const,
  balance: (userId: number) => [...CREDIT_QUERY_KEYS.balances(), userId] as const,
  
  // Statistics
  stats: () => [...CREDIT_QUERY_KEYS.all, 'stats'] as const,
  userStats: (userId: number) => [...CREDIT_QUERY_KEYS.stats(), 'user', userId] as const,
  systemStats: () => [...CREDIT_QUERY_KEYS.stats(), 'system'] as const,
  
  // Packages
  packages: () => [...CREDIT_QUERY_KEYS.all, 'packages'] as const,
  package: (id: string) => [...CREDIT_QUERY_KEYS.packages(), id] as const,
  
  // History and reports
  history: (userId: number, filters?: Record<string, any>) => 
    [...CREDIT_QUERY_KEYS.balance(userId), 'history', filters] as const,
  usageReport: (userId: number, period: string) => 
    [...CREDIT_QUERY_KEYS.userStats(userId), 'usage', period] as const,
} as const;

/**
 * Content-related query keys
 */
export const CONTENT_QUERY_KEYS = {
  // Base keys
  all: ['content'] as const,
  lists: () => [...CONTENT_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: Record<string, any>) => [...CONTENT_QUERY_KEYS.lists(), filters] as const,
  details: () => [...CONTENT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...CONTENT_QUERY_KEYS.details(), id] as const,
  
  // Search and filtering
  search: (query: string, filters?: Record<string, any>) => 
    [...CONTENT_QUERY_KEYS.all, 'search', query, filters] as const,
  
  // User content
  userContent: (userId: number) => [...CONTENT_QUERY_KEYS.all, 'user', userId] as const,
  userContentList: (userId: number, filters?: Record<string, any>) => 
    [...CONTENT_QUERY_KEYS.userContent(userId), 'list', filters] as const,
  
  // Processing
  processingJobs: () => [...CONTENT_QUERY_KEYS.all, 'processing', 'jobs'] as const,
  processingJob: (jobId: string) => [...CONTENT_QUERY_KEYS.processingJobs(), jobId] as const,
  userProcessingJobs: (userId: number) => 
    [...CONTENT_QUERY_KEYS.processingJobs(), 'user', userId] as const,
  
  // Statistics
  stats: () => [...CONTENT_QUERY_KEYS.all, 'stats'] as const,
  userStats: (userId: number) => [...CONTENT_QUERY_KEYS.stats(), 'user', userId] as const,
  typeStats: (type: 'video' | 'photo') => [...CONTENT_QUERY_KEYS.stats(), 'type', type] as const,
  
  // Files and media
  thumbnail: (id: string) => [...CONTENT_QUERY_KEYS.detail(id), 'thumbnail'] as const,
  preview: (id: string) => [...CONTENT_QUERY_KEYS.detail(id), 'preview'] as const,
  download: (id: string) => [...CONTENT_QUERY_KEYS.detail(id), 'download'] as const,
} as const;

/**
 * Analytics-related query keys
 */
export const ANALYTICS_QUERY_KEYS = {
  // Base keys
  all: ['analytics'] as const,
  dashboard: () => [...ANALYTICS_QUERY_KEYS.all, 'dashboard'] as const,
  
  // Dashboard components
  dashboardStats: () => [...ANALYTICS_QUERY_KEYS.dashboard(), 'stats'] as const,
  quickStats: () => [...ANALYTICS_QUERY_KEYS.dashboard(), 'quick-stats'] as const,
  
  // Growth analytics
  userGrowth: (period?: string) => [...ANALYTICS_QUERY_KEYS.all, 'user-growth', period] as const,
  creditUsage: (period?: string) => [...ANALYTICS_QUERY_KEYS.all, 'credit-usage', period] as const,
  contentCreation: (period?: string) => [...ANALYTICS_QUERY_KEYS.all, 'content-creation', period] as const,
  
  // Performance analytics
  systemPerformance: (period?: string) => 
    [...ANALYTICS_QUERY_KEYS.all, 'system-performance', period] as const,
  
  // Reports
  reports: () => [...ANALYTICS_QUERY_KEYS.all, 'reports'] as const,
  report: (reportId: string) => [...ANALYTICS_QUERY_KEYS.reports(), reportId] as const,
  customReport: (config: Record<string, any>) => 
    [...ANALYTICS_QUERY_KEYS.reports(), 'custom', config] as const,
  
  // Top performers
  topPerformers: (metric: string, period?: string) => 
    [...ANALYTICS_QUERY_KEYS.all, 'top-performers', metric, period] as const,
  
  // Real-time data
  realTime: () => [...ANALYTICS_QUERY_KEYS.all, 'real-time'] as const,
  realTimeMetric: (metric: string) => [...ANALYTICS_QUERY_KEYS.realTime(), metric] as const,
} as const;

/**
 * Notification-related query keys
 */
export const NOTIFICATION_QUERY_KEYS = {
  // Base keys
  all: ['notifications'] as const,
  lists: () => [...NOTIFICATION_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: Record<string, any>) => [...NOTIFICATION_QUERY_KEYS.lists(), filters] as const,
  details: () => [...NOTIFICATION_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...NOTIFICATION_QUERY_KEYS.details(), id] as const,
  
  // Templates
  templates: () => [...NOTIFICATION_QUERY_KEYS.all, 'templates'] as const,
  template: (id: string) => [...NOTIFICATION_QUERY_KEYS.templates(), id] as const,
  
  // Campaigns
  campaigns: () => [...NOTIFICATION_QUERY_KEYS.all, 'campaigns'] as const,
  campaign: (id: string) => [...NOTIFICATION_QUERY_KEYS.campaigns(), id] as const,
  campaignStats: (id: string) => [...NOTIFICATION_QUERY_KEYS.campaign(id), 'stats'] as const,
  
  // User notifications
  userNotifications: (userId: number) => 
    [...NOTIFICATION_QUERY_KEYS.all, 'user', userId] as const,
  userPreferences: (userId: number) => 
    [...NOTIFICATION_QUERY_KEYS.userNotifications(userId), 'preferences'] as const,
  
  // Delivery and analytics
  deliveryLog: () => [...NOTIFICATION_QUERY_KEYS.all, 'delivery-log'] as const,
  analytics: (period?: string) => [...NOTIFICATION_QUERY_KEYS.all, 'analytics', period] as const,
} as const;

/**
 * System-related query keys
 */
export const SYSTEM_QUERY_KEYS = {
  // Base keys
  all: ['system'] as const,
  
  // Status and health
  status: () => [...SYSTEM_QUERY_KEYS.all, 'status'] as const,
  health: () => [...SYSTEM_QUERY_KEYS.all, 'health'] as const,
  
  // Configuration
  config: () => [...SYSTEM_QUERY_KEYS.all, 'config'] as const,
  featureFlags: () => [...SYSTEM_QUERY_KEYS.all, 'feature-flags'] as const,
  
  // Monitoring
  metrics: () => [...SYSTEM_QUERY_KEYS.all, 'metrics'] as const,
  logs: (level?: string) => [...SYSTEM_QUERY_KEYS.all, 'logs', level] as const,
  auditLog: (filters?: Record<string, any>) => 
    [...SYSTEM_QUERY_KEYS.all, 'audit-log', filters] as const,
  
  // Maintenance
  maintenance: () => [...SYSTEM_QUERY_KEYS.all, 'maintenance'] as const,
  
  // Cache
  cache: () => [...SYSTEM_QUERY_KEYS.all, 'cache'] as const,
  cacheStats: () => [...SYSTEM_QUERY_KEYS.cache(), 'stats'] as const,
} as const;

/**
 * Admin-related query keys
 */
export const ADMIN_QUERY_KEYS = {
  // Base keys
  all: ['admin'] as const,
  
  // Admin users
  users: () => [...ADMIN_QUERY_KEYS.all, 'users'] as const,
  user: (id: number) => [...ADMIN_QUERY_KEYS.users(), id] as const,
  
  // Permissions and roles
  permissions: () => [...ADMIN_QUERY_KEYS.all, 'permissions'] as const,
  roles: () => [...ADMIN_QUERY_KEYS.all, 'roles'] as const,
  userPermissions: (userId: number) => [...ADMIN_QUERY_KEYS.user(userId), 'permissions'] as const,
  
  // Activity
  activity: () => [...ADMIN_QUERY_KEYS.all, 'activity'] as const,
  userActivity: (userId: number) => [...ADMIN_QUERY_KEYS.activity(), 'user', userId] as const,
  
  // Sessions
  sessions: () => [...ADMIN_QUERY_KEYS.all, 'sessions'] as const,
  userSessions: (userId: number) => [...ADMIN_QUERY_KEYS.sessions(), 'user', userId] as const,
} as const;

/**
 * File-related query keys
 */
export const FILE_QUERY_KEYS = {
  // Base keys
  all: ['files'] as const,
  
  // File operations
  info: (id: string) => [...FILE_QUERY_KEYS.all, 'info', id] as const,
  thumbnail: (id: string) => [...FILE_QUERY_KEYS.all, 'thumbnail', id] as const,
  preview: (id: string) => [...FILE_QUERY_KEYS.all, 'preview', id] as const,
  
  // Storage
  storageStats: () => [...FILE_QUERY_KEYS.all, 'storage', 'stats'] as const,
  userStorage: (userId: number) => [...FILE_QUERY_KEYS.all, 'storage', 'user', userId] as const,
} as const;

/**
 * Search-related query keys
 */
export const SEARCH_QUERY_KEYS = {
  // Base keys
  all: ['search'] as const,
  
  // Global search
  global: (query: string) => [...SEARCH_QUERY_KEYS.all, 'global', query] as const,
  
  // Entity-specific search
  users: (query: string) => [...SEARCH_QUERY_KEYS.all, 'users', query] as const,
  content: (query: string) => [...SEARCH_QUERY_KEYS.all, 'content', query] as const,
  notifications: (query: string) => [...SEARCH_QUERY_KEYS.all, 'notifications', query] as const,
  
  // Suggestions and autocomplete
  suggestions: (query: string) => [...SEARCH_QUERY_KEYS.all, 'suggestions', query] as const,
  autocomplete: (query: string, entity: string) => 
    [...SEARCH_QUERY_KEYS.all, 'autocomplete', entity, query] as const,
} as const;

/**
 * All query keys combined for easy access
 */
export const ALL_QUERY_KEYS = {
  USER: USER_QUERY_KEYS,
  CREDIT: CREDIT_QUERY_KEYS,
  CONTENT: CONTENT_QUERY_KEYS,
  ANALYTICS: ANALYTICS_QUERY_KEYS,
  NOTIFICATION: NOTIFICATION_QUERY_KEYS,
  SYSTEM: SYSTEM_QUERY_KEYS,
  ADMIN: ADMIN_QUERY_KEYS,
  FILE: FILE_QUERY_KEYS,
  SEARCH: SEARCH_QUERY_KEYS,
} as const;

/**
 * Query key utilities
 */
export const QueryKeyUtils = {
  /**
   * Invalidate all queries for a specific entity
   */
  getInvalidationKey: (entity: keyof typeof ALL_QUERY_KEYS): string[] => {
    return ALL_QUERY_KEYS[entity].all;
  },

  /**
   * Get all related keys for a user
   */
  getUserRelatedKeys: (userId: number): string[][] => {
    return [
      USER_QUERY_KEYS.detail(userId),
      USER_QUERY_KEYS.activity(userId),
      USER_QUERY_KEYS.credits(userId),
      USER_QUERY_KEYS.content(userId),
      CREDIT_QUERY_KEYS.balance(userId),
      CONTENT_QUERY_KEYS.userContent(userId),
      NOTIFICATION_QUERY_KEYS.userNotifications(userId),
    ];
  },

  /**
   * Get all related keys for content
   */
  getContentRelatedKeys: (contentId: string): string[][] => {
    return [
      CONTENT_QUERY_KEYS.detail(contentId),
      CONTENT_QUERY_KEYS.thumbnail(contentId),
      CONTENT_QUERY_KEYS.preview(contentId),
    ];
  },

  /**
   * Check if a query key matches a pattern
   */
  matchesPattern: (queryKey: string[], pattern: string[]): boolean => {
    if (pattern.length > queryKey.length) return false;
    
    return pattern.every((part, index) => {
      return part === queryKey[index] || part === '*';
    });
  },
} as const;
