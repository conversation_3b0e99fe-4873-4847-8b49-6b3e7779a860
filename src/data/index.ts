/**
 * Data Layer - Central Export
 * 
 * This is the main entry point for the Data Layer in our 3-layer MVI architecture.
 * The Data Layer handles data access, caching, and business logic operations.
 * 
 * Architecture Rules:
 * - Data Layer handles data access and business logic
 * - Can import from Model Layer for types
 * - Provides data to Interface Layer
 * - Contains repositories, data sources, and constants
 * - NO UI logic (belongs in Interface Layer)
 */

// ============================================================================
// CONSTANTS EXPORTS
// ============================================================================

// Re-export all constants from the constants module
export {
  // API Endpoints
  API_BASE,
  AUTH_ENDPOINTS,
  USER_ENDPOINTS,
  CREDIT_ENDPOINTS,
  CONTENT_ENDPOINTS,
  ANALYTICS_ENDPOINTS,
  NOTIFICATION_ENDPOINTS,
  SYSTEM_ENDPOINTS,
  ADMIN_ENDPOINTS,
  FILE_ENDPOINTS,
  SEARCH_ENDPOINTS,
  EXPORT_ENDPOINTS,
  IMPORT_ENDPOINTS,
  WEBSOCKET_ENDPOINTS,
  EXTERNAL_ENDPOINTS,
  ALL_ENDPOINTS,
  buildApiUrl,
  getEndpointUrl,
  isValidEndpoint,
  getEndpoint,

  // Storage and API Keys
  LOCAL_STORAGE_KEYS,
  SESSION_STORAGE_KEYS,
  COOKIE_KEYS,
  INDEXED_DB_CONFIG,
  API_HEADER_KEYS,
  WEBSOCKET_EVENT_KEYS,
  CACHE_KEYS,
  EVENT_KEYS,
  ERROR_KEYS,
  METRIC_KEYS,
  CONFIG_KEYS,
  KeyUtils,

  // React Query Keys
  createQueryKey,
  USER_QUERY_KEYS,
  CREDIT_QUERY_KEYS,
  CONTENT_QUERY_KEYS,
  ANALYTICS_QUERY_KEYS,
  NOTIFICATION_QUERY_KEYS,
  SYSTEM_QUERY_KEYS,
  ADMIN_QUERY_KEYS,
  FILE_QUERY_KEYS,
  SEARCH_QUERY_KEYS,
  ALL_QUERY_KEYS,
  QueryKeyUtils,

  // Data Layer Utilities
  DataLayerUtils,
  DATA_LAYER_CONFIG,
  ALL_DATA_CONSTANTS,
  DATA_LAYER_INFO,
  validateDataLayer,
} from './constants';

// ============================================================================
// DATA SOURCES EXPORTS
// ============================================================================

// Re-export all data sources
export {
  // Mock Data
  MOCK_USERS,
  MOCK_DASHBOARD_STATS,
  MOCK_QUICK_STATS,
  MOCK_CREDIT_TRANSACTIONS,
  MOCK_CONTENT,
  MOCK_ANALYTICS_DATA,
  MOCK_USER_GROWTH_DATA,
  MOCK_CREDIT_USAGE_DATA,
  MOCK_CONTENT_CREATION_DATA,
  MOCK_NOTIFICATIONS,
  MOCK_SYSTEM_STATUS,
  MockDataUtils,
  ALL_MOCK_DATA,

  // Storage Sources
  LocalStorageSource,
  SessionStorageSource,
  CookieSource,
  AuthStorage,
  PreferencesStorage,
  DashboardStorage,
  CacheStorage,
  StorageUtils,

  // Cache Sources
  MemoryCache,
  PersistentCache,
  CacheManager,
  globalCache,
  CacheUtils,

  // Data Source Utilities
  DataSourceUtils,
  DATA_SOURCE_CONFIG,
  ALL_DATA_SOURCES,
  DATA_SOURCE_INFO,
} from './sources';

// ============================================================================
// REPOSITORIES EXPORTS
// ============================================================================

// Re-export all repositories
export {
  // Base Repository
  BaseRepository,
  RepositoryError,

  // Repository Classes
  UserRepository,
  CreditRepository,
  AnalyticsRepository,
  ContentRepository,
  NotificationRepository,
  SystemRepository,

  // Repository Instances
  userRepository,
  creditRepository,
  analyticsRepository,
  contentRepository,
  notificationRepository,
  systemRepository,
  repositories,

  // Repository Utilities
  RepositoryUtils,
  DEFAULT_REPOSITORY_CONFIG,
  REPOSITORY_CONFIGS,
  getRepositoryConfig,
  ALL_REPOSITORIES,
  REPOSITORY_LAYER_INFO,
} from './repositories';

// Re-export repository types
export type {
  RepositoryConfig,
  RepositoryOptions,
  RepositoryErrorType,
} from './repositories';

// ============================================================================
// DATA LAYER UTILITIES
// ============================================================================

/**
 * Data Layer utility functions
 */
export const DataLayer = {
  /**
   * Initialize the entire Data Layer
   */
  initialize: async (): Promise<{
    success: boolean;
    sources: any;
    repositories: any;
    errors: string[];
  }> => {
    const errors: string[] = [];

    try {
      // Initialize data sources
      const sources = DataSourceUtils.initializeDataSources();
      
      // Initialize repositories
      const repositories = RepositoryUtils.initializeRepositories();
      
      // Collect any errors
      if (repositories.errors.length > 0) {
        errors.push(...repositories.errors);
      }

      return {
        success: errors.length === 0,
        sources,
        repositories,
        errors,
      };
    } catch (error) {
      errors.push(`Data Layer initialization failed: ${error}`);
      return {
        success: false,
        sources: null,
        repositories: null,
        errors,
      };
    }
  },

  /**
   * Get Data Layer health status
   */
  getHealth: async (): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    sources: any;
    repositories: any;
    cache: any;
  }> => {
    try {
      // Get data sources health
      const sources = DataSourceUtils.getHealthStatus();
      
      // Get repositories health
      const repositories = await RepositoryUtils.getRepositoryHealth();
      
      // Get cache health
      const cacheStats = globalCache.getStats();
      const cache = {
        status: 'healthy' as const,
        stats: cacheStats,
      };

      // Determine overall health
      let overall: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      
      if (sources.overall === 'error' || repositories.overall === 'unhealthy') {
        overall = 'unhealthy';
      } else if (sources.overall === 'degraded' || repositories.overall === 'degraded') {
        overall = 'degraded';
      }

      return {
        overall,
        sources,
        repositories,
        cache,
      };
    } catch (error) {
      console.error('Data Layer health check failed:', error);
      return {
        overall: 'unhealthy',
        sources: { overall: 'error' },
        repositories: { overall: 'unhealthy' },
        cache: { status: 'error' },
      };
    }
  },

  /**
   * Clear all Data Layer caches
   */
  clearAllCaches: async (): Promise<{
    success: boolean;
    cleared: {
      globalCache: boolean;
      repositories: any;
      storage: boolean;
    };
    errors: string[];
  }> => {
    const errors: string[] = [];
    const cleared = {
      globalCache: false,
      repositories: null as any,
      storage: false,
    };

    try {
      // Clear global cache
      cleared.globalCache = globalCache.clear();
      
      // Clear repository caches
      cleared.repositories = await RepositoryUtils.clearAllCaches();
      
      // Clear storage caches
      cleared.storage = CacheStorage.clearCache();

      return {
        success: errors.length === 0,
        cleared,
        errors,
      };
    } catch (error) {
      errors.push(`Cache clearing failed: ${error}`);
      return {
        success: false,
        cleared,
        errors,
      };
    }
  },

  /**
   * Get Data Layer statistics
   */
  getStatistics: (): {
    sources: any;
    repositories: any;
    cache: any;
    configuration: any;
  } => {
    try {
      return {
        sources: {
          mockDataAvailable: true,
          storageAvailable: DataSourceUtils.isBrowser(),
          configuration: DataSourceUtils.exportConfiguration(),
        },
        repositories: RepositoryUtils.getRepositoryStats(),
        cache: globalCache.getStats(),
        configuration: {
          environment: process.env.NODE_ENV,
          dataLayerConfig: DATA_LAYER_CONFIG,
          repositoryConfig: getRepositoryConfig(),
        },
      };
    } catch (error) {
      console.error('Failed to get Data Layer statistics:', error);
      return {
        sources: { error: String(error) },
        repositories: { error: String(error) },
        cache: { error: String(error) },
        configuration: { error: String(error) },
      };
    }
  },

  /**
   * Validate Data Layer setup
   */
  validateSetup: (): {
    valid: boolean;
    issues: string[];
    recommendations: string[];
    details: {
      dataLayer: any;
      repositories: any;
    };
  } => {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Validate Data Layer
      const dataLayerValidation = validateDataLayer();
      if (!dataLayerValidation.valid) {
        issues.push('Data Layer validation failed');
      }

      // Validate repositories
      const repositoryValidation = RepositoryUtils.validateRepositorySetup();
      if (!repositoryValidation.valid) {
        issues.push(...repositoryValidation.issues);
      }
      recommendations.push(...repositoryValidation.recommendations);

      // Check environment configuration
      if (process.env.NODE_ENV === 'production') {
        const config = getRepositoryConfig();
        if (config.useMockData) {
          issues.push('Mock data is enabled in production environment');
        }
      }

      return {
        valid: issues.length === 0,
        issues,
        recommendations,
        details: {
          dataLayer: dataLayerValidation,
          repositories: repositoryValidation,
        },
      };
    } catch (error) {
      issues.push(`Validation failed: ${error}`);
      return {
        valid: false,
        issues,
        recommendations,
        details: {
          dataLayer: { valid: false, error: String(error) },
          repositories: { valid: false, error: String(error) },
        },
      };
    }
  },

  /**
   * Reset Data Layer to initial state
   */
  reset: async (): Promise<{
    success: boolean;
    actions: string[];
    errors: string[];
  }> => {
    const actions: string[] = [];
    const errors: string[] = [];

    try {
      // Clear all caches
      const cacheResult = await DataLayer.clearAllCaches();
      if (cacheResult.success) {
        actions.push('Cleared all caches');
      } else {
        errors.push(...cacheResult.errors);
      }

      // Reset data sources
      const sourcesReset = DataSourceUtils.resetDataSources();
      if (sourcesReset) {
        actions.push('Reset data sources');
      } else {
        errors.push('Failed to reset data sources');
      }

      return {
        success: errors.length === 0,
        actions,
        errors,
      };
    } catch (error) {
      errors.push(`Data Layer reset failed: ${error}`);
      return {
        success: false,
        actions,
        errors,
      };
    }
  },
};

// ============================================================================
// DATA LAYER CONFIGURATION
// ============================================================================

/**
 * Data Layer configuration
 */
export const DATA_LAYER_CONFIGURATION = {
  version: '1.0.0',
  architecture: '3-layer MVI',
  environment: process.env.NODE_ENV || 'development',
  features: {
    mockData: DataSourceUtils.shouldUseMockData(),
    caching: true,
    storage: DataSourceUtils.isBrowser(),
    repositories: true,
  },
  dependencies: ['Model Layer'],
  provides: ['Data access', 'Business logic', 'Caching', 'Storage'],
} as const;

// ============================================================================
// DATA LAYER INITIALIZATION
// ============================================================================

/**
 * Initialize Data Layer on module load
 */
const initializeDataLayer = async (): Promise<void> => {
  try {
    const result = await DataLayer.initialize();
    
    if (process.env.NODE_ENV === 'development') {
      console.log('Data Layer initialized:', result);
      
      // Validate setup in development
      const validation = DataLayer.validateSetup();
      if (!validation.valid) {
        console.warn('Data Layer setup issues:', validation.issues);
      }
      if (validation.recommendations.length > 0) {
        console.info('Data Layer recommendations:', validation.recommendations);
      }
    }
  } catch (error) {
    console.error('Data Layer initialization failed:', error);
  }
};

// Initialize on module load
if (typeof window !== 'undefined') {
  initializeDataLayer();
}

// ============================================================================
// EXPORT DATA LAYER
// ============================================================================

/**
 * Complete Data Layer export
 */
export const DATA_LAYER = {
  // Core functionality
  UTILS: DataLayer,
  CONFIG: DATA_LAYER_CONFIGURATION,
  
  // Components
  CONSTANTS: ALL_DATA_CONSTANTS,
  SOURCES: ALL_DATA_SOURCES,
  REPOSITORIES: ALL_REPOSITORIES,
  
  // Metadata
  INFO: {
    DATA_LAYER: DATA_LAYER_INFO,
    SOURCES: DATA_SOURCE_INFO,
    REPOSITORIES: REPOSITORY_LAYER_INFO,
  },
} as const;
